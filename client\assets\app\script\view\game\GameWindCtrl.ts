import { _decorator, log, Node, Prefab, EventTouch } from "cc";
import AnimalObj from "../../model/game/AnimalObj";
import AnimalCmpt from "./AnimalCmpt";
import GameModel from "../../model/game/GameModel";
import EventType from "../../common/event/EventType";
import AnimPlayCmpt from "./AnimPlayCmpt";
import RoleCmpt from "./RoleCmpt";
import RoleObj from "../../model/game/RoleObj";
import { viewHelper } from "../../common/helper/ViewHelper";
const { ccclass } = _decorator;

@ccclass
export default class GameWindCtrl extends mc.BaseWindCtrl {

    //@autocode property begin
    private mapNode_: Node = null // path://map_n
    private itemsNode_: Node = null // path://items_n
    private flutterNode_: Node = null // path://flutter_n
    //@end

    private animPlay: AnimPlayCmpt = null

    private animals: AnimalCmpt[] = []
    private animalMap: { [key: string]: AnimalCmpt } = {}

    private myRole: RoleCmpt = null

    private model: GameModel = null

    public listenEventMaps() {
        return [
            { [EventType.UPDATE_MY_BATTLE_AREA]: this.onUpdateMyBattleArea, enter: true },
            { [EventType.PLAY_FLUTTER_HP]: this.onPlayFlutterHp, enter: true },
            { [EventType.REMOVE_ANIMAL]: this.onRemoveAnimal, enter: true },
        ]
    }

    public async onCreate() {
        this.animPlay = this.node.addComponent(AnimPlayCmpt)
        this.model = this.getModel('game')
        this.myRole = await this.createRole(new RoleObj().init(110001), 0)
        await this.animPlay.init(this.key)
    }

    public onEnter(data: any) {
        this.model.enter()
        this.myRole.playAnimation('idle')
        this.updateEncounterInfo()
    }

    public onClean() {
        this.model.leave()
        this.animPlay.clean()
        this.animPlay = null
        assetsMgr.releaseTempResByTag(this.key)
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://map_n/goon_be
    onClickGoon(event: EventTouch, data: string) {
        this.myRole.playAnimation('attack', () => this.myRole.playAnimation('idle'))
        // viewHelper.showPnl('game/Map')
        viewHelper.showPnl('game/Test')
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private onUpdateMyBattleArea() {
        this.updateMyBattleArea()
    }

    // 播放飘字
    private onPlayFlutterHp(data: any) {
        const node = this.animals.find(m => m.uid === data.uid)?.node
        if (node) {
            this.animPlay.playFlutter(data, this.flutterNode_, ut.convertToNodeAR(node, this.flutterNode_))
        }
    }

    private onRemoveAnimal(uid: string, release: boolean) {
        this.cleanAnimal(this.animals.remove('uid', uid), release)
    }
    // ----------------------------------------- custom function ----------------------------------------------------

    update(dt: number) {
        this.model?.update(dt)
    }

    // 创建一个角色
    private async createRole(data: RoleObj, index: number) {
        if (!this.isValid || !data) {
            return null
        }
        const pfb = await assetsMgr.loadTempRes(data.getPrefabUrl(), Prefab, this.key)
        if (!pfb || !this.isValid) {
            return null
        }
        const pos = this.mapNode_.Child('role_pos/' + index).getPosition()
        const role = await mc.instantiate(pfb, this.itemsNode_).getComponent(RoleCmpt).init(data, pos, this.key)
        return role
    }

    // 创建一个动物
    private async createAnimal(data: AnimalObj, rootType: string) {
        if (!this.isValid || !data) {
            return null
        }
        let animal = this.animals.find(m => m.uid === data.uid)
        if (!animal) {
        } else if (data.isDie()) {
            this.animals.remove('uid', data.uid)
            this.cleanAnimal(animal)
            return null
        } else {
            return animal.resync(data)
        }
        const pfb = await assetsMgr.loadTempRes(data.getPrefabUrl(), Prefab, this.key)
        if (!pfb || !this.isValid) {
            return null
        } else if (this.animalMap[data.uid]) {
            return null //防止多次创建
        }
        // 重新获取以防数据不统一
        const uid = data.uid
        // data = this.model.getAnimal(uid)
        if (!data || data.isDie()) {
            return null
        }
        animal = mc.instantiate(pfb, this.itemsNode_).getComponent(AnimalCmpt).init(data, this.key)
        this.animals.push(animal)
        this.animalMap[data.uid] = animal
        return animal
    }

    private cleanAnimal(animal: AnimalCmpt, release?: boolean) {
        if (animal) {
            delete this.animalMap[animal.uid]
            animal.clean(release)
        }
    }

    // 刷新我的战斗区域
    private updateMyBattleArea() {
        this.model.getBattleAnimals().forEach(m => this.createAnimal(m, 'my'))
    }

    // 运行战斗
    private async runBattle() {
        await Promise.all(this.model.getEnemyAnimals().map(m => this.createAnimal(m, 'enemy')))
        this.model.battleBegin()
    }

    // 刷新遭遇信息
    private updateEncounterInfo() {
        // const data = this.model.getCurrContent()
    }
}
