{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/MapPnlCtrl.ts"], "names": ["_decorator", "log", "Label", "v2", "ccclass", "MapPnlCtrl", "mc", "BasePnlCtrl", "layersSv_", "game", "listenEventMaps", "onCreate", "set<PERSON>ara<PERSON>", "isMask", "isAct", "getModel", "onEnter", "maps", "getMaps", "lastLayerNode", "tempVec", "Items", "it", "layer", "Data", "node", "data", "Child", "string", "type", "lastLayerNodes", "for<PERSON>ach", "m", "i", "preNode", "children", "prePos", "position", "toVec2", "lineNode", "line", "id", "curNode", "curPos", "ut", "convertToNodeAR", "Height", "subtract", "length", "angle", "getAngle", "onRemove", "onClean", "onClickItem", "event"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,G,OAAAA,G;AAAiBC,MAAAA,K,OAAAA,K;AAAyBC,MAAAA,E,OAAAA,E;;;;;;;;;OAGzD;AAAEC,QAAAA;AAAF,O,GAAcJ,U;;yBAGCK,U,GADpBD,O,UAAD,MACqBC,UADrB,SACwCC,EAAE,CAACC,WAD3C,CACuD;AAAA;AAAA;AAEnD;AAFmD,eAG3CC,SAH2C,GAGnB,IAHmB;AAGd;AACrC;AAJmD,eAM3CC,IAN2C,GAMzB,IANyB;AAAA;;AAQ5CC,QAAAA,eAAe,GAAG;AACrB,iBAAO,EAAP;AACH;;AAEYC,QAAAA,QAAQ,GAAG;AAAA;;AAAA;AACpB,YAAA,KAAI,CAACC,QAAL,CAAc;AAAEC,cAAAA,MAAM,EAAE,KAAV;AAAiBC,cAAAA,KAAK,EAAE;AAAxB,aAAd;;AACA,YAAA,KAAI,CAACL,IAAL,GAAY,KAAI,CAACM,QAAL,CAAc,MAAd,CAAZ;AAFoB;AAGvB;;AAEMC,QAAAA,OAAO,GAAG;AACb,cAAMC,IAAI,GAAG,KAAKR,IAAL,CAAUS,OAAV,EAAb;AACA,cAAIC,aAAmB,GAAG,IAA1B;AAAA,cAAgCC,OAAO,GAAGjB,EAAE,EAA5C;AACA,eAAKK,SAAL,CAAea,KAAf,CAAqBJ,IAArB,EAA2B,CAACK,EAAD,EAAKC,KAAL,KAAe;AACtCD,YAAAA,EAAE,CAACE,IAAH,GAAUD,KAAV;AACAD,YAAAA,EAAE,CAACD,KAAH,CAASE,KAAT,EAAgB,CAACE,IAAD,EAAOC,IAAP,KAAgB;AAC5BD,cAAAA,IAAI,CAACE,KAAL,CAAW,KAAX,EAAkBzB,KAAlB,EAAyB0B,MAAzB,GAAkCF,IAAI,CAACG,IAAL,GAAY,EAA9C;AACH,aAFD,EAFsC,CAKtC;;AACA,gBAAIV,aAAJ,EAAmB;AACf,kBAAMW,cAA4B,GAAGX,aAAa,CAACK,IAAnD;AACAM,cAAAA,cAAc,CAACC,OAAf,CAAuB,CAACC,CAAD,EAAIC,CAAJ,KAAU;AAC7B,oBAAMC,OAAO,GAAGf,aAAa,CAACgB,QAAd,CAAuBF,CAAvB,CAAhB;AAAA,oBAA2CG,MAAM,GAAGF,OAAO,CAACG,QAAR,CAAiBC,MAAjB,EAApD;AACA,oBAAMC,QAAQ,GAAGL,OAAO,CAACP,KAAR,CAAc,OAAd,CAAjB;AACAY,gBAAAA,QAAQ,CAAClB,KAAT,CAAeW,CAAC,CAACG,QAAjB,EAA2B,CAACK,IAAD,EAAOC,EAAP,KAAc;AACrC,sBAAMC,OAAO,GAAGpB,EAAE,CAACa,QAAH,CAAYM,EAAZ,CAAhB;AAAA,sBAAiCE,MAAM,GAAGC,EAAE,CAACC,eAAH,CAAmBH,OAAnB,EAA4BH,QAA5B,EAAsCnB,OAAtC,CAA1C;AACAoB,kBAAAA,IAAI,CAACM,MAAL,GAAcH,MAAM,CAACI,QAAP,CAAgBX,MAAhB,EAAwBY,MAAxB,EAAd;AACAR,kBAAAA,IAAI,CAACS,KAAL,GAAaL,EAAE,CAACM,QAAH,CAAYP,MAAZ,EAAoBP,MAApB,CAAb;AACH,iBAJD;AAKH,eARD;AASH;;AACDjB,YAAAA,aAAa,GAAGG,EAAhB;AACH,WAnBD;AAoBH;;AAEM6B,QAAAA,QAAQ,GAAG,CACjB;;AAEMC,QAAAA,OAAO,GAAG,CAChB,CA9CkD,CAgDnD;AACA;AAEA;;;AACAC,QAAAA,WAAW,CAACC,KAAD,EAAoB5B,IAApB,EAAkC;AACzCzB,UAAAA,GAAG,CAAC,aAAD,EAAgByB,IAAhB,CAAH;AACH,SAtDkD,CAuDnD;AACA;AAEA;;;AA1DmD,O", "sourcesContent": ["import { _decorator, log, ScrollView, Label, Node, EventTouch, v2 } from \"cc\";\nimport GameModel from \"../../model/game/GameModel\";\nimport MapNodeObj from \"../../model/game/MapNodeObj\";\nconst { ccclass } = _decorator;\n\n@ccclass\nexport default class MapPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private layersSv_: ScrollView = null // path://layers_sv\n    //@end\n\n    private game: GameModel = null\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n        this.setParam({ isMask: false, isAct: false })\n        this.game = this.getModel('game')\n    }\n\n    public onEnter() {\n        const maps = this.game.getMaps()\n        let lastLayerNode: Node = null, tempVec = v2()\n        this.layersSv_.Items(maps, (it, layer) => {\n            it.Data = layer\n            it.Items(layer, (node, data) => {\n                node.Child('val', Label).string = data.type + ''\n            })\n            // 绘制上一个的线\n            if (lastLayerNode) {\n                const lastLayerNodes: MapNodeObj[] = lastLayerNode.Data\n                lastLayerNodes.forEach((m, i) => {\n                    const preNode = lastLayerNode.children[i], prePos = preNode.position.toVec2()\n                    const lineNode = preNode.Child('lines')\n                    lineNode.Items(m.children, (line, id) => {\n                        const curNode = it.children[id], curPos = ut.convertToNodeAR(curNode, lineNode, tempVec)\n                        line.Height = curPos.subtract(prePos).length()\n                        line.angle = ut.getAngle(curPos, prePos)\n                    })\n                })\n            }\n            lastLayerNode = it\n        })\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://layers_sv/view/content/layer/item_be\n    onClickItem(event: EventTouch, data: string) {\n        log('onClickItem', data)\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n}\n"]}