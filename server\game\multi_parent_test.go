package game

import (
	"fmt"
	"testing"
)

// 测试多父节点功能
func TestMultiParentConnections(t *testing.T) {
	fmt.Println("=== 多父节点连接测试 ===")
	
	// 测试多个场景
	for testCase := 0; testCase < 5; testCase++ {
		fmt.Printf("\n--- 测试案例 %d ---\n", testCase+1)
		
		// 生成地图
		mapData := GenerateMapData(4)
		
		// 检查每一层的连接情况
		for layerIndex := 1; layerIndex < len(mapData); layerIndex++ {
			currentLayer := mapData[layerIndex]
			prevLayer := mapData[layerIndex-1]
			
			fmt.Printf("第%d层到第%d层:\n", layerIndex, layerIndex+1)
			
			// 统计每个子节点的父节点数量
			childParentCount := make([]int, len(currentLayer))
			
			// 打印父节点连接
			for parentIndex, parent := range prevLayer {
				fmt.Printf("  父节点%d -> %v\n", parentIndex, parent.Children)
				for _, childIndex := range parent.Children {
					if int(childIndex) < len(childParentCount) {
						childParentCount[childIndex]++
					}
				}
			}
			
			// 统计多父节点的子节点
			multiParentChildren := 0
			orphanChildren := 0
			
			for childIndex, parentCount := range childParentCount {
				if parentCount == 0 {
					orphanChildren++
					fmt.Printf("  ✗ 子节点%d没有父节点\n", childIndex)
				} else if parentCount > 1 {
					multiParentChildren++
					fmt.Printf("  ✓ 子节点%d有%d个父节点\n", childIndex, parentCount)
				} else {
					fmt.Printf("  ○ 子节点%d有1个父节点\n", childIndex)
				}
			}
			
			// 检查路径交叉
			crossings := detectPathCrossings(prevLayer, currentLayer)
			if len(crossings) > 0 {
				fmt.Printf("  ✗ 发现%d个路径交叉\n", len(crossings))
				t.Errorf("第%d层到第%d层有路径交叉", layerIndex, layerIndex+1)
			} else {
				fmt.Printf("  ✓ 无路径交叉\n")
			}
			
			// 验证结果
			if orphanChildren > 0 {
				t.Errorf("第%d层有%d个孤立子节点", layerIndex+1, orphanChildren)
			}
			
			fmt.Printf("  多父节点子节点数: %d/%d\n", multiParentChildren, len(currentLayer))
		}
	}
}

// 测试特定的多父节点场景
func TestSpecificMultiParentScenarios(t *testing.T) {
	fmt.Println("\n=== 特定多父节点场景测试 ===")
	
	testCases := []struct {
		name        string
		parentCount int
		childCount  int
	}{
		{"3父2子", 3, 2},
		{"2父3子", 2, 3},
		{"4父3子", 4, 3},
		{"3父4子", 3, 4},
		{"5父3子", 5, 3},
		{"3父5子", 3, 5},
	}
	
	for _, tc := range testCases {
		fmt.Printf("\n--- %s ---\n", tc.name)
		
		// 创建测试层
		parentLayer := make([]*MapNode, tc.parentCount)
		childLayer := make([]*MapNode, tc.childCount)
		
		for i := 0; i < tc.parentCount; i++ {
			parentLayer[i] = &MapNode{
				Type:     MAP_NODE_TYPE_BATTLE,
				Children: []int32{},
			}
		}
		
		for i := 0; i < tc.childCount; i++ {
			childLayer[i] = &MapNode{
				Type:     MAP_NODE_TYPE_BATTLE,
				Children: []int32{},
			}
		}
		
		// 分配子节点
		assignChildrenByStrictRegions(parentLayer, childLayer)
		
		// 统计每个子节点的父节点数量
		childParentCount := make([]int, tc.childCount)
		
		// 打印连接关系
		for i, parent := range parentLayer {
			fmt.Printf("父节点%d -> %v\n", i, parent.Children)
			for _, childIndex := range parent.Children {
				if int(childIndex) < len(childParentCount) {
					childParentCount[childIndex]++
				}
			}
		}
		
		// 分析结果
		multiParentCount := 0
		orphanCount := 0
		
		for childIndex, parentCount := range childParentCount {
			if parentCount == 0 {
				orphanCount++
				fmt.Printf("✗ 子节点%d没有父节点\n", childIndex)
			} else if parentCount > 1 {
				multiParentCount++
				fmt.Printf("✓ 子节点%d有%d个父节点\n", childIndex, parentCount)
			}
		}
		
		// 验证
		if orphanCount > 0 {
			t.Errorf("%s 有%d个孤立子节点", tc.name, orphanCount)
		}
		
		// 检查交叉
		crossings := detectPathCrossings(parentLayer, childLayer)
		if len(crossings) > 0 {
			t.Errorf("%s 存在路径交叉: %v", tc.name, crossings)
		}
		
		fmt.Printf("多父节点子节点: %d/%d\n", multiParentCount, tc.childCount)
		fmt.Printf("✓ 连通性和无交叉验证通过\n")
	}
}

// 测试多父节点的统计信息
func TestMultiParentStatistics(t *testing.T) {
	fmt.Println("\n=== 多父节点统计测试 ===")
	
	totalTests := 20
	totalMultiParentNodes := 0
	totalNodes := 0
	
	for testCase := 0; testCase < totalTests; testCase++ {
		mapData := GenerateMapData(5)
		
		for layerIndex := 1; layerIndex < len(mapData); layerIndex++ {
			currentLayer := mapData[layerIndex]
			prevLayer := mapData[layerIndex-1]
			
			// 统计每个子节点的父节点数量
			childParentCount := make([]int, len(currentLayer))
			
			for _, parent := range prevLayer {
				for _, childIndex := range parent.Children {
					if int(childIndex) < len(childParentCount) {
						childParentCount[childIndex]++
					}
				}
			}
			
			// 统计多父节点
			for _, parentCount := range childParentCount {
				totalNodes++
				if parentCount > 1 {
					totalMultiParentNodes++
				}
			}
		}
	}
	
	multiParentRatio := float64(totalMultiParentNodes) / float64(totalNodes) * 100
	fmt.Printf("总测试: %d个地图\n", totalTests)
	fmt.Printf("总节点数: %d\n", totalNodes)
	fmt.Printf("多父节点数: %d\n", totalMultiParentNodes)
	fmt.Printf("多父节点比例: %.2f%%\n", multiParentRatio)
	
	// 验证多父节点比例是否合理（应该大于0%，说明算法确实允许多父节点）
	if multiParentRatio == 0 {
		t.Error("多父节点比例为0%，算法可能没有正确实现多父节点功能")
	} else {
		fmt.Printf("✓ 算法成功实现了多父节点功能\n")
	}
}

// 测试极端情况下的多父节点
func TestExtremeMultiParentCases(t *testing.T) {
	fmt.Println("\n=== 极端情况多父节点测试 ===")
	
	extremeCases := []struct {
		name        string
		parentCount int
		childCount  int
	}{
		{"10父1子", 10, 1},
		{"1父10子", 1, 10},
		{"8父2子", 8, 2},
		{"2父8子", 2, 8},
	}
	
	for _, tc := range extremeCases {
		fmt.Printf("\n--- %s ---\n", tc.name)
		
		// 创建测试层
		parentLayer := make([]*MapNode, tc.parentCount)
		childLayer := make([]*MapNode, tc.childCount)
		
		for i := 0; i < tc.parentCount; i++ {
			parentLayer[i] = &MapNode{
				Type:     MAP_NODE_TYPE_BATTLE,
				Children: []int32{},
			}
		}
		
		for i := 0; i < tc.childCount; i++ {
			childLayer[i] = &MapNode{
				Type:     MAP_NODE_TYPE_BATTLE,
				Children: []int32{},
			}
		}
		
		// 分配子节点
		assignChildrenByStrictRegions(parentLayer, childLayer)
		
		// 统计每个子节点的父节点数量
		childParentCount := make([]int, tc.childCount)
		
		for i, parent := range parentLayer {
			fmt.Printf("父节点%d -> %v\n", i, parent.Children)
			for _, childIndex := range parent.Children {
				if int(childIndex) < len(childParentCount) {
					childParentCount[childIndex]++
				}
			}
		}
		
		// 验证每个子节点都有父节点
		for childIndex, parentCount := range childParentCount {
			if parentCount == 0 {
				t.Errorf("%s 子节点%d没有父节点", tc.name, childIndex)
			} else {
				fmt.Printf("子节点%d有%d个父节点\n", childIndex, parentCount)
			}
		}
		
		// 检查交叉
		crossings := detectPathCrossings(parentLayer, childLayer)
		if len(crossings) > 0 {
			t.Errorf("%s 存在路径交叉", tc.name)
		} else {
			fmt.Printf("✓ 无路径交叉\n")
		}
	}
}
