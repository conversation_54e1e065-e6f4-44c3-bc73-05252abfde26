{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts"], "names": ["_decorator", "CCBoolean", "CCInteger", "Component", "NodeEventType", "ScrollView", "UITransform", "v3", "ccclass", "property", "menu", "requireComponent", "disallowMultiple", "ScrollViewPlus", "type", "range", "tooltip", "slide", "scrollView", "viewNode", "contentTransform", "viewTransform", "content", "item", "frameCount", "renderStartIndex", "needRenderCount", "currRenderCount", "setItemCallback", "callback<PERSON><PERSON><PERSON>", "datas", "isCanScrollingUpdate", "canScrollingUpdate", "_temp_vec2_1", "_temp_vec2_2", "onLoad", "init", "getComponent", "parent", "children", "active", "on", "CHILD_REMOVED", "onScrolling", "CHILDREN_ORDER_CHANGED", "node", "ChildrenCount", "contentX", "width", "anchorX", "contentY", "height", "anchorY", "pos", "getPosition", "x", "y", "for<PERSON>ach", "p", "transform", "opacity", "update", "dt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updateRate", "updateItems", "cnt", "updateRenderCount", "cur", "i", "setItemData", "mc", "instantiate", "l", "Data", "it", "call", "reset", "items", "list", "prefab", "cb", "target", "length", "m", "updateNodeShow"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,a,OAAAA,a;AAAeC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;;;;;;;;;OAE9F;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,IAArB;AAA2BC,QAAAA,gBAA3B;AAA6CC,QAAAA;AAA7C,O,GAAkEZ,U;;yBAMnDa,c,WAHpBD,gBAAgB,E,UAChBD,gBAAgB,CAACN,UAAD,C,UAChBK,IAAI,CAAC,sBAAD,C,UAGAD,QAAQ,CAACR,SAAD,C,UAERQ,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEZ,SAAR;AAAmBa,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,EAAJ,EAAQ,CAAR,CAA1B;AAAsCC,QAAAA,OAAO,EAAE,SAA/C;AAA0DC,QAAAA,KAAK,EAAE;AAAjE,OAAD,C,UAERR,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEZ,SAAR;AAAmBa,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,EAAJ,EAAQ,CAAR,CAA1B;AAAsCC,QAAAA,OAAO,EAAE,eAA/C;AAAgEC,QAAAA,KAAK,EAAE;AAAvE,OAAD,C,EAVZT,O,iEAAD,MAIqBK,cAJrB,SAI4CV,SAJ5C,CAIsD;AAAA;AAAA;;AAAA;;AAGb;AAHa;;AAKnB;AALmB;;AAOZ;AAPY,eAS1Ce,UAT0C,GASjB,IATiB;AAAA,eAU1CC,QAV0C,GAUzB,IAVyB;AAAA,eAW1CC,gBAX0C,GAWV,IAXU;AAAA,eAY1CC,aAZ0C,GAYb,IAZa;AAAA,eAa1CC,OAb0C,GAa1B,IAb0B;AAAA,eAc1CC,IAd0C,GAc7B,IAd6B;AAAA,eAgB1CC,UAhB0C,GAgBrB,CAhBqB;AAgBnB;AAhBmB,eAiB1CC,gBAjB0C,GAiBf,CAjBe;AAiBb;AAjBa,eAkB1CC,eAlB0C,GAkBhB,CAlBgB;AAkBd;AAlBc,eAmB1CC,eAnB0C,GAmBhB,CAnBgB;AAmBd;AAnBc,eAqB1CC,eArB0C,GAqBd,IArBc;AAAA,eAsB1CC,cAtB0C,GAsBpB,IAtBoB;AAAA,eAuB1CC,KAvB0C,GAuB3B,IAvB2B;AAAA,eAwB1CC,oBAxB0C,GAwBV,KAxBU;AAwBJ;AAxBI,eAyB1CC,kBAzB0C,GAyBZ,KAzBY;AAAA,eA2B1CC,YA3B0C,GA2BrB1B,EAAE,EA3BmB;AAAA,eA4B1C2B,YA5B0C,GA4BrB3B,EAAE,EA5BmB;AAAA;;AA8BlD4B,QAAAA,MAAM,GAAG;AACL,eAAKC,IAAL;AACH;;AAEOA,QAAAA,IAAI,GAAG;AACX,cAAI,KAAKlB,UAAT,EAAqB;AACjB;AACH;;AACD,eAAKA,UAAL,GAAkB,KAAKmB,YAAL,CAAkBhC,UAAlB,CAAlB;AACA,eAAKiB,OAAL,GAAe,KAAKJ,UAAL,CAAgBI,OAA/B;AACA,eAAKH,QAAL,GAAgB,KAAKG,OAAL,CAAagB,MAA7B;AACA,eAAKlB,gBAAL,GAAwB,KAAKE,OAAL,CAAae,YAAb,CAA0B/B,WAA1B,CAAxB;AACA,eAAKe,aAAL,GAAqB,KAAKF,QAAL,CAAckB,YAAd,CAA2B/B,WAA3B,CAArB;AACA,eAAKiB,IAAL,GAAY,KAAKD,OAAL,CAAaiB,QAAb,CAAsB,CAAtB,CAAZ;;AACA,cAAI,KAAKhB,IAAT,EAAe;AACX,iBAAKA,IAAL,CAAUiB,MAAV,GAAmB,KAAnB;AACH;;AACD,eAAKlB,OAAL,CAAamB,EAAb,CAAgBrC,aAAa,CAACsC,aAA9B,EAA6C,KAAKC,WAAlD,EAA+D,IAA/D;AACA,eAAKrB,OAAL,CAAamB,EAAb,CAAgBrC,aAAa,CAACwC,sBAA9B,EAAsD,KAAKD,WAA3D,EAAwE,IAAxE;AACA,eAAKE,IAAL,CAAUJ,EAAV,CAAa,WAAb,EAA0B,KAAKE,WAA/B,EAA4C,IAA5C;AACH,SAlDiD,CAoDlD;;;AACQA,QAAAA,WAAW,GAAG;AAClB,cAAI,CAAC,KAAKzB,UAAN,IAAoB,KAAKI,OAAL,CAAawB,aAAb,KAA+B,CAAnD,IAAwD,CAAC,KAAKf,oBAAlE,EAAwF;AACpF;AACH,WAHiB,CAIlB;;;AACA,gBAAMgB,QAAQ,GAAG,KAAK3B,gBAAL,CAAsB4B,KAAtB,GAA8B,KAAK5B,gBAAL,CAAsB6B,OAArE;AACA,gBAAMC,QAAQ,GAAG,KAAK9B,gBAAL,CAAsB+B,MAAtB,GAA+B,KAAK/B,gBAAL,CAAsBgC,OAAtE;AACA,gBAAMC,GAAG,GAAG,KAAK/B,OAAL,CAAagC,WAAb,CAAyB,KAAKrB,YAA9B,CAAZ;AACAoB,UAAAA,GAAG,CAACE,CAAJ,IAAS,KAAKlC,aAAL,CAAmB2B,KAAnB,GAA2B,KAAK3B,aAAL,CAAmB4B,OAA9C,GAAwDF,QAAjE;AACAM,UAAAA,GAAG,CAACG,CAAJ,IAAS,KAAKnC,aAAL,CAAmB8B,MAAnB,GAA4B,KAAK9B,aAAL,CAAmB+B,OAA/C,GAAyDF,QAAlE,CATkB,CAUlB;;AACA,eAAK5B,OAAL,CAAaiB,QAAb,CAAsBkB,OAAtB,CAA8BZ,IAAI,IAAI;AAClC,gBAAI,CAACA,IAAI,CAACL,MAAV,EAAkB;AACd;AACH;;AACD,kBAAMkB,CAAC,GAAGb,IAAI,CAACS,WAAL,CAAiB,KAAKpB,YAAtB,CAAV;AACA,kBAAMyB,SAAS,GAAGd,IAAI,CAAC1C,SAAL,CAAeG,WAAf,CAAlB;AACAoD,YAAAA,CAAC,CAACH,CAAF,GAAMF,GAAG,CAACE,CAAJ,GAAQG,CAAC,CAACH,CAAV,IAAeR,QAAQ,GAAGY,SAAS,CAACX,KAAV,GAAkBW,SAAS,CAACV,OAAtD,CAAN;AACAS,YAAAA,CAAC,CAACF,CAAF,GAAMH,GAAG,CAACG,CAAJ,GAAQE,CAAC,CAACF,CAAV,IAAeN,QAAQ,GAAGS,SAAS,CAACR,MAAV,GAAmBQ,SAAS,CAACP,OAAvD,CAAN;;AACA,gBAAIM,CAAC,CAACH,CAAF,GAAM,CAACI,SAAS,CAACX,KAAjB,IAA0BU,CAAC,CAACH,CAAF,GAAM,KAAKlC,aAAL,CAAmB2B,KAAnD,IAA4DU,CAAC,CAACF,CAAF,GAAM,CAACG,SAAS,CAACR,MAA7E,IAAuFO,CAAC,CAACF,CAAF,GAAM,KAAKnC,aAAL,CAAmB8B,MAApH,EAA4H;AAAE;AAC1H,kBAAIN,IAAI,CAACe,OAAL,KAAiB,CAArB,EAAwB;AACpBf,gBAAAA,IAAI,CAACe,OAAL,GAAe,CAAf;AACH;AACJ,aAJD,MAIO,IAAIf,IAAI,CAACe,OAAL,KAAiB,GAArB,EAA0B;AAC7Bf,cAAAA,IAAI,CAACe,OAAL,GAAe,GAAf;AACH;AACJ,WAfD,EAXkB,CA2BlB;AACH;;AAEDC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf;AACA,cAAI,CAAC,KAAKvC,IAAV,EAAgB;AACZ;AACH,WAFD,MAEO,IAAI,KAAKwC,aAAL,IAAsB,KAAKpC,eAAL,GAAuB,KAAKD,eAAtD,EAAuE;AAC1E,iBAAKF,UAAL,IAAmB,CAAnB;;AACA,gBAAI,KAAKA,UAAL,IAAmB,KAAKwC,UAA5B,EAAwC;AACpC,mBAAKxC,UAAL,GAAkB,CAAlB;AACA,mBAAKyC,WAAL;AACH;AACJ,WANM,MAMA,IAAI,KAAKjC,kBAAL,IAA2B,CAAC,KAAKD,oBAArC,EAA2D;AAC9D,iBAAKA,oBAAL,GAA4B,IAA5B;AACA,iBAAKY,WAAL;AACH;AACJ,SAjGiD,CAmGlD;;;AACQsB,QAAAA,WAAW,GAAG;AAClB,cAAIC,GAAG,GAAG,KAAKC,iBAAL,GAAyB,CAAzB,GAA6B,KAAKA,iBAAlC,GAAsD,KAAKzC,eAArE;AAAA,cAAsF0C,GAAG,GAAG,CAA5F;;AACA,cAAI,KAAKzC,eAAL,GAAuBuC,GAAvB,GAA6B,KAAKxC,eAAtC,EAAuD;AACnDwC,YAAAA,GAAG,GAAG,KAAKxC,eAAL,GAAuB,KAAKC,eAAlC;AACH;;AACD,cAAI0C,CAAC,GAAG,KAAK5C,gBAAb;;AACA,iBAAO4C,CAAC,GAAG,KAAK3C,eAAT,IAA4B0C,GAAG,GAAGF,GAAzC,EAA8CG,CAAC,EAA/C,EAAmD;AAC/C,iBAAKC,WAAL,CAAiB,KAAKhD,OAAL,CAAaiB,QAAb,CAAsB8B,CAAtB,KAA4BE,EAAE,CAACC,WAAH,CAAe,KAAKjD,IAApB,EAA0B,KAAKD,OAA/B,CAA7C,EAAsF+C,CAAtF;AACAD,YAAAA,GAAG,IAAI,CAAP;AACH;;AACD,eAAKzC,eAAL,IAAwBuC,GAAxB;AACA,eAAKzC,gBAAL,GAAwB4C,CAAxB;;AACA,cAAI,KAAK1C,eAAL,IAAwB,KAAKD,eAAjC,EAAkD;AAC9C;AACA;AACA,iBAAK,IAAI+C,CAAC,GAAG,KAAKnD,OAAL,CAAawB,aAA1B,EAAyCuB,CAAC,GAAGI,CAA7C,EAAgDJ,CAAC,EAAjD,EAAqD;AACjD,oBAAMxB,IAAI,GAAG,KAAKvB,OAAL,CAAaiB,QAAb,CAAsB8B,CAAtB,CAAb;AACAxB,cAAAA,IAAI,CAACL,MAAL,GAAc,KAAd;AACAK,cAAAA,IAAI,CAAC6B,IAAL,GAAY,IAAZ;AACH;AACJ;AACJ;;AAEOJ,QAAAA,WAAW,CAACK,EAAD,EAAWN,CAAX,EAAsB;AACrCM,UAAAA,EAAE,CAACnC,MAAH,GAAY,IAAZ;AACAmC,UAAAA,EAAE,CAACD,IAAH,GAAU,IAAV;AACAC,UAAAA,EAAE,CAACf,OAAH,GAAa,GAAb;;AACA,cAAI,CAAC,KAAKhC,eAAV,EAA2B,CAC1B,CADD,MACO,IAAI,KAAKC,cAAT,EAAyB;AAAA;;AAC5B,iBAAKD,eAAL,CAAqBgD,IAArB,CAA0B,KAAK/C,cAA/B,EAA+C8C,EAA/C,iBAAmD,KAAK7C,KAAxD,qBAAmD,YAAauC,CAAb,CAAnD,EAAoEA,CAApE;AACH,WAFM,MAEA;AAAA;;AACH,iBAAKzC,eAAL,CAAqB+C,EAArB,kBAAyB,KAAK7C,KAA9B,qBAAyB,aAAauC,CAAb,CAAzB,EAA0CA,CAA1C;AACH;;AACD,iBAAOM,EAAP;AACH;;AAEME,QAAAA,KAAK,GAAG;AACX,eAAK9C,oBAAL,GAA4B,KAA5B;AACA,eAAKC,kBAAL,GAA0B,KAA1B;AACH;;AAEM8C,QAAAA,KAAK,CAAIC,IAAJ,EAAwBC,MAAxB,EAAsCC,EAAtC,EAAmFC,MAAnF,EAAiG;AACzG,cAAI,OAAQH,IAAR,KAAkB,QAAtB,EAAgC;AAC5B,iBAAKrD,eAAL,GAAuBqD,IAAvB;AACA,iBAAKjD,KAAL,GAAa,IAAb;AACH,WAHD,MAGO;AACH,iBAAKJ,eAAL,GAAuBqD,IAAI,CAACI,MAA5B;AACA,iBAAKrD,KAAL,GAAaiD,IAAb;AACH;;AACD,cAAIC,MAAJ,EAAY;AACR,iBAAKzD,IAAL,GAAYyD,MAAZ;AACH;;AACD,eAAKpD,eAAL,GAAuBqD,EAAvB;AACA,eAAKpD,cAAL,GAAsBqD,MAAtB;AACA,eAAKvD,eAAL,GAAuB,CAAvB;AACA,eAAKF,gBAAL,GAAwB,CAAxB;AACA,eAAKO,kBAAL,GAA0B,IAA1B;;AACA,cAAI,KAAKN,eAAL,KAAyB,CAA7B,EAAgC;AAC5B,iBAAKJ,OAAL,CAAaiB,QAAb,CAAsBkB,OAAtB,CAA8B2B,CAAC,IAAI;AAC/BA,cAAAA,CAAC,CAAC5C,MAAF,GAAW,KAAX;AACA4C,cAAAA,CAAC,CAACV,IAAF,GAAS,IAAT;AACH,aAHD;AAIH;AACJ;;AAEMW,QAAAA,cAAc,GAAG;AACpB,eAAKrD,kBAAL,GAA0B,IAA1B;AACH;;AAvKiD,O;;;;;iBAGlB,I;;;;;;;iBAEH,C;;;;;;;iBAEO,C", "sourcesContent": ["import { _decorator, CCBoolean, CCInteger, Component, Node, NodeEventType, ScrollView, UITransform, v3, Vec3 } from \"cc\";\r\n\r\nconst { ccclass, property, menu, requireComponent, disallowMultiple } = _decorator;\r\n\r\n@ccclass\r\n@disallowMultiple()\r\n@requireComponent(ScrollView)\r\n@menu('自定义组件/ScrollViewPlus')\r\nexport default class ScrollViewPlus extends Component {\r\n\r\n    @property(CCBoolean)\r\n    public isFrameRender: boolean = true //是否开启分帧渲染\r\n    @property({ type: CCInteger, range: [1, 10, 1], tooltip: '多少帧渲染一次', slide: true })\r\n    private updateRate: number = 1 //渲染频率 多少帧渲染一个\r\n    @property({ type: CCInteger, range: [0, 30, 1], tooltip: '一次渲染多少个,0渲染所有', slide: true })\r\n    private updateRenderCount: number = 1 //分帧渲染个数\r\n\r\n    private scrollView: ScrollView = null\r\n    private viewNode: Node = null\r\n    private contentTransform: UITransform = null\r\n    private viewTransform: UITransform = null\r\n    private content: Node = null\r\n    private item: Node = null\r\n\r\n    private frameCount: number = 0 //多少帧\r\n    private renderStartIndex: number = 0 //渲染起点下标\r\n    private needRenderCount: number = 0 //需要渲染的item个数\r\n    private currRenderCount: number = 0 //当前渲染的item个数\r\n\r\n    private setItemCallback: Function = null\r\n    private callbackTarget: any = null\r\n    private datas: any[] = null\r\n    private isCanScrollingUpdate: boolean = false //是否可以滚动刷新了\r\n    private canScrollingUpdate: boolean = false\r\n\r\n    private _temp_vec2_1: Vec3 = v3()\r\n    private _temp_vec2_2: Vec3 = v3()\r\n\r\n    onLoad() {\r\n        this.init()\r\n    }\r\n\r\n    private init() {\r\n        if (this.scrollView) {\r\n            return\r\n        }\r\n        this.scrollView = this.getComponent(ScrollView)\r\n        this.content = this.scrollView.content\r\n        this.viewNode = this.content.parent\r\n        this.contentTransform = this.content.getComponent(UITransform)\r\n        this.viewTransform = this.viewNode.getComponent(UITransform)\r\n        this.item = this.content.children[0]\r\n        if (this.item) {\r\n            this.item.active = false\r\n        }\r\n        this.content.on(NodeEventType.CHILD_REMOVED, this.onScrolling, this)\r\n        this.content.on(NodeEventType.CHILDREN_ORDER_CHANGED, this.onScrolling, this)\r\n        this.node.on('scrolling', this.onScrolling, this)\r\n    }\r\n\r\n    // 滚动\r\n    private onScrolling() {\r\n        if (!this.scrollView || this.content.ChildrenCount === 0 || !this.isCanScrollingUpdate) {\r\n            return\r\n        }\r\n        // log('111 count=' + this.content.children.filter(m => m.active && m.opacity === 255).length + '/' + this.content.ChildrenCount)\r\n        const contentX = this.contentTransform.width * this.contentTransform.anchorX\r\n        const contentY = this.contentTransform.height * this.contentTransform.anchorY\r\n        const pos = this.content.getPosition(this._temp_vec2_1)\r\n        pos.x += this.viewTransform.width * this.viewTransform.anchorX - contentX\r\n        pos.y += this.viewTransform.height * this.viewTransform.anchorY - contentY\r\n        // 遍历 ScrollView Content 内容节点的子节点，对每个子节点的包围盒做和 ScrollView 可视区域包围盒做碰撞判断\r\n        this.content.children.forEach(node => {\r\n            if (!node.active) {\r\n                return\r\n            }\r\n            const p = node.getPosition(this._temp_vec2_2)\r\n            const transform = node.Component(UITransform)\r\n            p.x = pos.x + p.x + (contentX - transform.width * transform.anchorX)\r\n            p.y = pos.y + p.y + (contentY - transform.height * transform.anchorY)\r\n            if (p.x < -transform.width || p.x > this.viewTransform.width || p.y < -transform.height || p.y > this.viewTransform.height) { //如果没有相交就隐藏\r\n                if (node.opacity !== 0) {\r\n                    node.opacity = 0\r\n                }\r\n            } else if (node.opacity !== 255) {\r\n                node.opacity = 255\r\n            }\r\n        })\r\n        // log('222 count=' + this.content.children.filter(m => m.active && m.opacity === 255).length + '/' + this.content.ChildrenCount)\r\n    }\r\n\r\n    update(dt: number) {\r\n        // 渲染\r\n        if (!this.item) {\r\n            return\r\n        } else if (this.isFrameRender && this.currRenderCount < this.needRenderCount) {\r\n            this.frameCount += 1\r\n            if (this.frameCount >= this.updateRate) {\r\n                this.frameCount = 0\r\n                this.updateItems()\r\n            }\r\n        } else if (this.canScrollingUpdate && !this.isCanScrollingUpdate) {\r\n            this.isCanScrollingUpdate = true\r\n            this.onScrolling()\r\n        }\r\n    }\r\n\r\n    // 开始创建item\r\n    private updateItems() {\r\n        let cnt = this.updateRenderCount > 0 ? this.updateRenderCount : this.needRenderCount, cur = 0\r\n        if (this.currRenderCount + cnt > this.needRenderCount) {\r\n            cnt = this.needRenderCount - this.currRenderCount\r\n        }\r\n        let i = this.renderStartIndex\r\n        for (; i < this.needRenderCount && cur < cnt; i++) {\r\n            this.setItemData(this.content.children[i] || mc.instantiate(this.item, this.content), i)\r\n            cur += 1\r\n        }\r\n        this.currRenderCount += cnt\r\n        this.renderStartIndex = i\r\n        if (this.currRenderCount >= this.needRenderCount) {\r\n            // log('updateItems done...', i, this.content.ChildrenCount)\r\n            // 将剩余的隐藏\r\n            for (let l = this.content.ChildrenCount; i < l; i++) {\r\n                const node = this.content.children[i]\r\n                node.active = false\r\n                node.Data = null\r\n            }\r\n        }\r\n    }\r\n\r\n    private setItemData(it: Node, i: number) {\r\n        it.active = true\r\n        it.Data = null\r\n        it.opacity = 255\r\n        if (!this.setItemCallback) {\r\n        } else if (this.callbackTarget) {\r\n            this.setItemCallback.call(this.callbackTarget, it, this.datas?.[i], i)\r\n        } else {\r\n            this.setItemCallback(it, this.datas?.[i], i)\r\n        }\r\n        return it\r\n    }\r\n\r\n    public reset() {\r\n        this.isCanScrollingUpdate = false\r\n        this.canScrollingUpdate = false\r\n    }\r\n\r\n    public items<T>(list: T[] | number, prefab?: any, cb?: (it: Node, data: T, i: number) => void, target?: any) {\r\n        if (typeof (list) === 'number') {\r\n            this.needRenderCount = list\r\n            this.datas = null\r\n        } else {\r\n            this.needRenderCount = list.length\r\n            this.datas = list\r\n        }\r\n        if (prefab) {\r\n            this.item = prefab\r\n        }\r\n        this.setItemCallback = cb\r\n        this.callbackTarget = target\r\n        this.currRenderCount = 0\r\n        this.renderStartIndex = 0\r\n        this.canScrollingUpdate = true\r\n        if (this.needRenderCount === 0) {\r\n            this.content.children.forEach(m => {\r\n                m.active = false\r\n                m.Data = null\r\n            })\r\n        }\r\n    }\r\n\r\n    public updateNodeShow() {\r\n        this.canScrollingUpdate = true\r\n    }\r\n}\r\n"]}