{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts"], "names": ["setItemData", "it", "data", "i", "cb", "target", "call", "Node", "js", "Prefab", "error", "Color", "Label", "RichText", "UIOpacity", "UITransform", "Tween", "BaseLocale", "prototype", "Data", "hasOwnProperty", "Object", "defineProperty", "get", "children", "length", "cmpt", "getComponent", "addComponent", "opacity", "set", "val", "getSiblingIndex", "setSiblingIndex", "Component", "height", "transform", "width", "stopAllActions", "stopAllByTarget", "<PERSON><PERSON><PERSON><PERSON>", "name", "className", "String", "arr", "split", "l", "<PERSON><PERSON><PERSON><PERSON>", "getChildByName", "Child", "cls", "getClassName", "replace", "field", "undefined", "Items", "list", "prefab", "childs", "item", "count", "logger", "active", "mc", "instantiate", "AddItem", "findIndex", "m", "index", "Swih", "reverse", "ignores", "includes", "push", "adaptScale", "targetSize", "selfSize", "maxScale", "getContentSize", "scale", "Math", "min", "adaptSize", "r", "h", "floor", "getActive", "setActive", "color", "WHITE", "fromHEX", "SetSwallowTouches", "_touchListener", "setSwallowTouches", "IsSwallowTouches", "isSwallowTouches", "setLocaleKey", "key", "params", "locale", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;AA4MA,WAASA,WAAT,CAAqBC,EAArB,EAA+BC,IAA/B,EAA0CC,CAA1C,EAAqDC,EAArD,EAAmEC,MAAnE,EAAgF;AAC5E,QAAI,CAACD,EAAL,EAAS;AACL;AACH,KAFD,MAEO,IAAIC,MAAJ,EAAY;AACfD,MAAAA,EAAE,CAACE,IAAH,CAAQD,MAAR,EAAgBJ,EAAhB,EAAoBC,IAApB,EAA0BC,CAA1B;AACH,KAFM,MAEA;AACHC,MAAAA,EAAE,CAACH,EAAD,EAAKC,IAAL,EAAWC,CAAX,CAAF;AACH;AACJ,G,CAED;;;;;;;;;;;;;;AAhNSI,MAAAA,I,OAAAA,I;AAAMC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,M,OAAAA,M;AAA0BC,MAAAA,K,OAAAA,K;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;;AADnGC,MAAAA,U;;;;;;AAJP;AACA;AACA;;;;;AAKAV,MAAAA,IAAI,CAACW,SAAL,CAAeC,IAAf,GAAsB,IAAtB;;AAEA,UAAI,CAACZ,IAAI,CAACW,SAAL,CAAeE,cAAf,CAA8B,eAA9B,CAAL,EAAqD;AACjDC,QAAAA,MAAM,CAACC,cAAP,CAAsBf,IAAI,CAACW,SAA3B,EAAsC,eAAtC,EAAuD;AACnDK,UAAAA,GAAG,GAAG;AACF,mBAAO,KAAKC,QAAL,CAAcC,MAArB;AACH;;AAHkD,SAAvD;AAKH;;AAED,UAAI,CAAClB,IAAI,CAACW,SAAL,CAAeE,cAAf,CAA8B,SAA9B,CAAL,EAA+C;AAC3CC,QAAAA,MAAM,CAACC,cAAP,CAAsBf,IAAI,CAACW,SAA3B,EAAsC,SAAtC,EAAiD;AAC7CK,UAAAA,GAAG,GAAG;AACF,kBAAMG,IAAI,GAAG,KAAKC,YAAL,CAAkBb,SAAlB,KAAgC,KAAKc,YAAL,CAAkBd,SAAlB,CAA7C;AACA,mBAAOY,IAAI,CAACG,OAAZ;AACH,WAJ4C;;AAK7CC,UAAAA,GAAG,CAACC,GAAD,EAAc;AACb,kBAAML,IAAI,GAAG,KAAKC,YAAL,CAAkBb,SAAlB,KAAgC,KAAKc,YAAL,CAAkBd,SAAlB,CAA7C;AACAY,YAAAA,IAAI,CAACG,OAAL,GAAeE,GAAf;AACH;;AAR4C,SAAjD;AAUH;;AAED,UAAI,CAACxB,IAAI,CAACW,SAAL,CAAeE,cAAf,CAA8B,QAA9B,CAAL,EAA8C;AAC1CC,QAAAA,MAAM,CAACC,cAAP,CAAsBf,IAAI,CAACW,SAA3B,EAAsC,QAAtC,EAAgD;AAC5CK,UAAAA,GAAG,GAAG;AACF,mBAAO,KAAKS,eAAL,EAAP;AACH,WAH2C;;AAI5CF,UAAAA,GAAG,CAACC,GAAD,EAAc;AACb,iBAAKE,eAAL,CAAqBF,GAArB;AACH;;AAN2C,SAAhD;AAQH;;AAED,UAAI,CAACxB,IAAI,CAACW,SAAL,CAAeE,cAAf,CAA8B,WAA9B,CAAL,EAAiD;AAC7CC,QAAAA,MAAM,CAACC,cAAP,CAAsBf,IAAI,CAACW,SAA3B,EAAsC,WAAtC,EAAmD;AAC/CK,UAAAA,GAAG,GAAG;AACF,mBAAO,KAAKW,SAAL,CAAenB,WAAf,CAAP;AACH;;AAH8C,SAAnD;AAKH;;AAED,UAAI,CAACR,IAAI,CAACW,SAAL,CAAeE,cAAf,CAA8B,QAA9B,CAAL,EAA8C;AAC1CC,QAAAA,MAAM,CAACC,cAAP,CAAsBf,IAAI,CAACW,SAA3B,EAAsC,QAAtC,EAAgD;AAC5CK,UAAAA,GAAG,GAAG;AAAA;;AACF,mBAAO,yBAAKW,SAAL,CAAenB,WAAf,sCAA6BoB,MAA7B,KAAuC,CAA9C;AACH,WAH2C;;AAI5CL,UAAAA,GAAG,CAACC,GAAD,EAAc;AACb,kBAAMK,SAAS,GAAG,KAAKF,SAAL,CAAenB,WAAf,CAAlB;;AACA,gBAAIqB,SAAJ,EAAe;AACXA,cAAAA,SAAS,CAACD,MAAV,GAAmBJ,GAAnB;AACH;AACJ;;AAT2C,SAAhD;AAWH;;AAED,UAAI,CAACxB,IAAI,CAACW,SAAL,CAAeE,cAAf,CAA8B,OAA9B,CAAL,EAA6C;AACzCC,QAAAA,MAAM,CAACC,cAAP,CAAsBf,IAAI,CAACW,SAA3B,EAAsC,OAAtC,EAA+C;AAC3CK,UAAAA,GAAG,GAAG;AAAA;;AACF,mBAAO,0BAAKW,SAAL,CAAenB,WAAf,uCAA6BsB,KAA7B,KAAsC,CAA7C;AACH,WAH0C;;AAI3CP,UAAAA,GAAG,CAACC,GAAD,EAAc;AACb,kBAAMK,SAAS,GAAG,KAAKF,SAAL,CAAenB,WAAf,CAAlB;;AACA,gBAAIqB,SAAJ,EAAe;AACXA,cAAAA,SAAS,CAACC,KAAV,GAAkBN,GAAlB;AACH;AACJ;;AAT0C,SAA/C;AAWH,O,CAED;;;AACAxB,MAAAA,IAAI,CAACW,SAAL,CAAeoB,cAAf,GAAgC,YAAY;AACxCtB,QAAAA,KAAK,CAACuB,eAAN,CAAsB,IAAtB;AACH,OAFD;;AAIAhC,MAAAA,IAAI,CAACW,SAAL,CAAesB,SAAf,GAA2B,UAAUC,IAAV,EAAiCC,SAAjC,EAAuD;AAC9ED,QAAAA,IAAI,GAAGE,MAAM,CAACF,IAAD,CAAb;AACA,YAAIV,GAAG,GAAG,IAAV;AACA,cAAMa,GAAG,GAAGH,IAAI,CAACI,KAAL,CAAW,GAAX,CAAZ;;AACA,aAAK,IAAI1C,CAAC,GAAG,CAAR,EAAW2C,CAAC,GAAGF,GAAG,CAACnB,MAAxB,EAAgCtB,CAAC,GAAG2C,CAApC,EAAuC3C,CAAC,EAAxC,EAA4C;AACxC,cAAI,CAAC4B,GAAG,CAACgB,OAAT,EAAkB;AACd,mBAAO,IAAP;AACH;;AACDhB,UAAAA,GAAG,GAAGA,GAAG,CAACiB,cAAJ,CAAmBJ,GAAG,CAACzC,CAAD,CAAtB,CAAN;;AACA,cAAI,CAAC4B,GAAL,EAAU;AACN,mBAAO,IAAP;AACH;AACJ;;AACD,YAAIW,SAAJ,EAAe;AACXX,UAAAA,GAAG,GAAGA,GAAG,CAACJ,YAAJ,CAAiBe,SAAjB,CAAN;AACH;;AACD,eAAOX,GAAP;AACH,OAjBD;;AAmBAxB,MAAAA,IAAI,CAACW,SAAL,CAAe+B,KAAf,GAAuB,UAAUR,IAAV,EAAiCC,SAAjC,EAAuD;AAC1E,YAAI,CAAC,KAAKK,OAAV,EAAmB;AACf,iBAAO,IAAP;AACH;;AACDN,QAAAA,IAAI,GAAGE,MAAM,CAACF,IAAD,CAAb;AACA,cAAMS,GAAG,GAAG,OAAOR,SAAP,KAAqB,UAArB,GAAkC,MAAMlC,EAAE,CAAC2C,YAAH,CAAgBT,SAAhB,EAA2BU,OAA3B,CAAmC,GAAnC,EAAwC,EAAxC,CAAxC,GAAuFV,SAAS,GAAG,MAAMA,SAAT,GAAqB,EAAjI;AACA,cAAMW,KAAK,GAAG,OAAOZ,IAAI,CAACW,OAAL,CAAa,KAAb,EAAoB,GAApB,CAAP,GAAkCF,GAAhD;AACA,YAAInB,GAAG,GAAG,KAAKsB,KAAL,CAAV;;AACA,YAAItB,GAAG,KAAKuB,SAAZ,EAAuB;AACnBvB,UAAAA,GAAG,GAAG,IAAN;;AACA,cAAI,CAACA,GAAG,CAACgB,OAAT,EAAkB;AACd,mBAAO,IAAP;AACH;;AACD,gBAAMH,GAAG,GAAGH,IAAI,CAACI,KAAL,CAAW,GAAX,CAAZ;;AACA,eAAK,IAAI1C,CAAC,GAAG,CAAR,EAAW2C,CAAC,GAAGF,GAAG,CAACnB,MAAxB,EAAgCtB,CAAC,GAAG2C,CAApC,EAAuC3C,CAAC,EAAxC,EAA4C;AACxC4B,YAAAA,GAAG,GAAGA,GAAG,CAACiB,cAAJ,CAAmBJ,GAAG,CAACzC,CAAD,CAAtB,CAAN;;AACA,gBAAI,CAAC4B,GAAL,EAAU;AACN;AACH;AACJ;;AACD,cAAIA,GAAG,IAAIW,SAAX,EAAsB;AAClBX,YAAAA,GAAG,GAAGA,GAAG,CAACJ,YAAJ,CAAiBe,SAAjB,CAAN;AACH;;AACD,eAAKW,KAAL,IAAc,CAAC,CAACtB,GAAF,GAAQA,GAAR,GAAc,IAA5B;AACH;;AACD,eAAOA,GAAP;AACH,OA1BD;;AA4BAxB,MAAAA,IAAI,CAACW,SAAL,CAAegB,SAAf,GAA2B,UAAUQ,SAAV,EAA+B;AACtD,YAAI,CAACA,SAAL,EAAgB;AACZ,iBAAO,IAAP;AACH;;AACD,cAAMQ,GAAG,GAAG,OAAOR,SAAP,KAAqB,UAArB,GAAkClC,EAAE,CAAC2C,YAAH,CAAgBT,SAAhB,EAA2BU,OAA3B,CAAmC,GAAnC,EAAwC,EAAxC,CAAlC,GAAgFV,SAA5F;AACA,cAAMW,KAAK,GAAG,OAAOH,GAArB;AACA,YAAInB,GAAG,GAAG,KAAKsB,KAAL,CAAV;;AACA,YAAItB,GAAG,KAAKuB,SAAZ,EAAuB;AACnBvB,UAAAA,GAAG,GAAG,KAAKJ,YAAL,CAAkBe,SAAlB,CAAN;AACA,eAAKW,KAAL,IAActB,GAAd;AACH;;AACD,eAAOA,GAAP;AACH,OAZD;;AAcAxB,MAAAA,IAAI,CAACW,SAAL,CAAeqC,KAAf,GAAuB,UAAaC,IAAb,EAAiCC,MAAjC,EAA+CrD,EAA/C,EAAkGC,MAAlG,EAAgH;AACnI,YAAIF,CAAC,GAAG,CAAR;AAAA,YAAWuD,MAAM,GAAG,KAAKlC,QAAzB;AAAA,YAAmCmC,IAAI,GAAGD,MAAM,CAAC,CAAD,CAAhD;AACA,YAAIE,KAAK,GAAG,CAAZ;;AACA,YAAI,OAAQJ,IAAR,KAAkB,QAAtB,EAAgC;AAC5BI,UAAAA,KAAK,GAAGJ,IAAR;AACAA,UAAAA,IAAI,GAAG,IAAP;AACH,SAHD,MAGO;AACHI,UAAAA,KAAK,GAAGJ,IAAI,CAAC/B,MAAb;AACH;;AACD,YAAI,OAAQgC,MAAR,KAAoB,UAAxB,EAAoC;AAChCpD,UAAAA,MAAM,GAAGD,EAAT;AACAA,UAAAA,EAAE,GAAGqD,MAAL;AACH,SAHD,MAGO,IAAIA,MAAM,YAAYlD,IAAlB,IAA0BkD,MAAM,YAAYhD,MAAhD,EAAwD;AAC3DkD,UAAAA,IAAI,GAAGF,MAAP;AACH;;AACD,YAAI,CAACE,IAAL,EAAW;AACP,iBAAOE,MAAM,CAACnD,KAAP,CAAa,uBAAb,CAAP;AACH;;AACD,aAAK,IAAIoC,CAAC,GAAG,KAAKtB,QAAL,CAAcC,MAA3B,EAAmCtB,CAAC,GAAG2C,CAAvC,EAA0C3C,CAAC,EAA3C,EAA+C;AAC3C,gBAAMF,EAAE,GAAGyD,MAAM,CAACvD,CAAD,CAAjB;;AACA,cAAIA,CAAC,GAAGyD,KAAR,EAAe;AACX3D,YAAAA,EAAE,CAAC6D,MAAH,GAAY,IAAZ;AACA9D,YAAAA,WAAW,CAACC,EAAD,EAAKuD,IAAI,GAAGA,IAAI,CAACrD,CAAD,CAAP,GAAamD,SAAtB,EAAiCnD,CAAjC,EAAoCC,EAApC,EAAwCC,MAAxC,CAAX;AACH,WAHD,MAGO;AACHJ,YAAAA,EAAE,CAACkB,IAAH,GAAU,IAAV;AACAlB,YAAAA,EAAE,CAAC6D,MAAH,GAAY,KAAZ;AACH;AACJ;;AACD,eAAO3D,CAAC,GAAGyD,KAAX,EAAkBzD,CAAC,EAAnB,EAAuB;AACnB,gBAAMF,EAAE,GAAG8D,EAAE,CAACC,WAAH,CAAeL,IAAf,EAAqB,IAArB,CAAX;AACA1D,UAAAA,EAAE,CAAC6D,MAAH,GAAY,IAAZ;AACA9D,UAAAA,WAAW,CAACC,EAAD,EAAKuD,IAAI,GAAGA,IAAI,CAACrD,CAAD,CAAP,GAAamD,SAAtB,EAAiCnD,CAAjC,EAAoCC,EAApC,EAAwCC,MAAxC,CAAX;AACH;AACJ,OAjCD,C,CAmCA;;;AACAE,MAAAA,IAAI,CAACW,SAAL,CAAe+C,OAAf,GAAyB,UAAUR,MAAV,EAAwBrD,EAAxB,EAA4DC,MAA5D,EAA+E;AACpG,YAAIsD,IAAI,GAAG,IAAX;;AACA,YAAI,OAAQF,MAAR,KAAoB,UAAxB,EAAoC;AAChCpD,UAAAA,MAAM,GAAGD,EAAT;AACAA,UAAAA,EAAE,GAAGqD,MAAL;AACH,SAHD,MAGO,IAAIA,MAAM,YAAYlD,IAAlB,IAA0BkD,MAAM,YAAYhD,MAAhD,EAAwD;AAC3DkD,UAAAA,IAAI,GAAGF,MAAP;AACH;;AACD,YAAItD,CAAC,GAAG,KAAKqB,QAAL,CAAc0C,SAAd,CAAwBC,CAAC,IAAI,CAACA,CAAC,CAACL,MAAhC,CAAR;AAAA,YAAiD7D,EAAE,GAAG,IAAtD;;AACA,YAAI0D,IAAJ,EAAU;AACN1D,UAAAA,EAAE,GAAG8D,EAAE,CAACC,WAAH,CAAeL,IAAf,EAAqB,IAArB,CAAL;AACH,SAFD,MAEO,IAAIxD,CAAC,KAAK,CAAC,CAAX,EAAc;AACjBF,UAAAA,EAAE,GAAG,KAAKuB,QAAL,CAAcrB,CAAd,CAAL;AACH,SAFM,MAEA;AACHF,UAAAA,EAAE,GAAG8D,EAAE,CAACC,WAAH,CAAe,KAAKxC,QAAL,CAAc,CAAd,CAAf,EAAiC,IAAjC,CAAL;AACH;;AACDvB,QAAAA,EAAE,CAAC6D,MAAH,GAAY,IAAZ;AACA,cAAMM,KAAK,GAAGjE,CAAC,KAAK,CAAC,CAAP,GAAW,KAAKqB,QAAL,CAAcC,MAAzB,GAAkCtB,CAAhD;;AACA,YAAI,CAACC,EAAL,EAAS;AACL,iBAAO;AAAEH,YAAAA,EAAF;AAAME,YAAAA;AAAN,WAAP;AACH;;AACD,eAAOH,WAAW,CAACC,EAAD,EAAKmE,KAAL,EAAYd,SAAZ,EAAuBlD,EAAvB,EAA2BC,MAA3B,CAAlB;AACH,OAtBD;;AAmCAE,MAAAA,IAAI,CAACW,SAAL,CAAemD,IAAf,GAAsB,UAAUtC,GAAV,EAAmDuC,OAAnD,EAAsEC,OAAtE,EAAgG;AAClH,YAAI9B,IAAJ,EAAkBrC,EAAlB;;AACA,YAAI,OAAQ2B,GAAR,KAAiB,UAArB,EAAiC;AAC7B3B,UAAAA,EAAE,GAAG2B,GAAL;AACH,SAFD,MAEO,IAAI,OAAQA,GAAR,KAAiB,QAAjB,IAA6B,OAAQA,GAAR,KAAiB,QAAlD,EAA4D;AAC/DU,UAAAA,IAAI,GAAGE,MAAM,CAACZ,GAAD,CAAb;AACH,SAFM,MAEA;AACH,iBAAO,EAAP;AACH;;AACD,YAAIa,GAAW,GAAG,EAAlB;;AACA,aAAK,IAAIzC,CAAC,GAAG,CAAR,EAAW2C,CAAC,GAAG,KAAKtB,QAAL,CAAcC,MAAlC,EAA0CtB,CAAC,GAAG2C,CAA9C,EAAiD3C,CAAC,EAAlD,EAAsD;AAClD,gBAAMgE,CAAC,GAAG,KAAK3C,QAAL,CAAcrB,CAAd,CAAV;;AACA,cAAIoE,OAAJ,YAAIA,OAAO,CAAEC,QAAT,CAAkBL,CAAC,CAAC1B,IAApB,CAAJ,EAA+B;AAC3B;AACH,WAFD,MAEO,IAAI6B,OAAJ,EAAa;AAChBH,YAAAA,CAAC,CAACL,MAAF,GAAW1D,EAAE,GAAG,CAACA,EAAE,CAAC+D,CAAD,CAAN,GAAaA,CAAC,CAAC1B,IAAF,KAAWA,IAArC;AACH,WAFM,MAEA;AACH0B,YAAAA,CAAC,CAACL,MAAF,GAAW1D,EAAE,GAAG,CAAC,CAACA,EAAE,CAAC+D,CAAD,CAAP,GAAcA,CAAC,CAAC1B,IAAF,KAAWA,IAAtC;AACH;;AACD,cAAI0B,CAAC,CAACL,MAAN,EAAc;AACVlB,YAAAA,GAAG,CAAC6B,IAAJ,CAASN,CAAT;AACH;AACJ;;AACD,eAAOvB,GAAP;AACH,OAxBD,C,CA0BA;;;AACArC,MAAAA,IAAI,CAACW,SAAL,CAAewD,UAAf,GAA4B,UAAUC,UAAV,EAA4BC,QAA5B,EAA6CC,QAAgB,GAAG,CAAhE,EAAmE;AAC3FD,QAAAA,QAAQ,GAAGA,QAAQ,IAAI,KAAKE,cAAL,EAAvB,CAD2F,CAE3F;;AACA,YAAIC,KAAK,GAAGJ,UAAU,CAACtC,KAAX,GAAmBuC,QAAQ,CAACvC,KAAxC,CAH2F,CAI3F;;AACA,YAAIuC,QAAQ,CAACzC,MAAT,GAAkB4C,KAAlB,GAA0BJ,UAAU,CAACxC,MAAzC,EAAiD;AAC7C4C,UAAAA,KAAK,GAAGJ,UAAU,CAACxC,MAAX,GAAoByC,QAAQ,CAACzC,MAArC;AACH;;AACD,aAAK4C,KAAL,GAAaC,IAAI,CAACC,GAAL,CAASF,KAAT,EAAgBF,QAAhB,CAAb;AACH,OATD,C,CAWA;;;AACAtE,MAAAA,IAAI,CAACW,SAAL,CAAegE,SAAf,GAA2B,UAAUP,UAAV,EAA4BC,QAA5B,EAA6CC,QAAgB,GAAG,CAAhE,EAAmE;AAC1FD,QAAAA,QAAQ,GAAGA,QAAQ,IAAI,KAAKE,cAAL,EAAvB;AACA,YAAIK,CAAC,GAAGH,IAAI,CAACC,GAAL,CAASN,UAAU,CAACtC,KAAX,GAAmBuC,QAAQ,CAACvC,KAArC,EAA4CwC,QAA5C,CAAR;AACA,cAAMO,CAAC,GAAGJ,IAAI,CAACK,KAAL,CAAWF,CAAC,GAAGP,QAAQ,CAACzC,MAAxB,CAAV;;AACA,YAAIiD,CAAC,IAAIT,UAAU,CAACxC,MAApB,EAA4B;AACxB,eAAKE,KAAL,GAAa2C,IAAI,CAACK,KAAL,CAAWF,CAAC,GAAGP,QAAQ,CAACvC,KAAxB,CAAb;AACA,eAAKF,MAAL,GAAciD,CAAd;AACH,SAHD,MAGO;AACHD,UAAAA,CAAC,GAAGH,IAAI,CAACC,GAAL,CAASN,UAAU,CAACxC,MAAX,GAAoByC,QAAQ,CAACzC,MAAtC,EAA8C0C,QAA9C,CAAJ;AACA,eAAKxC,KAAL,GAAa2C,IAAI,CAACK,KAAL,CAAWF,CAAC,GAAGP,QAAQ,CAACvC,KAAxB,CAAb;AACA,eAAKF,MAAL,GAAc6C,IAAI,CAACK,KAAL,CAAWF,CAAC,GAAGP,QAAQ,CAACzC,MAAxB,CAAd;AACH;AACJ,OAZD,C,CAcA;;;AACA5B,MAAAA,IAAI,CAACW,SAAL,CAAeoE,SAAf,GAA2B,YAAY;AACnC,eAAO,KAAKxB,MAAZ;AACH,OAFD;;AAIAvD,MAAAA,IAAI,CAACW,SAAL,CAAeqE,SAAf,GAA2B,UAAUxD,GAAV,EAAwB;AAC/C,aAAK+B,MAAL,GAAc/B,GAAd;AACA,eAAOA,GAAP;AACH,OAHD,C,CAKA;;;AACAxB,MAAAA,IAAI,CAACW,SAAL,CAAeP,KAAf,GAAuB,UAAUoB,GAAV,EAA+B;AAClD,YAAI,CAACA,GAAL,EAAU,CACT,CADD,MACO,IAAIA,GAAG,YAAYpB,KAAnB,EAA0B;AAC7B,eAAK6E,KAAL,GAAazD,GAAb;AACH,SAFM,MAEA;AACH,eAAKyD,KAAL,GAAa7E,KAAK,CAAC8E,KAAN,CAAYC,OAAZ,CAAoB3D,GAApB,CAAb;AACH;;AACD,eAAO,IAAP;AACH,OARD,C,CAUA;;;AACAxB,MAAAA,IAAI,CAACW,SAAL,CAAeyE,iBAAf,GAAmC,UAAU5D,GAAV,EAAwB;AACvD,YAAI,KAAK6D,cAAT,EAAyB;AACrB,eAAKA,cAAL,CAAoBC,iBAApB,CAAsC9D,GAAtC;AACH;AACJ,OAJD;;AAMAxB,MAAAA,IAAI,CAACW,SAAL,CAAe4E,gBAAf,GAAkC,YAAY;AAC1C,eAAO,KAAKF,cAAL,IAAuB,KAAKA,cAAL,CAAoBG,gBAApB,EAA9B;AACH,OAFD,C,CAIA;;;AACAxF,MAAAA,IAAI,CAACW,SAAL,CAAe8E,YAAf,GAA8B,UAAUC,GAAV,EAAuB,GAAGC,MAA1B,EAAyC;AACnE,cAAMC,MAAM,GAAG,KAAKjE,SAAL;AAAA;AAAA,qCAAf;;AACA,YAAIiE,MAAJ,EAAY;AACRA,UAAAA,MAAM,CAACC,MAAP,CAAcH,GAAd,EAAmB,GAAGC,MAAtB;AACH,SAFD,MAEO;AACHxF,UAAAA,KAAK,CAAC,0CAAD,CAAL;AACH;;AACD,eAAO,KAAKwB,SAAL,CAAetB,KAAf,KAAyB,KAAKsB,SAAL,CAAerB,QAAf,CAAhC;AACH,OARD", "sourcesContent": ["\r\n/**\r\n * Node扩展方法\r\n */\r\n\r\nimport BaseLocale from \"../base/BaseLocale\";\r\nimport { Node, js, Prefab, SwihNodeCallback, error, Size, Color, Label, RichText, UIOpacity, UITransform, Tween } from \"cc\";\r\n\r\nNode.prototype.Data = null\r\n\r\nif (!Node.prototype.hasOwnProperty('ChildrenCount')) {\r\n    Object.defineProperty(Node.prototype, 'ChildrenCount', {\r\n        get() {\r\n            return this.children.length\r\n        }\r\n    })\r\n}\r\n\r\nif (!Node.prototype.hasOwnProperty('opacity')) {\r\n    Object.defineProperty(Node.prototype, 'opacity', {\r\n        get() {\r\n            const cmpt = this.getComponent(UIOpacity) || this.addComponent(UIOpacity)\r\n            return cmpt.opacity\r\n        },\r\n        set(val: number) {\r\n            const cmpt = this.getComponent(UIOpacity) || this.addComponent(UIOpacity)\r\n            cmpt.opacity = val\r\n        }\r\n    })\r\n}\r\n\r\nif (!Node.prototype.hasOwnProperty('zIndex')) {\r\n    Object.defineProperty(Node.prototype, 'zIndex', {\r\n        get() {\r\n            return this.getSiblingIndex()\r\n        },\r\n        set(val: number) {\r\n            this.setSiblingIndex(val)\r\n        }\r\n    })\r\n}\r\n\r\nif (!Node.prototype.hasOwnProperty('transform')) {\r\n    Object.defineProperty(Node.prototype, 'transform', {\r\n        get() {\r\n            return this.Component(UITransform)\r\n        }\r\n    })\r\n}\r\n\r\nif (!Node.prototype.hasOwnProperty('Height')) {\r\n    Object.defineProperty(Node.prototype, 'Height', {\r\n        get() {\r\n            return this.Component(UITransform)?.height || 0\r\n        },\r\n        set(val: number) {\r\n            const transform = this.Component(UITransform)\r\n            if (transform) {\r\n                transform.height = val\r\n            }\r\n        }\r\n    })\r\n}\r\n\r\nif (!Node.prototype.hasOwnProperty('Width')) {\r\n    Object.defineProperty(Node.prototype, 'Width', {\r\n        get() {\r\n            return this.Component(UITransform)?.width || 0\r\n        },\r\n        set(val: number) {\r\n            const transform = this.Component(UITransform)\r\n            if (transform) {\r\n                transform.width = val\r\n            }\r\n        }\r\n    })\r\n}\r\n\r\n// 停止所有动画\r\nNode.prototype.stopAllActions = function () {\r\n    Tween.stopAllByTarget(this)\r\n}\r\n\r\nNode.prototype.FindChild = function (name: string | number, className?: any): any {\r\n    name = String(name)\r\n    let val = this\r\n    const arr = name.split('/')\r\n    for (let i = 0, l = arr.length; i < l; i++) {\r\n        if (!val.isValid) {\r\n            return null\r\n        }\r\n        val = val.getChildByName(arr[i])\r\n        if (!val) {\r\n            return null\r\n        }\r\n    }\r\n    if (className) {\r\n        val = val.getComponent(className)\r\n    }\r\n    return val\r\n}\r\n\r\nNode.prototype.Child = function (name: string | number, className?: any): any {\r\n    if (!this.isValid) {\r\n        return null\r\n    }\r\n    name = String(name)\r\n    const cls = typeof className === 'function' ? '_' + js.getClassName(className).replace('.', '') : (className ? '_' + className : '')\r\n    const field = '$_' + name.replace(/\\//g, '_') + cls\r\n    let val = this[field]\r\n    if (val === undefined) {\r\n        val = this\r\n        if (!val.isValid) {\r\n            return null\r\n        }\r\n        const arr = name.split('/')\r\n        for (let i = 0, l = arr.length; i < l; i++) {\r\n            val = val.getChildByName(arr[i])\r\n            if (!val) {\r\n                break\r\n            }\r\n        }\r\n        if (val && className) {\r\n            val = val.getComponent(className)\r\n        }\r\n        this[field] = !!val ? val : null\r\n    }\r\n    return val\r\n}\r\n\r\nNode.prototype.Component = function (className: any): any {\r\n    if (!className) {\r\n        return null\r\n    }\r\n    const cls = typeof className === 'function' ? js.getClassName(className).replace('.', '') : className\r\n    const field = '$_' + cls\r\n    let val = this[field]\r\n    if (val === undefined) {\r\n        val = this.getComponent(className)\r\n        this[field] = val\r\n    }\r\n    return val\r\n};\r\n\r\nNode.prototype.Items = function <T>(list: T[] | number, prefab?: any, cb?: (it: Node, data: T, i: number) => void | any, target?: any) {\r\n    let i = 0, childs = this.children, item = childs[0]\r\n    let count = 0\r\n    if (typeof (list) === 'number') {\r\n        count = list\r\n        list = null\r\n    } else {\r\n        count = list.length\r\n    }\r\n    if (typeof (prefab) === 'function') {\r\n        target = cb\r\n        cb = prefab\r\n    } else if (prefab instanceof Node || prefab instanceof Prefab) {\r\n        item = prefab\r\n    }\r\n    if (!item) {\r\n        return logger.error('Items error, not item')\r\n    }\r\n    for (let l = this.children.length; i < l; i++) {\r\n        const it = childs[i]\r\n        if (i < count) {\r\n            it.active = true\r\n            setItemData(it, list ? list[i] : undefined, i, cb, target)\r\n        } else {\r\n            it.Data = null\r\n            it.active = false\r\n        }\r\n    }\r\n    for (; i < count; i++) {\r\n        const it = mc.instantiate(item, this)\r\n        it.active = true\r\n        setItemData(it, list ? list[i] : undefined, i, cb, target)\r\n    }\r\n}\r\n\r\n// 添加一个\r\nNode.prototype.AddItem = function (prefab?: any, cb?: (it: Node, i: number) => void, target?: any): any {\r\n    let item = null\r\n    if (typeof (prefab) === 'function') {\r\n        target = cb\r\n        cb = prefab\r\n    } else if (prefab instanceof Node || prefab instanceof Prefab) {\r\n        item = prefab\r\n    }\r\n    let i = this.children.findIndex(m => !m.active), it = null\r\n    if (item) {\r\n        it = mc.instantiate(item, this)\r\n    } else if (i !== -1) {\r\n        it = this.children[i]\r\n    } else {\r\n        it = mc.instantiate(this.children[0], this)\r\n    }\r\n    it.active = true\r\n    const index = i === -1 ? this.children.length : i\r\n    if (!cb) {\r\n        return { it, i }\r\n    }\r\n    return setItemData(it, index, undefined, cb, target)\r\n}\r\n\r\nfunction setItemData(it: Node, data: any, i: number, cb: Function, target: any) {\r\n    if (!cb) {\r\n        return\r\n    } else if (target) {\r\n        cb.call(target, it, data, i)\r\n    } else {\r\n        cb(it, data, i)\r\n    }\r\n}\r\n\r\n// 切换节点\r\nNode.prototype.Swih = function (val: string | number | SwihNodeCallback, reverse?: boolean, ignores?: string): Node[] {\r\n    let name: string, cb: SwihNodeCallback\r\n    if (typeof (val) === 'function') {\r\n        cb = val\r\n    } else if (typeof (val) === 'number' || typeof (val) === 'string') {\r\n        name = String(val)\r\n    } else {\r\n        return []\r\n    }\r\n    let arr: Node[] = []\r\n    for (let i = 0, l = this.children.length; i < l; i++) {\r\n        const m = this.children[i]\r\n        if (ignores?.includes(m.name)) {\r\n            continue\r\n        } else if (reverse) {\r\n            m.active = cb ? !cb(m) : (m.name !== name)\r\n        } else {\r\n            m.active = cb ? !!cb(m) : (m.name === name)\r\n        }\r\n        if (m.active) {\r\n            arr.push(m)\r\n        }\r\n    }\r\n    return arr\r\n}\r\n\r\n// 适应大小\r\nNode.prototype.adaptScale = function (targetSize: Size, selfSize?: Size, maxScale: number = 1) {\r\n    selfSize = selfSize || this.getContentSize()\r\n    // 先算出宽度比例\r\n    let scale = targetSize.width / selfSize.width\r\n    // 如果高度大了 就用高的比例\r\n    if (selfSize.height * scale > targetSize.height) {\r\n        scale = targetSize.height / selfSize.height\r\n    }\r\n    this.scale = Math.min(scale, maxScale)\r\n}\r\n\r\n// 适应宽高\r\nNode.prototype.adaptSize = function (targetSize: Size, selfSize?: Size, maxScale: number = 1) {\r\n    selfSize = selfSize || this.getContentSize()\r\n    let r = Math.min(targetSize.width / selfSize.width, maxScale)\r\n    const h = Math.floor(r * selfSize.height)\r\n    if (h <= targetSize.height) {\r\n        this.width = Math.floor(r * selfSize.width)\r\n        this.height = h\r\n    } else {\r\n        r = Math.min(targetSize.height / selfSize.height, maxScale)\r\n        this.width = Math.floor(r * selfSize.width)\r\n        this.height = Math.floor(r * selfSize.height)\r\n    }\r\n}\r\n\r\n//\r\nNode.prototype.getActive = function () {\r\n    return this.active\r\n}\r\n\r\nNode.prototype.setActive = function (val: boolean) {\r\n    this.active = val\r\n    return val\r\n}\r\n\r\n// 设置颜色\r\nNode.prototype.Color = function (val: string | Color) {\r\n    if (!val) {\r\n    } else if (val instanceof Color) {\r\n        this.color = val\r\n    } else {\r\n        this.color = Color.WHITE.fromHEX(val)\r\n    }\r\n    return this\r\n}\r\n\r\n// 设置触摸事件穿透\r\nNode.prototype.SetSwallowTouches = function (val: boolean) {\r\n    if (this._touchListener) {\r\n        this._touchListener.setSwallowTouches(val)\r\n    }\r\n}\r\n\r\nNode.prototype.IsSwallowTouches = function () {\r\n    return this._touchListener && this._touchListener.isSwallowTouches()\r\n}\r\n\r\n// 设置多语言key\r\nNode.prototype.setLocaleKey = function (key: string, ...params: any[]) {\r\n    const locale = this.Component(BaseLocale)\r\n    if (locale) {\r\n        locale.setKey(key, ...params)\r\n    } else {\r\n        error('setLocaleKey error, not LocaleComponent!')\r\n    }\r\n    return this.Component(Label) || this.Component(RichText)\r\n}\r\n"]}