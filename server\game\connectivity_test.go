package game

import (
	"fmt"
	"testing"
)

// 测试连通性：确保每个子节点都有父节点
func TestConnectivity(t *testing.T) {
	fmt.Println("=== 连通性测试 ===")

	// 测试多个地图
	for testCase := 0; testCase < 5; testCase++ {
		fmt.Printf("\n--- 测试案例 %d ---\n", testCase+1)

		// 生成地图
		mapData := GenerateMapData(6)

		// 检查每一层的连通性
		allConnected := true
		for layerIndex := 1; layerIndex < len(mapData); layerIndex++ {
			currentLayer := mapData[layerIndex]
			prevLayer := mapData[layerIndex-1]

			fmt.Printf("第%d层到第%d层:\n", layerIndex, layerIndex+1)
			fmt.Printf("  父节点数: %d, 子节点数: %d\n", len(prevLayer), len(currentLayer))

			// 检查每个子节点是否都有父节点
			childHasParent := make([]bool, len(currentLayer))

			// 统计父节点的连接
			for parentIndex, parent := range prevLayer {
				fmt.Printf("  父节点%d -> %v\n", parentIndex, parent.Children)
				for _, childIndex := range parent.Children {
					if int(childIndex) < len(childHasParent) {
						childHasParent[childIndex] = true
					}
				}
			}

			// 检查孤立的子节点
			orphanNodes := make([]int, 0)
			for childIndex, hasParent := range childHasParent {
				if !hasParent {
					orphanNodes = append(orphanNodes, childIndex)
				}
			}

			if len(orphanNodes) > 0 {
				allConnected = false
				fmt.Printf("  ✗ 发现%d个孤立子节点: %v\n", len(orphanNodes), orphanNodes)
				t.Errorf("第%d层有孤立节点: %v", layerIndex+1, orphanNodes)
			} else {
				fmt.Printf("  ✓ 所有子节点都有父节点\n")
			}

			// 检查路径交叉
			crossings := detectPathCrossings(prevLayer, currentLayer)
			if len(crossings) > 0 {
				fmt.Printf("  ✗ 发现%d个路径交叉\n", len(crossings))
				t.Errorf("第%d层到第%d层有路径交叉", layerIndex, layerIndex+1)
			} else {
				fmt.Printf("  ✓ 无路径交叉\n")
			}
		}

		if allConnected {
			fmt.Printf("🎉 测试案例%d: 所有节点都连通且无交叉\n", testCase+1)
		}
	}
}

// 测试特定的连通性场景
func TestSpecificConnectivityScenarios(t *testing.T) {
	fmt.Println("\n=== 特定连通性场景测试 ===")

	testCases := []struct {
		name        string
		parentCount int
		childCount  int
	}{
		{"3父2子", 3, 2},
		{"2父5子", 2, 5},
		{"4父3子", 4, 3},
		{"1父4子", 1, 4},
		{"5父1子", 5, 1},
		{"6父4子", 6, 4},
		{"3父7子", 3, 7},
	}

	for _, tc := range testCases {
		fmt.Printf("\n--- %s ---\n", tc.name)

		// 创建测试层
		parentLayer := make([]*MapNode, tc.parentCount)
		childLayer := make([]*MapNode, tc.childCount)

		for i := 0; i < tc.parentCount; i++ {
			parentLayer[i] = &MapNode{
				Type:     MAP_NODE_TYPE_BATTLE,
				Children: []int32{},
			}
		}

		for i := 0; i < tc.childCount; i++ {
			childLayer[i] = &MapNode{
				Type:     MAP_NODE_TYPE_BATTLE,
				Children: []int32{},
			}
		}

		// 分配子节点
		assignChildrenByStrictRegions(parentLayer, childLayer)

		// 打印连接关系
		for i, parent := range parentLayer {
			fmt.Printf("父节点%d -> %v\n", i, parent.Children)
		}

		// 检查连通性
		childHasParent := make([]bool, tc.childCount)
		for _, parent := range parentLayer {
			for _, childIndex := range parent.Children {
				if int(childIndex) < len(childHasParent) {
					childHasParent[childIndex] = true
				}
			}
		}

		orphanNodes := make([]int, 0)
		for childIndex, hasParent := range childHasParent {
			if !hasParent {
				orphanNodes = append(orphanNodes, childIndex)
			}
		}

		if len(orphanNodes) > 0 {
			t.Errorf("%s 有孤立子节点: %v", tc.name, orphanNodes)
			fmt.Printf("✗ 发现孤立子节点: %v\n", orphanNodes)
		} else {
			fmt.Printf("✓ 所有子节点都有父节点\n")
		}

		// 检查交叉
		crossings := detectPathCrossings(parentLayer, childLayer)
		if len(crossings) > 0 {
			t.Errorf("%s 存在路径交叉: %v", tc.name, crossings)
			fmt.Printf("✗ 发现路径交叉: %v\n", crossings)
		} else {
			fmt.Printf("✓ 无路径交叉\n")
		}

		// 检查每个父节点是否都有子节点
		for i, parent := range parentLayer {
			if len(parent.Children) == 0 {
				t.Errorf("%s 父节点%d没有子节点", tc.name, i)
				fmt.Printf("✗ 父节点%d没有子节点\n", i)
			}
		}
	}
}

// 测试大规模地图的连通性
func TestLargeMapConnectivity(t *testing.T) {
	fmt.Println("\n=== 大规模地图连通性测试 ===")

	sizes := []int32{10, 15, 20}

	for _, size := range sizes {
		fmt.Printf("\n--- %d层地图 ---\n", size)
		mapData := GenerateMapData(size)

		totalNodes := 0
		totalConnections := 0
		hasOrphanNodes := false

		for layerIndex := 0; layerIndex < len(mapData); layerIndex++ {
			layer := mapData[layerIndex]
			totalNodes += len(layer)

			// 统计连接数
			for _, node := range layer {
				totalConnections += len(node.Children)
			}

			// 检查连通性（除了第一层）
			if layerIndex > 0 {
				prevLayer := mapData[layerIndex-1]
				childHasParent := make([]bool, len(layer))

				for _, parent := range prevLayer {
					for _, childIndex := range parent.Children {
						if int(childIndex) < len(childHasParent) {
							childHasParent[childIndex] = true
						}
					}
				}

				orphanCount := 0
				for _, hasParent := range childHasParent {
					if !hasParent {
						orphanCount++
					}
				}

				if orphanCount > 0 {
					hasOrphanNodes = true
					fmt.Printf("第%d层有%d个孤立节点\n", layerIndex+1, orphanCount)
				}
			}
		}

		fmt.Printf("总节点数: %d\n", totalNodes)
		fmt.Printf("总连接数: %d\n", totalConnections)

		if hasOrphanNodes {
			t.Errorf("%d层地图存在孤立节点", size)
			fmt.Printf("✗ 存在孤立节点\n")
		} else {
			fmt.Printf("✓ 所有节点都连通\n")
		}
	}
}
