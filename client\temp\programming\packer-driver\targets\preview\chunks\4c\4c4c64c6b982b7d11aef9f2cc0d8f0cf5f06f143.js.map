{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/TestPnlCtrl.ts"], "names": ["_decorator", "log", "ccclass", "TestPnlCtrl", "mc", "BasePnlCtrl", "listenEventMaps", "onCreate", "set<PERSON>ara<PERSON>", "isMask", "onEnter", "data", "onRemove", "onClean", "onClickButton", "event"], "mappings": ";;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,G,OAAAA,G;;;;;;;;;OACf;AAAEC,QAAAA;AAAF,O,GAAcF,U;;yBAGCG,W,GADpBD,O,UAAD,MACqBC,WADrB,SACyCC,EAAE,CAACC,WAD5C,CACwD;AAEpD;AACA;AAEOC,QAAAA,eAAe,GAAG;AACrB,iBAAO,EAAP;AACH;;AAEYC,QAAAA,QAAQ,GAAG;AAAA;;AAAA;AACpB,YAAA,KAAI,CAACC,QAAL,CAAc;AAAEC,cAAAA,MAAM,EAAE;AAAV,aAAd;AADoB;AAEvB;;AAEMC,QAAAA,OAAO,CAACC,IAAD,EAAY;AACtBV,UAAAA,GAAG,CAAC,qBAAD,CAAH;AACH;;AAEMW,QAAAA,QAAQ,GAAG;AACdX,UAAAA,GAAG,CAAC,sBAAD,CAAH;AACH;;AAEMY,QAAAA,OAAO,GAAG;AACbZ,UAAAA,GAAG,CAAC,qBAAD,CAAH;AACH,SAvBmD,CAyBpD;AACA;AAEA;;;AACAa,QAAAA,aAAa,CAACC,KAAD,EAAoBJ,IAApB,EAAkC;AAC3CV,UAAAA,GAAG,CAAC,eAAD,EAAkBU,IAAlB,CAAH;AACH,SA/BmD,CAgCpD;AACA;AAEA;;;AAnCoD,O", "sourcesContent": ["import { _decorator, log, EventTouch } from \"cc\";\nconst { ccclass } = _decorator;\n\n@ccclass\nexport default class TestPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    //@end\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n        this.setParam({ isMask: false })\n    }\n\n    public onEnter(data: any) {\n        log('TestPnlCtrl onEnter')\n    }\n\n    public onRemove() {\n        log('TestPnlCtrl onRemove')\n    }\n\n    public onClean() {\n        log('TestPnlCtrl onClean')\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://button_be\n    onClickButton(event: EventTouch, data: string) {\n        log('onClickButton', data)\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n}\n"]}