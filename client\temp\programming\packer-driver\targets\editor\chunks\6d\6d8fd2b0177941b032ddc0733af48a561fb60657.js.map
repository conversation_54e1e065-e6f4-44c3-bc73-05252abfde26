{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAAkgC,uCAAlgC,EAA2lC,uCAA3lC,EAAisC,uCAAjsC,EAAoyC,wCAApyC,EAAs4C,wCAAt4C,EAA0+C,wCAA1+C,EAAilD,wCAAjlD,EAAqrD,wCAArrD,EAA0xD,wCAA1xD,EAA83D,wCAA93D,EAAm+D,wCAAn+D,EAAykE,wCAAzkE,EAAsrE,wCAAtrE,EAA8xE,wCAA9xE,EAAw4E,wCAAx4E,EAAg/E,wCAAh/E,EAAylF,wCAAzlF,EAAqsF,wCAArsF,EAA+yF,wCAA/yF,EAAu5F,wCAAv5F,EAA+/F,wCAA//F,EAAymG,wCAAzmG,EAAqtG,wCAArtG,EAA4zG,wCAA5zG,EAAs6G,wCAAt6G,EAA4gH,wCAA5gH,EAAmnH,wCAAnnH,EAAstH,wCAAttH,EAAg0H,wCAAh0H,EAAw6H,wCAAx6H,EAA8gI,wCAA9gI,EAAmnI,wCAAnnI,EAA8tI,wCAA9tI,EAAq0I,wCAAr0I,EAAq7I,wCAAr7I,EAAyhJ,wCAAzhJ,EAAkoJ,wCAAloJ,EAAyuJ,wCAAzuJ,EAAg1J,wCAAh1J,EAAo7J,wCAAp7J,EAAuhK,wCAAvhK,EAA0nK,wCAA1nK,EAAguK,wCAAhuK,EAAw0K,wCAAx0K,EAA66K,wCAA76K,EAAmhL,wCAAnhL,EAAynL,wCAAznL,EAA8tL,wCAA9tL,EAA8zL,wCAA9zL,EAAi6L,wCAAj6L,EAAggM,wCAAhgM,EAAylM,wCAAzlM,EAAorM,wCAAprM,EAAmxM,wCAAnxM,EAA03M,wCAA13M,EAA09M,wCAA19M,EAAkjN,wCAAljN,EAA0pN,wCAA1pN,EAAixN,wCAAjxN,EAAs4N,wCAAt4N,EAAo/N,wCAAp/N,EAAkmO,wCAAlmO,EAA6sO,wCAA7sO,EAAwzO,wCAAxzO,EAAu6O,wCAAv6O,EAAmhP,wCAAnhP,EAA8nP,wCAA9nP,EAAyuP,wCAAzuP,EAAo1P,wCAAp1P,EAAk8P,wCAAl8P,EAAwjQ,wCAAxjQ,EAAsqQ,wCAAtqQ,EAAixQ,wCAAjxQ,EAAg4Q,wCAAh4Q,EAA6+Q,wCAA7+Q,EAA4lR,wCAA5lR,EAA4sR,wCAA5sR,EAAyzR,wCAAzzR,EAAo6R,wCAAp6R,EAAkhS,wCAAlhS,EAAgoS,wCAAhoS,EAA8uS,wCAA9uS,EAA+1S,wCAA/1S,EAAg9S,wCAAh9S,EAAikT,wCAAjkT,EAAmrT,wCAAnrT,EAA+xT,wCAA/xT,EAA24T,wCAA34T,EAAu/T,wCAAv/T,EAAimU,wCAAjmU,EAAgtU,wCAAhtU,EAA4zU,wCAA5zU,EAAw6U,wCAAx6U,EAAkhV,yCAAlhV,EAAioV,yCAAjoV,EAA8uV,yCAA9uV,EAAs1V,yCAAt1V,EAAg8V,yCAAh8V,EAA2iW,yCAA3iW,EAAmpW,yCAAnpW,EAA+vW,yCAA/vW,EAAi3W,yCAAj3W,EAA69W,yCAA79W,EAAukX,yCAAvkX,EAAkrX,yCAAlrX,EAA8xX,yCAA9xX,EAAw4X,yCAAx4X,EAAg/X,yCAAh/X,EAA2lY,yCAA3lY,EAAysY,yCAAzsY,EAAuzY,yCAAvzY,EAAq6Y,yCAAr6Y,EAAwhZ,yCAAxhZ,EAAwoZ,yCAAxoZ,EAAwvZ,yCAAxvZ,EAAy2Z,yCAAz2Z,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/App.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseModel.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWdtCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelRollNumber.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelTimer.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelWaitDot.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiColor.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiFrame.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendAnimation.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendArray.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendButton.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendCC.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendComponent.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendToggleContainer.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendVec.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AudioMgr.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NodePoolMgr.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/StorageMgr.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/EventCenter.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Utils.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/lib/base64.js\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/lib/mqttws31.js\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/lib/pb/long/long.js\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/lib/pb/protobuf/protobuf.js\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/proto/ProtoHelper.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/proto/msg.js\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/LocalConfig.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/AnimalFrameAnimConf.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Constant.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/DataType.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/ECode.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/interface.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/JsbEvent.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NotEvent.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/LoadProgressHelper.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseCondition.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseDecorator.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/BuffObj.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/RandomObj.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalStateObj.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/EncounterObj.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/FoodObj.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/RoleObj.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/MapPnlCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/TestPnlCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/WindWaitNotCtrl.ts\"), () => import(\"file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}