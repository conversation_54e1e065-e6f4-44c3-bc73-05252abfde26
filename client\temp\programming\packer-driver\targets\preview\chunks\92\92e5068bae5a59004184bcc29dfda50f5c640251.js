System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, AnimalCamp, EventType, FSPModel, AnimalObj, MapNodeObj, _dec, _class, _crd, GameModel;

  function _reportPossibleCrUseOfAnimalCamp(extras) {
    _reporterNs.report("AnimalCamp", "../../common/constant/Enums", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIFrameAnimation(extras) {
    _reporterNs.report("IFrameAnimation", "../../common/constant/interface", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventType(extras) {
    _reporterNs.report("EventType", "../../common/event/EventType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFSPModel(extras) {
    _reporterNs.report("FSPModel", "../battle/FSPModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAnimalObj(extras) {
    _reporterNs.report("AnimalObj", "./AnimalObj", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFoodObj(extras) {
    _reporterNs.report("FoodObj", "./FoodObj", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMapNodeObj(extras) {
    _reporterNs.report("MapNodeObj", "./MapNodeObj", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      AnimalCamp = _unresolved_2.AnimalCamp;
    }, function (_unresolved_3) {
      EventType = _unresolved_3.default;
    }, function (_unresolved_4) {
      FSPModel = _unresolved_4.default;
    }, function (_unresolved_5) {
      AnimalObj = _unresolved_5.default;
    }, function (_unresolved_6) {
      MapNodeObj = _unresolved_6.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ce65360IrdCeov+v5oDCn0o", "GameModel", undefined);

      /**
       * 游戏模块
       */
      _export("default", GameModel = (_dec = mc.addmodel('game'), _dec(_class = class GameModel extends mc.BaseModel {
        constructor() {
          super(...arguments);
          this.runing = false;
          //是否运行中
          this.maps = [];
          //地图数据
          this.mapPaths = [];
          //已经走的路径
          this.day = 0;
          // 天数
          this.hp = [];
          //血量
          this.winCount = 0;
          //胜利次数
          this.battleAnimals = [];
          //当前战斗区域的动物列表
          this.prepareAnimals = [];
          //当前备战区域的动物列表
          this.foods = [];
          //当前的食物列表
          this.enemyAnimals = [];
          //敌方战斗区域的动物列表
          this.frameAnimations = [];
          //动画组件 在这里统一每帧调用
          this.fspModel = null;
        }

        onCreate() {} // 初始化基础数据


        initBaseData(data) {
          console.log('initBaseData', data);
          this.day = data.day;
          this.hp = data.hp || [100, 100];
          this.winCount = data.winCount;
        }

        init(data) {
          console.log('init', data);
          this.maps = data.mapData.maps.map(m => m.nodes.map(n => new (_crd && MapNodeObj === void 0 ? (_reportPossibleCrUseOfMapNodeObj({
            error: Error()
          }), MapNodeObj) : MapNodeObj)().init(n)));
          var gameData = data.gameData,
              player = gameData.player; // Shop: this.Shop.ToPb(),
          // OtherPlayer: otherPlayer,
          // MapPaths: array.Clone(this.MapPaths),
        }

        enter() {
          this.runing = true;
        }

        leave() {
          this.runing = false;
          this.frameAnimations = [];
        }

        isRuning() {
          return this.runing;
        }

        getMaps() {
          return this.maps;
        }

        getDay() {
          return this.day;
        }

        getBattleAnimals() {
          return this.battleAnimals;
        }

        getPrepareAnimals() {
          return this.prepareAnimals;
        }

        getFoods() {
          return this.foods;
        }

        addAnimal(id) {
          if (this.battleAnimals.length < 3) {
            var animal = new (_crd && AnimalObj === void 0 ? (_reportPossibleCrUseOfAnimalObj({
              error: Error()
            }), AnimalObj) : AnimalObj)().init(id, (_crd && AnimalCamp === void 0 ? (_reportPossibleCrUseOfAnimalCamp({
              error: Error()
            }), AnimalCamp) : AnimalCamp).FRIENDLY, this.battleAnimals.length);
            this.battleAnimals.push(animal);
            this.emit((_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).UPDATE_MY_BATTLE_AREA);
          }
        } // 获取敌方动物


        getEnemyAnimals() {
          if (this.enemyAnimals.length === 0) {
            this.enemyAnimals = [new (_crd && AnimalObj === void 0 ? (_reportPossibleCrUseOfAnimalObj({
              error: Error()
            }), AnimalObj) : AnimalObj)().init(11001, (_crd && AnimalCamp === void 0 ? (_reportPossibleCrUseOfAnimalCamp({
              error: Error()
            }), AnimalCamp) : AnimalCamp).ENEMY, 0), new (_crd && AnimalObj === void 0 ? (_reportPossibleCrUseOfAnimalObj({
              error: Error()
            }), AnimalObj) : AnimalObj)().init(11001, (_crd && AnimalCamp === void 0 ? (_reportPossibleCrUseOfAnimalCamp({
              error: Error()
            }), AnimalCamp) : AnimalCamp).ENEMY, 1), new (_crd && AnimalObj === void 0 ? (_reportPossibleCrUseOfAnimalObj({
              error: Error()
            }), AnimalObj) : AnimalObj)().init(11001, (_crd && AnimalCamp === void 0 ? (_reportPossibleCrUseOfAnimalCamp({
              error: Error()
            }), AnimalCamp) : AnimalCamp).ENEMY, 2)];
          }

          return this.enemyAnimals;
        }

        addFrameAnimation(cmpt) {
          if (!this.frameAnimations.has('uuid', cmpt.uuid)) {
            this.frameAnimations.push(cmpt);
          }
        }

        removeFrameAnimation(uuid) {
          this.frameAnimations.remove('uuid', uuid);
        }

        update(dt) {
          if (!this.runing) {
            return;
          } else if (this.fspModel) {
            this.fspModel.update(dt);
          } else {
            this.updateAnimationFrame(dt * 1000);
          }
        } // 刷新动画帧 毫秒


        updateAnimationFrame(dt) {
          for (var i = this.frameAnimations.length - 1; i >= 0; i--) {
            var cmpt = this.frameAnimations[i];

            if (cmpt.isValid) {
              cmpt.updateFrame(dt);
            } else {
              this.frameAnimations.splice(i, 1);
            }
          }
        } //////////////////////////////////////////////////////////[ 战斗相关 ]///////////////////////////////////////////////////////////////////


        getFspModel() {
          return this.fspModel;
        } // 战斗开始


        battleBegin() {
          var fighters = [];
          fighters.pushArr(this.battleAnimals.map(m => m.toFighter()));
          fighters.pushArr(this.enemyAnimals.map(m => m.toFighter()));
          this.fspModel = new (_crd && FSPModel === void 0 ? (_reportPossibleCrUseOfFSPModel({
            error: Error()
          }), FSPModel) : FSPModel)().init({
            randSeed: ut.random(10000, 99999),
            fighters: fighters
          });
        } // 战斗结束 本地


        battleEndByLocal() {
          this.fspModel.stop();
          this.fspModel = null;
        } // 根据阵营删除动物 来至战斗


        removeAnimalByBattle(camp, uid) {
          if (camp === 1) {
            this.battleAnimals.remove('uid', uid);
          } else {
            this.enemyAnimals.remove('uid', uid);
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=92e5068bae5a59004184bcc29dfda50f5c640251.js.map