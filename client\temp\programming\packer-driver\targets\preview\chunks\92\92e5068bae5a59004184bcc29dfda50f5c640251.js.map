{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts"], "names": ["AnimalCamp", "EventType", "FSPModel", "AnimalObj", "MapNodeObj", "GameModel", "mc", "addmodel", "BaseModel", "runing", "maps", "mapPaths", "day", "hp", "winCount", "battleAnimals", "prepareAnimals", "foods", "enemyAnimals", "frameAnimations", "fspModel", "onCreate", "initBaseData", "data", "console", "log", "init", "mapData", "map", "m", "nodes", "n", "gameData", "player", "enter", "leave", "isRuning", "getMaps", "getDay", "getBattleAnimals", "getPrepareAnimals", "getFoods", "addAnimal", "id", "length", "animal", "FRIENDLY", "push", "emit", "UPDATE_MY_BATTLE_AREA", "getEnemyAnimals", "ENEMY", "addFrameAnimation", "cmpt", "has", "uuid", "removeFrameAnimation", "remove", "update", "dt", "updateAnimationFrame", "i", "<PERSON><PERSON><PERSON><PERSON>", "updateFrame", "splice", "getFspModel", "battleBegin", "fighters", "pushArr", "to<PERSON><PERSON>er", "randSeed", "ut", "random", "battleEndByLocal", "stop", "removeAnimalByBattle", "camp", "uid"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,iBAAAA,U;;AAEFC,MAAAA,S;;AACAC,MAAAA,Q;;AACAC,MAAAA,S;;AAEAC,MAAAA,U;;;;;;;AAEP;AACA;AACA;yBAEqBC,S,WADpBC,EAAE,CAACC,QAAH,CAAY,MAAZ,C,gBAAD,MACqBF,SADrB,SACuCC,EAAE,CAACE,SAD1C,CACoD;AAAA;AAAA;AAAA,eAExCC,MAFwC,GAEtB,KAFsB;AAEhB;AAFgB,eAIxCC,IAJwC,GAIjB,EAJiB;AAId;AAJc,eAKxCC,QALwC,GAKnB,EALmB;AAKhB;AALgB,eAOxCC,GAPwC,GAO1B,CAP0B;AAOxB;AAPwB,eAQxCC,EARwC,GAQzB,EARyB;AAQtB;AARsB,eASxCC,QATwC,GASrB,CATqB;AASnB;AATmB,eAWxCC,aAXwC,GAWX,EAXW;AAWR;AAXQ,eAYxCC,cAZwC,GAYV,EAZU;AAYP;AAZO,eAaxCC,KAbwC,GAarB,EAbqB;AAalB;AAbkB,eAexCC,YAfwC,GAeZ,EAfY;AAeT;AAfS,eAiBxCC,eAjBwC,GAiBH,EAjBG;AAiBA;AAjBA,eAkBxCC,QAlBwC,GAkBnB,IAlBmB;AAAA;;AAoBzCC,QAAAA,QAAQ,GAAG,CACjB,CArB+C,CAuBhD;;;AACOC,QAAAA,YAAY,CAACC,IAAD,EAAY;AAC3BC,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BF,IAA5B;AACA,eAAKX,GAAL,GAAWW,IAAI,CAACX,GAAhB;AACA,eAAKC,EAAL,GAAUU,IAAI,CAACV,EAAL,IAAW,CAAC,GAAD,EAAM,GAAN,CAArB;AACA,eAAKC,QAAL,GAAgBS,IAAI,CAACT,QAArB;AACH;;AAEMY,QAAAA,IAAI,CAACH,IAAD,EAAY;AACnBC,UAAAA,OAAO,CAACC,GAAR,CAAY,MAAZ,EAAoBF,IAApB;AACA,eAAKb,IAAL,GAAYa,IAAI,CAACI,OAAL,CAAajB,IAAb,CAAkBkB,GAAlB,CAAsBC,CAAC,IAAIA,CAAC,CAACC,KAAF,CAAQF,GAAR,CAAYG,CAAC,IAAI;AAAA;AAAA,0CAAiBL,IAAjB,CAAsBK,CAAtB,CAAjB,CAA3B,CAAZ;AACA,cAAMC,QAAQ,GAAGT,IAAI,CAACS,QAAtB;AAAA,cAAgCC,MAAM,GAAGD,QAAQ,CAACC,MAAlD,CAHmB,CAInB;AACA;AACA;AACH;;AAEMC,QAAAA,KAAK,GAAG;AACX,eAAKzB,MAAL,GAAc,IAAd;AACH;;AAEM0B,QAAAA,KAAK,GAAG;AACX,eAAK1B,MAAL,GAAc,KAAd;AACA,eAAKU,eAAL,GAAuB,EAAvB;AACH;;AAEMiB,QAAAA,QAAQ,GAAG;AAAE,iBAAO,KAAK3B,MAAZ;AAAoB;;AACjC4B,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAK3B,IAAZ;AAAkB;;AAC9B4B,QAAAA,MAAM,GAAG;AAAE,iBAAO,KAAK1B,GAAZ;AAAiB;;AAC5B2B,QAAAA,gBAAgB,GAAG;AAAE,iBAAO,KAAKxB,aAAZ;AAA2B;;AAChDyB,QAAAA,iBAAiB,GAAG;AAAE,iBAAO,KAAKxB,cAAZ;AAA4B;;AAClDyB,QAAAA,QAAQ,GAAG;AAAE,iBAAO,KAAKxB,KAAZ;AAAmB;;AAEhCyB,QAAAA,SAAS,CAACC,EAAD,EAAa;AACzB,cAAI,KAAK5B,aAAL,CAAmB6B,MAAnB,GAA4B,CAAhC,EAAmC;AAC/B,gBAAMC,MAAM,GAAG;AAAA;AAAA,0CAAgBnB,IAAhB,CAAqBiB,EAArB,EAAyB;AAAA;AAAA,0CAAWG,QAApC,EAA8C,KAAK/B,aAAL,CAAmB6B,MAAjE,CAAf;AACA,iBAAK7B,aAAL,CAAmBgC,IAAnB,CAAwBF,MAAxB;AACA,iBAAKG,IAAL,CAAU;AAAA;AAAA,wCAAUC,qBAApB;AACH;AACJ,SA9D+C,CAgEhD;;;AACOC,QAAAA,eAAe,GAAG;AACrB,cAAI,KAAKhC,YAAL,CAAkB0B,MAAlB,KAA6B,CAAjC,EAAoC;AAChC,iBAAK1B,YAAL,GAAoB,CAChB;AAAA;AAAA,0CAAgBQ,IAAhB,CAAqB,KAArB,EAA4B;AAAA;AAAA,0CAAWyB,KAAvC,EAA8C,CAA9C,CADgB,EAEhB;AAAA;AAAA,0CAAgBzB,IAAhB,CAAqB,KAArB,EAA4B;AAAA;AAAA,0CAAWyB,KAAvC,EAA8C,CAA9C,CAFgB,EAGhB;AAAA;AAAA,0CAAgBzB,IAAhB,CAAqB,KAArB,EAA4B;AAAA;AAAA,0CAAWyB,KAAvC,EAA8C,CAA9C,CAHgB,CAApB;AAKH;;AACD,iBAAO,KAAKjC,YAAZ;AACH;;AAEMkC,QAAAA,iBAAiB,CAACC,IAAD,EAAwB;AAC5C,cAAI,CAAC,KAAKlC,eAAL,CAAqBmC,GAArB,CAAyB,MAAzB,EAAiCD,IAAI,CAACE,IAAtC,CAAL,EAAkD;AAC9C,iBAAKpC,eAAL,CAAqB4B,IAArB,CAA0BM,IAA1B;AACH;AACJ;;AAEMG,QAAAA,oBAAoB,CAACD,IAAD,EAAe;AACtC,eAAKpC,eAAL,CAAqBsC,MAArB,CAA4B,MAA5B,EAAoCF,IAApC;AACH;;AAEMG,QAAAA,MAAM,CAACC,EAAD,EAAa;AACtB,cAAI,CAAC,KAAKlD,MAAV,EAAkB;AACd;AACH,WAFD,MAEO,IAAI,KAAKW,QAAT,EAAmB;AACtB,iBAAKA,QAAL,CAAcsC,MAAd,CAAqBC,EAArB;AACH,WAFM,MAEA;AACH,iBAAKC,oBAAL,CAA0BD,EAAE,GAAG,IAA/B;AACH;AACJ,SA9F+C,CAgGhD;;;AACOC,QAAAA,oBAAoB,CAACD,EAAD,EAAa;AACpC,eAAK,IAAIE,CAAC,GAAG,KAAK1C,eAAL,CAAqByB,MAArB,GAA8B,CAA3C,EAA8CiB,CAAC,IAAI,CAAnD,EAAsDA,CAAC,EAAvD,EAA2D;AACvD,gBAAMR,IAAI,GAAG,KAAKlC,eAAL,CAAqB0C,CAArB,CAAb;;AACA,gBAAIR,IAAI,CAACS,OAAT,EAAkB;AACdT,cAAAA,IAAI,CAACU,WAAL,CAAiBJ,EAAjB;AACH,aAFD,MAEO;AACH,mBAAKxC,eAAL,CAAqB6C,MAArB,CAA4BH,CAA5B,EAA+B,CAA/B;AACH;AACJ;AACJ,SA1G+C,CA4GhD;;;AAEOI,QAAAA,WAAW,GAAG;AAAE,iBAAO,KAAK7C,QAAZ;AAAsB,SA9GG,CAgHhD;;;AACO8C,QAAAA,WAAW,GAAG;AACjB,cAAMC,QAAQ,GAAG,EAAjB;AACAA,UAAAA,QAAQ,CAACC,OAAT,CAAiB,KAAKrD,aAAL,CAAmBa,GAAnB,CAAuBC,CAAC,IAAIA,CAAC,CAACwC,SAAF,EAA5B,CAAjB;AACAF,UAAAA,QAAQ,CAACC,OAAT,CAAiB,KAAKlD,YAAL,CAAkBU,GAAlB,CAAsBC,CAAC,IAAIA,CAAC,CAACwC,SAAF,EAA3B,CAAjB;AACA,eAAKjD,QAAL,GAAgB;AAAA;AAAA,sCAAeM,IAAf,CAAoB;AAChC4C,YAAAA,QAAQ,EAAEC,EAAE,CAACC,MAAH,CAAU,KAAV,EAAiB,KAAjB,CADsB;AAEhCL,YAAAA,QAAQ,EAAEA;AAFsB,WAApB,CAAhB;AAIH,SAzH+C,CA2HhD;;;AACOM,QAAAA,gBAAgB,GAAG;AACtB,eAAKrD,QAAL,CAAcsD,IAAd;AACA,eAAKtD,QAAL,GAAgB,IAAhB;AACH,SA/H+C,CAiIhD;;;AACOuD,QAAAA,oBAAoB,CAACC,IAAD,EAAeC,GAAf,EAA4B;AACnD,cAAID,IAAI,KAAK,CAAb,EAAgB;AACZ,iBAAK7D,aAAL,CAAmB0C,MAAnB,CAA0B,KAA1B,EAAiCoB,GAAjC;AACH,WAFD,MAEO;AACH,iBAAK3D,YAAL,CAAkBuC,MAAlB,CAAyB,KAAzB,EAAgCoB,GAAhC;AACH;AACJ;;AAxI+C,O", "sourcesContent": ["import { AnimalCamp } from \"../../common/constant/Enums\"\r\nimport { IFrameAnimation } from \"../../common/constant/interface\"\r\nimport EventType from \"../../common/event/EventType\"\r\nimport FSPModel from \"../battle/FSPModel\"\r\nimport AnimalObj from \"./AnimalObj\"\r\nimport FoodObj from \"./FoodObj\"\r\nimport MapNodeObj from \"./MapNodeObj\"\r\n\r\n/**\r\n * 游戏模块\r\n */\r\*************('game')\r\nexport default class GameModel extends mc.BaseModel {\r\n\r\n    private runing: boolean = false //是否运行中\r\n\r\n    private maps: MapNodeObj[][] = [] //地图数据\r\n    private mapPaths: number[] = [] //已经走的路径\r\n\r\n    private day: number = 0 // 天数\r\n    private hp: number[] = [] //血量\r\n    private winCount: number = 0 //胜利次数\r\n\r\n    private battleAnimals: AnimalObj[] = [] //当前战斗区域的动物列表\r\n    private prepareAnimals: AnimalObj[] = [] //当前备战区域的动物列表\r\n    private foods: FoodObj[] = [] //当前的食物列表\r\n\r\n    private enemyAnimals: AnimalObj[] = [] //敌方战斗区域的动物列表\r\n\r\n    private frameAnimations: IFrameAnimation[] = [] //动画组件 在这里统一每帧调用\r\n    private fspModel: FSPModel = null\r\n\r\n    public onCreate() {\r\n    }\r\n\r\n    // 初始化基础数据\r\n    public initBaseData(data: any) {\r\n        console.log('initBaseData', data)\r\n        this.day = data.day\r\n        this.hp = data.hp || [100, 100]\r\n        this.winCount = data.winCount\r\n    }\r\n\r\n    public init(data: any) {\r\n        console.log('init', data)\r\n        this.maps = data.mapData.maps.map(m => m.nodes.map(n => new MapNodeObj().init(n)))\r\n        const gameData = data.gameData, player = gameData.player\r\n        // Shop: this.Shop.ToPb(),\r\n        // OtherPlayer: otherPlayer,\r\n        // MapPaths: array.Clone(this.MapPaths),\r\n    }\r\n\r\n    public enter() {\r\n        this.runing = true\r\n    }\r\n\r\n    public leave() {\r\n        this.runing = false\r\n        this.frameAnimations = []\r\n    }\r\n\r\n    public isRuning() { return this.runing }\r\n    public getMaps() { return this.maps }\r\n    public getDay() { return this.day }\r\n    public getBattleAnimals() { return this.battleAnimals }\r\n    public getPrepareAnimals() { return this.prepareAnimals }\r\n    public getFoods() { return this.foods }\r\n\r\n    public addAnimal(id: number) {\r\n        if (this.battleAnimals.length < 3) {\r\n            const animal = new AnimalObj().init(id, AnimalCamp.FRIENDLY, this.battleAnimals.length)\r\n            this.battleAnimals.push(animal)\r\n            this.emit(EventType.UPDATE_MY_BATTLE_AREA)\r\n        }\r\n    }\r\n\r\n    // 获取敌方动物\r\n    public getEnemyAnimals() {\r\n        if (this.enemyAnimals.length === 0) {\r\n            this.enemyAnimals = [\r\n                new AnimalObj().init(11001, AnimalCamp.ENEMY, 0),\r\n                new AnimalObj().init(11001, AnimalCamp.ENEMY, 1),\r\n                new AnimalObj().init(11001, AnimalCamp.ENEMY, 2),\r\n            ]\r\n        }\r\n        return this.enemyAnimals\r\n    }\r\n\r\n    public addFrameAnimation(cmpt: IFrameAnimation) {\r\n        if (!this.frameAnimations.has('uuid', cmpt.uuid)) {\r\n            this.frameAnimations.push(cmpt)\r\n        }\r\n    }\r\n\r\n    public removeFrameAnimation(uuid: string) {\r\n        this.frameAnimations.remove('uuid', uuid)\r\n    }\r\n\r\n    public update(dt: number) {\r\n        if (!this.runing) {\r\n            return\r\n        } else if (this.fspModel) {\r\n            this.fspModel.update(dt)\r\n        } else {\r\n            this.updateAnimationFrame(dt * 1000)\r\n        }\r\n    }\r\n\r\n    // 刷新动画帧 毫秒\r\n    public updateAnimationFrame(dt: number) {\r\n        for (let i = this.frameAnimations.length - 1; i >= 0; i--) {\r\n            const cmpt = this.frameAnimations[i]\r\n            if (cmpt.isValid) {\r\n                cmpt.updateFrame(dt)\r\n            } else {\r\n                this.frameAnimations.splice(i, 1)\r\n            }\r\n        }\r\n    }\r\n\r\n    //////////////////////////////////////////////////////////[ 战斗相关 ]///////////////////////////////////////////////////////////////////\r\n\r\n    public getFspModel() { return this.fspModel }\r\n\r\n    // 战斗开始\r\n    public battleBegin() {\r\n        const fighters = []\r\n        fighters.pushArr(this.battleAnimals.map(m => m.toFighter()))\r\n        fighters.pushArr(this.enemyAnimals.map(m => m.toFighter()))\r\n        this.fspModel = new FSPModel().init({\r\n            randSeed: ut.random(10000, 99999),\r\n            fighters: fighters,\r\n        })\r\n    }\r\n\r\n    // 战斗结束 本地\r\n    public battleEndByLocal() {\r\n        this.fspModel.stop()\r\n        this.fspModel = null\r\n    }\r\n\r\n    // 根据阵营删除动物 来至战斗\r\n    public removeAnimalByBattle(camp: number, uid: string) {\r\n        if (camp === 1) {\r\n            this.battleAnimals.remove('uid', uid)\r\n        } else {\r\n            this.enemyAnimals.remove('uid', uid)\r\n        }\r\n    }\r\n\r\n}"]}