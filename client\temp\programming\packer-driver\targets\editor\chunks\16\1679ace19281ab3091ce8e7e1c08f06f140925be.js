System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, ca, LoginState, NetEvent, gHelper, ecode, log, _dec, _class, _crd, LoginModel;

  function _reportPossibleCrUseOfca(extras) {
    _reporterNs.report("ca", "db://assets/scene/ca", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLoginState(extras) {
    _reporterNs.report("LoginState", "../../common/constant/Enums", _context.meta, extras);
  }

  function _reportPossibleCrUseOfNetEvent(extras) {
    _reporterNs.report("NetEvent", "../../common/event/NetEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfgHelper(extras) {
    _reporterNs.report("gHelper", "../../common/helper/GameHelper", _context.meta, extras);
  }

  function _reportPossibleCrUseOfNetworkModel(extras) {
    _reporterNs.report("NetworkModel", "../common/NetworkModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUserModel(extras) {
    _reporterNs.report("UserModel", "../common/UserModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfecode(extras) {
    _reporterNs.report("ecode", "../../common/constant/ECode", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameModel(extras) {
    _reporterNs.report("GameModel", "../game/GameModel", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      log = _cc.log;
    }, function (_unresolved_2) {
      ca = _unresolved_2.default;
    }, function (_unresolved_3) {
      LoginState = _unresolved_3.LoginState;
    }, function (_unresolved_4) {
      NetEvent = _unresolved_4.default;
    }, function (_unresolved_5) {
      gHelper = _unresolved_5.gHelper;
    }, function (_unresolved_6) {
      ecode = _unresolved_6.ecode;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6d121BKwdBHwLo5g5SlNgiD", "LoginModel", undefined);

      __checkObsolete__(['log']);

      /**
       * 登录模块
       */
      _export("default", LoginModel = (_dec = mc.addmodel('login'), _dec(_class = class LoginModel extends mc.BaseModel {
        constructor(...args) {
          super(...args);
          this.DEFAULT_MAX_RECONNECT_ATTEMPTS = 60;
          //最大重连次数
          this.net = null;
          this.user = null;
          this.game = null;
          this.initBaseAsset = false;
          //是否初始化基础资源
          this.initGameAsset = false;
          //是否初始化游戏资源
          this.reconnectAttempts = 0;
          this.reconnectionDelay = 5;
        }

        //重连间隔（秒）
        onCreate() {
          this.net = this.getModel('net');
          this.user = this.getModel('user');
          this.game = this.getModel('game');
        }

        isInitBaseAsset() {
          return this.initBaseAsset;
        }

        setInitBaseAsset(val) {
          this.initBaseAsset = val;
        }

        isInitGameAsset() {
          return this.initGameAsset;
        }

        setInitGameAsset(val) {
          this.initGameAsset = val;
        } // 服务器断开连接


        onDisconnect(err) {
          this.emit((_crd && NetEvent === void 0 ? (_reportPossibleCrUseOfNetEvent({
            error: Error()
          }), NetEvent) : NetEvent).NET_DISCONNECT, err);
        } // 连接服务器


        async connect() {
          this.net.offAll(); // 连接服务器

          const ok = await this.net.connect((_crd && gHelper === void 0 ? (_reportPossibleCrUseOfgHelper({
            error: Error()
          }), gHelper) : gHelper).getServerUrl());

          if (ok) {
            this.net.on('disconnect', this.onDisconnect, this);
          }

          return ok;
        } // 重新连接


        async reconnect() {
          this.reconnectAttempts = 0;
          this.reconnectionDelay = 2;
          let ok = false;

          while (this.reconnectAttempts < this.DEFAULT_MAX_RECONNECT_ATTEMPTS) {
            if (this.net.isKick()) {
              return false;
            }

            ok = await this.net.connect((_crd && gHelper === void 0 ? (_reportPossibleCrUseOfgHelper({
              error: Error()
            }), gHelper) : gHelper).getServerUrl());

            if (ok) {
              this.net.on('disconnect', this.onDisconnect, this);
              break;
            }

            await ut.wait(this.reconnectionDelay);
            this.reconnectAttempts += 1;
            this.reconnectionDelay = Math.min(5, this.reconnectionDelay + 1); //下一次久一点
          }

          return ok;
        } // 断开网络


        disconnect() {
          this.net.off('disconnect');
          this.net.close();
        } // 加载游客id


        async loadGuestId() {
          let id = storageMgr.loadString('guest_id');

          if (!id) {// id = await jsbHelper.getDeviceData('guest_id', 'ca_account')
          }

          return id || ut.UID();
        }

        saveGuestId(id) {
          storageMgr.saveString('guest_id', id); // jsbHelper.saveDeviceData('guest_id', id, 'ca_account')
        } // 获取大厅服务器是否需要排队


        async getLobbyServerIsNeedQueueUp() {
          var _res$data;

          const res = await this.net.post({
            url: (_crd && gHelper === void 0 ? (_reportPossibleCrUseOfgHelper({
              error: Error()
            }), gHelper) : gHelper).getHttpServerUrl() + '/getServerLoad'
          });
          return !!(res != null && (_res$data = res.data) != null && _res$data.lobbyFull);
        } // 尝试登录


        async tryLogin(accountToken) {
          accountToken = accountToken || storageMgr.loadString('account_token');

          if (!accountToken || accountToken === '0') {
            if (accountToken !== '0') {// taHelper.track('ta_tutorial', { tutorial_step: '0-1' }) //首个场景打开
            }

            return {
              state: (_crd && LoginState === void 0 ? (_reportPossibleCrUseOfLoginState({
                error: Error()
              }), LoginState) : LoginState).NOT_ACCOUNT_TOKEN
            };
          }

          const {
            err,
            data
          } = await this.net.request('lobby/HD_TryLogin', {
            accountToken,
            lang: mc.lang,
            platform: (_crd && gHelper === void 0 ? (_reportPossibleCrUseOfgHelper({
              error: Error()
            }), gHelper) : gHelper).getShopPlatform(),
            version: (_crd && ca === void 0 ? (_reportPossibleCrUseOfca({
              error: Error()
            }), ca) : ca).VERSION
          });

          if (err === (_crd && ecode === void 0 ? (_reportPossibleCrUseOfecode({
            error: Error()
          }), ecode) : ecode).NOT_ACCOUNT_TOKEN || err === (_crd && ecode === void 0 ? (_reportPossibleCrUseOfecode({
            error: Error()
          }), ecode) : ecode).TOKEN_INVALID) {
            //没有token或者无效 都重新登录
            return {
              state: (_crd && LoginState === void 0 ? (_reportPossibleCrUseOfLoginState({
                error: Error()
              }), LoginState) : LoginState).NOT_ACCOUNT_TOKEN
            };
          } else if (err === (_crd && ecode === void 0 ? (_reportPossibleCrUseOfecode({
            error: Error()
          }), ecode) : ecode).VERSION_TOOLOW) {
            //提示版本过低
            return {
              state: (_crd && LoginState === void 0 ? (_reportPossibleCrUseOfLoginState({
                error: Error()
              }), LoginState) : LoginState).VERSION_TOOLOW,
              data
            };
          } else if (err === (_crd && ecode === void 0 ? (_reportPossibleCrUseOfecode({
            error: Error()
          }), ecode) : ecode).CUR_LOBBY_FULL) {
            //当前大厅服满了 重新登陆
            await ut.wait(0.5);
            return this.tryLogin(accountToken);
          } else if (err === (_crd && ecode === void 0 ? (_reportPossibleCrUseOfecode({
            error: Error()
          }), ecode) : ecode).LOBBY_QUEUE_UP) {
            //大厅服都满了 需要排队
            const ok = await this.getLobbyServerIsNeedQueueUp();

            if (ok) {
              return {
                state: (_crd && LoginState === void 0 ? (_reportPossibleCrUseOfLoginState({
                  error: Error()
                }), LoginState) : LoginState).QUEUE_UP
              }; //满了 需要排队
            }

            return this.tryLogin(accountToken); //没满继续登陆
          } else if (err === (_crd && ecode === void 0 ? (_reportPossibleCrUseOfecode({
            error: Error()
          }), ecode) : ecode).BAN_ACCOUNT) {
            //被封禁了
            return {
              state: (_crd && LoginState === void 0 ? (_reportPossibleCrUseOfLoginState({
                error: Error()
              }), LoginState) : LoginState).BANACCOUNT_TIME,
              type: data.banAccountType,
              time: data.banAccountSurplusTime
            };
          } else if (err) {
            log('tryLogin err!', err);
            return {
              state: (_crd && LoginState === void 0 ? (_reportPossibleCrUseOfLoginState({
                error: Error()
              }), LoginState) : LoginState).FAILURE,
              err: err
            };
          }

          const info = data.user;
          this.user.init(info);

          if (data.gameBaseData) {
            this.game.initBaseData(data.gameBaseData);
          } // 这里大厅服 不再返回账号token 统一使用登陆服的token 重复使用


          storageMgr.saveString('account_token', data.accountToken || '0');
          return {
            state: (_crd && LoginState === void 0 ? (_reportPossibleCrUseOfLoginState({
              error: Error()
            }), LoginState) : LoginState).SUCCEED,
            data: {
              sid: info.sid
            }
          };
        } // 游客登录


        async guestLogin() {
          let guestId = ut.getBrowserParamByKey('id');

          if (!guestId) {
            guestId = await this.loadGuestId();
          }

          const ok = await this.reconnect();

          if (!ok) {
            return {
              state: (_crd && LoginState === void 0 ? (_reportPossibleCrUseOfLoginState({
                error: Error()
              }), LoginState) : LoginState).FAILURE,
              err: 'login.failed'
            };
          }

          const {
            err,
            data
          } = await this.net.request('login/HD_GuestLogin', {
            guestId,
            nickname: assetsMgr.lang('login.guest_nickname'),
            platform: (_crd && gHelper === void 0 ? (_reportPossibleCrUseOfgHelper({
              error: Error()
            }), gHelper) : gHelper).getShopPlatform()
          }, true);

          if (!err) {
            this.saveGuestId((data == null ? void 0 : data.guestId) || '');
          }

          return this.loginVerifyRet(err, data == null ? void 0 : data.accountToken);
        } // 返回登陆验证结果


        loginVerifyRet(err, accountToken) {
          if (err) {
            return {
              state: (_crd && LoginState === void 0 ? (_reportPossibleCrUseOfLoginState({
                error: Error()
              }), LoginState) : LoginState).FAILURE,
              err: err
            };
          }

          storageMgr.saveString('account_token', accountToken || '0'); //

          return {
            state: (_crd && LoginState === void 0 ? (_reportPossibleCrUseOfLoginState({
              error: Error()
            }), LoginState) : LoginState).SUCCEED,
            accountToken
          };
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1679ace19281ab3091ce8e7e1c08f06f140925be.js.map