package game

import (
	"fmt"
	"testing"
)

// 测试地图可视化
func TestMapVisualization(t *testing.T) {
	fmt.Println("=== 地图可视化测试 ===")

	// 生成一个5层地图
	mapData := GenerateMapData(5)

	// 可视化地图
	visualizeMap(mapData)

	// 验证无交叉
	fmt.Println("\n=== 路径交叉检测 ===")
	hasAnyCrossing := false
	for layerIndex := 0; layerIndex < len(mapData)-1; layerIndex++ {
		currentLayer := mapData[layerIndex]
		nextLayer := mapData[layerIndex+1]

		crossings := detectPathCrossings(currentLayer, nextLayer)
		if len(crossings) > 0 {
			hasAnyCrossing = true
			fmt.Printf("第%d层到第%d层发现%d个交叉\n", layerIndex+1, layerIndex+2, len(crossings))
		} else {
			fmt.Printf("第%d层到第%d层: ✓ 无交叉\n", layerIndex+1, layerIndex+2)
		}
	}

	if !hasAnyCrossing {
		fmt.Println("\n🎉 所有路径都没有交叉！")
	}
}

// 可视化地图结构
func visualizeMap(mapData [][]*MapNode) {
	fmt.Println("\n=== 地图结构可视化 ===")

	for layerIndex, layer := range mapData {
		fmt.Printf("\n第%d层 (%d个节点):\n", layerIndex+1, len(layer))

		// 打印节点信息
		for i, node := range layer {
			nodeTypeName := getNodeTypeName(node.Type)
			fmt.Printf("  [%d] %s", i, nodeTypeName)
			if len(node.Children) > 0 {
				fmt.Printf(" -> %v", node.Children)
			}
			fmt.Println()
		}

		// 如果不是最后一层，打印连接图
		if layerIndex < len(mapData)-1 {
			fmt.Println()
			printConnectionDiagram(layer, mapData[layerIndex+1])
		}
	}
}

// 打印连接图
func printConnectionDiagram(parentLayer []*MapNode, childLayer []*MapNode) {
	parentCount := len(parentLayer)
	childCount := len(childLayer)

	// 打印父节点行
	parentLine := "  "
	for i := 0; i < parentCount; i++ {
		if i > 0 {
			parentLine += "    "
		}
		parentLine += fmt.Sprintf("[%d]", i)
	}
	fmt.Println(parentLine)

	// 打印连接线
	connectionLines := make([]string, 3) // 3行连接线
	for i := range connectionLines {
		connectionLines[i] = "  "
	}

	// 为每个父节点绘制连接线
	for parentIndex, parent := range parentLayer {
		parentPos := parentIndex * 7 // 每个节点占7个字符位置

		for _, childIndex := range parent.Children {
			childPos := int(childIndex) * 7

			// 绘制垂直线
			if parentPos < len(connectionLines[0]) {
				connectionLines[0] = setCharAt(connectionLines[0], parentPos+1, '|')
			}

			// 绘制水平线
			startPos := min(parentPos+1, childPos+1)
			endPos := max(parentPos+1, childPos+1)
			for pos := startPos; pos <= endPos; pos++ {
				if pos < len(connectionLines[1]) {
					connectionLines[1] = setCharAt(connectionLines[1], pos, '-')
				}
			}

			// 绘制到子节点的垂直线
			if childPos+1 < len(connectionLines[2]) {
				connectionLines[2] = setCharAt(connectionLines[2], childPos+1, '|')
			}
		}
	}

	// 打印连接线
	for _, line := range connectionLines {
		fmt.Println(line)
	}

	// 打印子节点行
	childLine := "  "
	for i := 0; i < childCount; i++ {
		if i > 0 {
			childLine += "    "
		}
		childLine += fmt.Sprintf("[%d]", i)
	}
	fmt.Println(childLine)
}

// 辅助函数：在字符串指定位置设置字符
func setCharAt(s string, index int, char rune) string {
	// 确保字符串足够长
	for len(s) <= index {
		s += " "
	}

	runes := []rune(s)
	runes[index] = char
	return string(runes)
}

// 辅助函数：获取最小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// 辅助函数：获取最大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// 获取节点类型名称
func getNodeTypeName(nodeType int32) string {
	switch nodeType {
	case MAP_NODE_TYPE_BATTLE:
		return "战斗"
	case MAP_NODE_TYPE_SHOP:
		return "商店"
	case MAP_NODE_TYPE_EVENT:
		return "事件"
	case MAP_NODE_TYPE_TREASURE:
		return "宝箱"
	case MAP_NODE_TYPE_PLAYER:
		return "玩家"
	default:
		return "未知"
	}
}

// 测试不同规模的地图
func TestDifferentMapSizes(t *testing.T) {
	fmt.Println("\n=== 不同规模地图测试 ===")

	sizes := []int32{3, 5, 8, 10}

	for _, size := range sizes {
		fmt.Printf("\n--- %d层地图 ---\n", size)
		mapData := GenerateMapData(size)

		// 统计信息
		totalNodes := 0
		for i, layer := range mapData {
			totalNodes += len(layer)
			fmt.Printf("第%d层: %d个节点\n", i+1, len(layer))
		}
		fmt.Printf("总节点数: %d\n", totalNodes)

		// 检查交叉
		crossingCount := 0
		for layerIndex := 0; layerIndex < len(mapData)-1; layerIndex++ {
			currentLayer := mapData[layerIndex]
			nextLayer := mapData[layerIndex+1]
			crossings := detectPathCrossings(currentLayer, nextLayer)
			crossingCount += len(crossings)
		}

		if crossingCount == 0 {
			fmt.Printf("✓ 无路径交叉\n")
		} else {
			fmt.Printf("✗ 发现%d个路径交叉\n", crossingCount)
			t.Errorf("%d层地图存在路径交叉", size)
		}
	}
}

// 测试极端情况
func TestEdgeCases(t *testing.T) {
	fmt.Println("\n=== 极端情况测试 ===")

	testCases := []struct {
		name        string
		parentCount int
		childCount  int
	}{
		{"1父1子", 1, 1},
		{"1父5子", 1, 5},
		{"5父1子", 5, 1},
		{"10父2子", 10, 2},
		{"2父10子", 2, 10},
	}

	for _, tc := range testCases {
		fmt.Printf("\n--- %s ---\n", tc.name)

		// 创建测试层
		parentLayer := make([]*MapNode, tc.parentCount)
		childLayer := make([]*MapNode, tc.childCount)

		for i := 0; i < tc.parentCount; i++ {
			parentLayer[i] = &MapNode{
				Type:     MAP_NODE_TYPE_BATTLE,
				Children: []int32{},
			}
		}

		for i := 0; i < tc.childCount; i++ {
			childLayer[i] = &MapNode{
				Type:     MAP_NODE_TYPE_BATTLE,
				Children: []int32{},
			}
		}

		// 分配子节点
		assignChildrenByStrictRegions(parentLayer, childLayer)

		// 打印结果
		for i, parent := range parentLayer {
			fmt.Printf("父节点%d -> %v\n", i, parent.Children)
		}

		// 检查交叉
		crossings := detectPathCrossings(parentLayer, childLayer)
		if len(crossings) > 0 {
			t.Errorf("%s 存在路径交叉: %v", tc.name, crossings)
		} else {
			fmt.Printf("✓ 无路径交叉\n")
		}

		// 检查每个父节点是否都有子节点
		for i, parent := range parentLayer {
			if len(parent.Children) == 0 {
				t.Errorf("%s 父节点%d没有子节点", tc.name, i)
			}
		}
	}
}
