
declare module "cc" {

    type SwihNodeCallback = (it: Node) => any;
    type SwihToggleCallback = (it: Toggle) => any;

    export namespace math {
        interface Vec2 {
            ID(): string;
            Join(separator?: string): string;
            toVec3(): Vec3;
            newVec3(): Vec3;
            toJson(): Point;
            FlipX(): Vec2;
            floor(): Vec2;
            ceil(): Vec2;
            Length(): number;
            SelfMul(): number;
            toIndex(size: Vec2): number;
        }

        interface Vec3 {
            Join(separator?: string): string;
            toVec2(): Vec2;
            newVec2(): Vec2;
        }
    }

    interface Node {
        Data: any;
        opacity: number;
        zIndex: number;
        Width: number;
        Height: number;
        transform: UITransform;
        readonly ChildrenCount: number;
        _hitTest(vec: Vec2 | Vec3): boolean;
        stopAllActions(): void;
        Child(name: string | number): Node;
        Child(name: string | number, className: string): any;
        Child<T extends Component>(name: string | number, type: { prototype: T }): T;
        FindChild(name: string | number): Node;
        FindChild(name: string | number, className: string): any;
        FindChild<T extends Component>(name: string | number, type: { prototype: T }): T;
        Component<T extends Component>(type: { prototype: T }): T;
        Component(className: string): any;
        Items<T>(list: T[] | number, item: Node | Prefab, setItemData: (it: Node, data: T, i: number) => void, target?: any): void;
        Items<T>(list: T[] | number, setItemData: (it: Node, data: T, i: number) => void, target?: any): void;
        Items<T>(list: T[] | number): void;
        AddItem(item: Node | Prefab, setItemData: (it: Node, i: number) => void, target?: any): void;
        AddItem(setItemData: (it: Node, i: number) => void, target?: any): void;
        AddItem(): { it: Node, i: number };
        Swih(val: string | number | SwihNodeCallback, reverse?: boolean, ignores?: string): Node[];
        adaptScale(targetSize: Size, selfSize?: Size, maxScale?: number): void;
        adaptSize(targetSize: Size, selfSize?: Size, maxScale?: number): void;
        getActive(): boolean;
        setActive(val: boolean): boolean;
        Color(val: string | Color): Node;
        SetSwallowTouches(val: boolean): void;
        IsSwallowTouches(): boolean;
        setLocaleKey(key: string, ...params: any[]): Label | RichTextComponent;
    }

    interface Component {
        transform: UITransform;
        getActive(): boolean;
        setActive(val: boolean): boolean;
        getPosition(out?: Vec3): Vec3;
        Child(name: string | number): Node;
        Child(name: string | number, className: string): any;
        Child<T extends Component>(name: string | number, type: { prototype: T }): T;
        FindChild(name: string | number): Node;
        FindChild(name: string | number, className: string): any;
        FindChild<T extends Component>(name: string | number, type: { prototype: T }): T;
        Component<T extends Component>(type: { prototype: T }): T;
        Component(className: string): any;
        Color(val: string | Color): Component;
        getColor(): Color;
    }

    interface Animation {
        playAsync(name?: string, startTime?: number): Promise<void>;
        playToFinished(callback: Function, name?: string): AnimationState;
        setCompleteListener(callback: Function): void;
        reset(name?: string): void;
    }

    // interface Skeleton {
    //     play(name: string): void;
    //     playAsync(name: string): Promise<void>;
    // }

    interface Label {
        setLocaleKey(key: string, ...params: any[]): void;
        Color(val: string | Color): Label;
    }

    interface RichTextComponent {
        setLocaleKey(key: string, ...params: any[]): void;
        Color(val: string | Color): RichTextComponent;
    }

    interface Sprite {
        setLocaleKey(key: string): void;
        Color(val: string | Color): Sprite;
    }

    interface ScrollView {
        Items<T>(list: T[] | number, item: Node | Prefab, setItemData: (it: Node, data: T, i: number) => void, target?: any): void;
        Items<T>(list: T[] | number, setItemData?: (it: Node, data: T, i: number) => void, target?: any): void;
        Items<T>(list: T[] | number): void;
        AddItem(setItemData: (it: Node, i: number) => void, target?: any): void;
        Find(predicate: (value: Node, index: number, obj: Node[]) => unknown, thisArg?: any): Node;
        IsEmpty(): boolean;
        List(len: number, setItemData?: (it: Node, i: number) => void, target?: any): void;
        GetItemNode(): Node;
        SelectItemToCentre(index: number): void;
    }

    interface ToggleContainer {
        Swih(val: string | number | SwihToggleCallback): Toggle[];
        Tabs(val: string | number | SwihToggleCallback): Toggle;
    }

    interface EditBox {
        setPlaceholder(key: string, font: string, ...params: any[]): void;
    }

    interface Button {
        setInteractableAndMF(val: boolean): void;
    }
}