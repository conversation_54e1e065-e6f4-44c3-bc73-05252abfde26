System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, error, Layout, misc, Node, Prefab, ScrollView, ScrollViewEx, ScrollViewPlus, _crd;

  function setItemData(it, data, i, cb, target) {
    it.active = true;
    it.opacity = 255;

    if (!cb) {
      return;
    } else if (target) {
      cb.call(target, it, data, i);
    } else {
      cb(it, data, i);
    }
  } // 查找content的子节点


  function _reportPossibleCrUseOfScrollViewEx(extras) {
    _reporterNs.report("ScrollViewEx", "../component/ScrollViewEx", _context.meta, extras);
  }

  function _reportPossibleCrUseOfScrollViewPlus(extras) {
    _reporterNs.report("ScrollViewPlus", "../component/ScrollViewPlus", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      error = _cc.error;
      Layout = _cc.Layout;
      misc = _cc.misc;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
      ScrollView = _cc.ScrollView;
    }, function (_unresolved_2) {
      ScrollViewEx = _unresolved_2.default;
    }, function (_unresolved_3) {
      ScrollViewPlus = _unresolved_3.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ccd865NbQ1ADY4HhxKJM0PV", "ExtendScrollView", undefined);
      /**
       * ScrollView 扩展方法
       */


      __checkObsolete__(['error', 'Layout', 'misc', 'Node', 'Prefab', 'ScrollView']);

      // 填充列表
      ScrollView.prototype.Items = function (list, prefab, cb, target) {
        let i = 0,
            childs = this.content.children,
            item = childs[0];
        let count = 0;

        if (typeof list === 'number') {
          count = list;
          list = null;
        } else {
          count = list.length;
        }

        if (typeof prefab === 'function') {
          target = cb;
          cb = prefab;
        } else if (prefab instanceof Node || prefab instanceof Prefab) {
          item = prefab;
        }

        if (!item) {
          return error('必须满足content中有一个可拷贝的节点');
        }

        const plus = this.Component(_crd && ScrollViewPlus === void 0 ? (_reportPossibleCrUseOfScrollViewPlus({
          error: Error()
        }), ScrollViewPlus) : ScrollViewPlus);
        plus == null || plus.reset();

        if (plus != null && plus.isFrameRender) {
          return plus.items(list, item, cb, target);
        }

        for (let l = this.content.ChildrenCount; i < l; i++) {
          const it = childs[i];

          if (i < count) {
            setItemData(it, list && list[i], i, cb, target);
          } else {
            it.Data = null;
            it.active = false;
          }
        }

        for (; i < count; i++) {
          setItemData(mc.instantiate(item, this.content), list && list[i], i, cb, target);
        } // 刷新一下显示


        plus == null || plus.updateNodeShow();
      }; // 添加一个


      ScrollView.prototype.AddItem = function (cb, target) {
        let i = this.content.children.findIndex(m => !m.active);
        let it = null;

        if (i !== -1) {
          it = this.content.children[i];
        } else {
          i = this.content.ChildrenCount;
          it = mc.instantiate(this.content.children[0], this.content);
        }

        it.active = true;
        setItemData(it, i, undefined, cb, target);
      };

      ScrollView.prototype.Find = function (predicate, thisArg) {
        return this.content.children.find(predicate, thisArg);
      }; //


      ScrollView.prototype.IsEmpty = function () {
        return !this.content.children.some(m => m.active);
      }; // list填充


      ScrollView.prototype.List = function (len, cb, target) {
        const ex = this.Component(_crd && ScrollViewEx === void 0 ? (_reportPossibleCrUseOfScrollViewEx({
          error: Error()
        }), ScrollViewEx) : ScrollViewEx);

        if (ex) {
          return ex.list(len, cb, target);
        } else {
          error('List error, not ScrollViewEx!');
        }
      }; //


      ScrollView.prototype.GetItemNode = function () {
        var _this$Component;

        return ((_this$Component = this.Component(_crd && ScrollViewEx === void 0 ? (_reportPossibleCrUseOfScrollViewEx({
          error: Error()
        }), ScrollViewEx) : ScrollViewEx)) == null ? void 0 : _this$Component.getItemNode()) || this.content.children[0];
      }; // 将选中的移动到中间


      ScrollView.prototype.SelectItemToCentre = function (index) {
        this.stopAutoScroll();

        if (index !== -1) {
          const lay = this.content.Component(Layout);
          lay.updateLayout();
          const width = this.content.children[0].width;
          const tx = (width + lay.spacingX) * index + width * 0.5 + lay.paddingLeft; //当前位置

          const pw = this.content.parent.width;
          const cx = pw * 0.5; //中间位置

          this.content.x = misc.clampf(cx - tx, Math.min(0, pw - this.content.width), 0);
        } else {
          this.scrollToLeft();
        }
      };

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=51e2a83801a55453c04df240fc1f30d63171d1fa.js.map