{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts"], "names": ["log", "ca", "LoginState", "NetEvent", "g<PERSON>elper", "ecode", "LoginModel", "mc", "addmodel", "BaseModel", "DEFAULT_MAX_RECONNECT_ATTEMPTS", "net", "user", "game", "initBaseAsset", "initGameAsset", "reconnectAttempts", "reconnectionDelay", "onCreate", "getModel", "isInitBaseAsset", "setInitBaseAsset", "val", "isInitGameAsset", "setInitGameAsset", "onDisconnect", "err", "emit", "NET_DISCONNECT", "connect", "offAll", "ok", "getServerUrl", "on", "reconnect", "isKick", "ut", "wait", "Math", "min", "disconnect", "off", "close", "loadGuestId", "id", "storageMgr", "loadString", "UID", "saveGuestId", "saveString", "getLobbyServerIsNeedQueueUp", "res", "post", "url", "getHttpServerUrl", "data", "lobbyFull", "<PERSON><PERSON><PERSON>in", "accountToken", "state", "NOT_ACCOUNT_TOKEN", "request", "lang", "platform", "getShopPlatform", "version", "VERSION", "TOKEN_INVALID", "VERSION_TOOLOW", "CUR_LOBBY_FULL", "LOBBY_QUEUE_UP", "QUEUE_UP", "BAN_ACCOUNT", "BANACCOUNT_TIME", "type", "banAccountType", "time", "banAccountSurplusTime", "FAILURE", "info", "init", "gameBaseData", "initBaseData", "SUCCEED", "sid", "guest<PERSON><PERSON><PERSON>", "guestId", "getBrowserParamByKey", "nickname", "assetsMgr", "loginVerifyRet"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOSA,MAAAA,G,OAAAA,G;;AAPFC,MAAAA,E;;AACEC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,Q;;AACEC,MAAAA,O,iBAAAA,O;;AAGAC,MAAAA,K,iBAAAA,K;;;;;;;;;AAIT;AACA;AACA;yBAEqBC,U,WADpBC,EAAE,CAACC,QAAH,CAAY,OAAZ,C,gBAAD,MACqBF,UADrB,SACwCC,EAAE,CAACE,SAD3C,CACqD;AAAA;AAAA;AAAA,eAEhCC,8BAFgC,GAEC,EAFD;AAEI;AAFJ,eAIzCC,GAJyC,GAIrB,IAJqB;AAAA,eAKzCC,IALyC,GAKvB,IALuB;AAAA,eAMzCC,IANyC,GAMvB,IANuB;AAAA,eAQzCC,aARyC,GAQhB,KARgB;AAQV;AARU,eASzCC,aATyC,GAShB,KATgB;AASV;AATU,eAUzCC,iBAVyC,GAUb,CAVa;AAAA,eAWzCC,iBAXyC,GAWb,CAXa;AAAA;;AAWX;AAE/BC,QAAAA,QAAQ,GAAG;AACd,eAAKP,GAAL,GAAW,KAAKQ,QAAL,CAAc,KAAd,CAAX;AACA,eAAKP,IAAL,GAAY,KAAKO,QAAL,CAAc,MAAd,CAAZ;AACA,eAAKN,IAAL,GAAY,KAAKM,QAAL,CAAc,MAAd,CAAZ;AACH;;AAEMC,QAAAA,eAAe,GAAG;AAAE,iBAAO,KAAKN,aAAZ;AAA2B;;AAC/CO,QAAAA,gBAAgB,CAACC,GAAD,EAAe;AAAE,eAAKR,aAAL,GAAqBQ,GAArB;AAA0B;;AAC3DC,QAAAA,eAAe,GAAG;AAAE,iBAAO,KAAKR,aAAZ;AAA2B;;AAC/CS,QAAAA,gBAAgB,CAACF,GAAD,EAAe;AAAE,eAAKP,aAAL,GAAqBO,GAArB;AAA0B,SAtBjB,CAwBjD;;;AACQG,QAAAA,YAAY,CAACC,GAAD,EAAW;AAC3B,eAAKC,IAAL,CAAU;AAAA;AAAA,oCAASC,cAAnB,EAAmCF,GAAnC;AACH,SA3BgD,CA6BjD;;;AACoB,cAAPG,OAAO,GAAG;AACnB,eAAKlB,GAAL,CAASmB,MAAT,GADmB,CAEnB;;AACA,gBAAMC,EAAE,GAAG,MAAM,KAAKpB,GAAL,CAASkB,OAAT,CAAiB;AAAA;AAAA,kCAAQG,YAAR,EAAjB,CAAjB;;AACA,cAAID,EAAJ,EAAQ;AACJ,iBAAKpB,GAAL,CAASsB,EAAT,CAAY,YAAZ,EAA0B,KAAKR,YAA/B,EAA6C,IAA7C;AACH;;AACD,iBAAOM,EAAP;AACH,SAtCgD,CAwCjD;;;AACsB,cAATG,SAAS,GAAG;AACrB,eAAKlB,iBAAL,GAAyB,CAAzB;AACA,eAAKC,iBAAL,GAAyB,CAAzB;AACA,cAAIc,EAAE,GAAG,KAAT;;AACA,iBAAO,KAAKf,iBAAL,GAAyB,KAAKN,8BAArC,EAAqE;AACjE,gBAAI,KAAKC,GAAL,CAASwB,MAAT,EAAJ,EAAuB;AACnB,qBAAO,KAAP;AACH;;AACDJ,YAAAA,EAAE,GAAG,MAAM,KAAKpB,GAAL,CAASkB,OAAT,CAAiB;AAAA;AAAA,oCAAQG,YAAR,EAAjB,CAAX;;AACA,gBAAID,EAAJ,EAAQ;AACJ,mBAAKpB,GAAL,CAASsB,EAAT,CAAY,YAAZ,EAA0B,KAAKR,YAA/B,EAA6C,IAA7C;AACA;AACH;;AACD,kBAAMW,EAAE,CAACC,IAAH,CAAQ,KAAKpB,iBAAb,CAAN;AACA,iBAAKD,iBAAL,IAA0B,CAA1B;AACA,iBAAKC,iBAAL,GAAyBqB,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,KAAKtB,iBAAL,GAAyB,CAArC,CAAzB,CAXiE,CAWA;AACpE;;AACD,iBAAOc,EAAP;AACH,SA3DgD,CA6DjD;;;AACOS,QAAAA,UAAU,GAAG;AAChB,eAAK7B,GAAL,CAAS8B,GAAT,CAAa,YAAb;AACA,eAAK9B,GAAL,CAAS+B,KAAT;AACH,SAjEgD,CAmEjD;;;AACyB,cAAXC,WAAW,GAAG;AACxB,cAAIC,EAAE,GAAGC,UAAU,CAACC,UAAX,CAAsB,UAAtB,CAAT;;AACA,cAAI,CAACF,EAAL,EAAS,CACL;AACH;;AACD,iBAAOA,EAAE,IAAIR,EAAE,CAACW,GAAH,EAAb;AACH;;AAEOC,QAAAA,WAAW,CAACJ,EAAD,EAAa;AAC5BC,UAAAA,UAAU,CAACI,UAAX,CAAsB,UAAtB,EAAkCL,EAAlC,EAD4B,CAE5B;AACH,SA/EgD,CAiFjD;;;AACwC,cAA3BM,2BAA2B,GAAG;AAAA;;AACvC,gBAAMC,GAAG,GAAG,MAAM,KAAKxC,GAAL,CAASyC,IAAT,CAAc;AAAEC,YAAAA,GAAG,EAAE;AAAA;AAAA,oCAAQC,gBAAR,KAA6B;AAApC,WAAd,CAAlB;AACA,iBAAO,CAAC,EAACH,GAAD,yBAACA,GAAG,CAAEI,IAAN,aAAC,UAAWC,SAAZ,CAAR;AACH,SArFgD,CAuFjD;;;AACqB,cAARC,QAAQ,CAACC,YAAD,EAAsC;AACvDA,UAAAA,YAAY,GAAGA,YAAY,IAAIb,UAAU,CAACC,UAAX,CAAsB,eAAtB,CAA/B;;AACA,cAAI,CAACY,YAAD,IAAiBA,YAAY,KAAK,GAAtC,EAA2C;AACvC,gBAAIA,YAAY,KAAK,GAArB,EAA0B,CACtB;AACH;;AACD,mBAAO;AAAEC,cAAAA,KAAK,EAAE;AAAA;AAAA,4CAAWC;AAApB,aAAP;AACH;;AACD,gBAAM;AAAElC,YAAAA,GAAF;AAAO6B,YAAAA;AAAP,cAAgB,MAAM,KAAK5C,GAAL,CAASkD,OAAT,CAAiB,mBAAjB,EAAsC;AAC9DH,YAAAA,YAD8D;AAE9DI,YAAAA,IAAI,EAAEvD,EAAE,CAACuD,IAFqD;AAG9DC,YAAAA,QAAQ,EAAE;AAAA;AAAA,oCAAQC,eAAR,EAHoD;AAI9DC,YAAAA,OAAO,EAAE;AAAA;AAAA,0BAAGC;AAJkD,WAAtC,CAA5B;;AAMA,cAAIxC,GAAG,KAAK;AAAA;AAAA,8BAAMkC,iBAAd,IAAmClC,GAAG,KAAK;AAAA;AAAA,8BAAMyC,aAArD,EAAoE;AAAE;AAClE,mBAAO;AAAER,cAAAA,KAAK,EAAE;AAAA;AAAA,4CAAWC;AAApB,aAAP;AACH,WAFD,MAEO,IAAIlC,GAAG,KAAK;AAAA;AAAA,8BAAM0C,cAAlB,EAAkC;AAAE;AACvC,mBAAO;AAAET,cAAAA,KAAK,EAAE;AAAA;AAAA,4CAAWS,cAApB;AAAoCb,cAAAA;AAApC,aAAP;AACH,WAFM,MAEA,IAAI7B,GAAG,KAAK;AAAA;AAAA,8BAAM2C,cAAlB,EAAkC;AAAE;AACvC,kBAAMjC,EAAE,CAACC,IAAH,CAAQ,GAAR,CAAN;AACA,mBAAO,KAAKoB,QAAL,CAAcC,YAAd,CAAP;AACH,WAHM,MAGA,IAAIhC,GAAG,KAAK;AAAA;AAAA,8BAAM4C,cAAlB,EAAkC;AAAE;AACvC,kBAAMvC,EAAE,GAAG,MAAM,KAAKmB,2BAAL,EAAjB;;AACA,gBAAInB,EAAJ,EAAQ;AACJ,qBAAO;AAAE4B,gBAAAA,KAAK,EAAE;AAAA;AAAA,8CAAWY;AAApB,eAAP,CADI,CACkC;AACzC;;AACD,mBAAO,KAAKd,QAAL,CAAcC,YAAd,CAAP,CALqC,CAKF;AACtC,WANM,MAMA,IAAIhC,GAAG,KAAK;AAAA;AAAA,8BAAM8C,WAAlB,EAA+B;AAAE;AACpC,mBAAO;AAAEb,cAAAA,KAAK,EAAE;AAAA;AAAA,4CAAWc,eAApB;AAAqCC,cAAAA,IAAI,EAAEnB,IAAI,CAACoB,cAAhD;AAAgEC,cAAAA,IAAI,EAAErB,IAAI,CAACsB;AAA3E,aAAP;AACH,WAFM,MAEA,IAAInD,GAAJ,EAAS;AACZ1B,YAAAA,GAAG,CAAC,eAAD,EAAkB0B,GAAlB,CAAH;AACA,mBAAO;AAAEiC,cAAAA,KAAK,EAAE;AAAA;AAAA,4CAAWmB,OAApB;AAA6BpD,cAAAA,GAAG,EAAEA;AAAlC,aAAP;AACH;;AACD,gBAAMqD,IAAI,GAAGxB,IAAI,CAAC3C,IAAlB;AACA,eAAKA,IAAL,CAAUoE,IAAV,CAAeD,IAAf;;AACA,cAAIxB,IAAI,CAAC0B,YAAT,EAAuB;AACnB,iBAAKpE,IAAL,CAAUqE,YAAV,CAAuB3B,IAAI,CAAC0B,YAA5B;AACH,WArCsD,CAsCvD;;;AACApC,UAAAA,UAAU,CAACI,UAAX,CAAsB,eAAtB,EAAuCM,IAAI,CAACG,YAAL,IAAqB,GAA5D;AACA,iBAAO;AAAEC,YAAAA,KAAK,EAAE;AAAA;AAAA,0CAAWwB,OAApB;AAA6B5B,YAAAA,IAAI,EAAE;AAAE6B,cAAAA,GAAG,EAAEL,IAAI,CAACK;AAAZ;AAAnC,WAAP;AACH,SAjIgD,CAmIjD;;;AACuB,cAAVC,UAAU,GAAG;AACtB,cAAIC,OAAO,GAAGlD,EAAE,CAACmD,oBAAH,CAAwB,IAAxB,CAAd;;AACA,cAAI,CAACD,OAAL,EAAc;AACVA,YAAAA,OAAO,GAAG,MAAM,KAAK3C,WAAL,EAAhB;AACH;;AACD,gBAAMZ,EAAE,GAAG,MAAM,KAAKG,SAAL,EAAjB;;AACA,cAAI,CAACH,EAAL,EAAS;AACL,mBAAO;AAAE4B,cAAAA,KAAK,EAAE;AAAA;AAAA,4CAAWmB,OAApB;AAA6BpD,cAAAA,GAAG,EAAE;AAAlC,aAAP;AACH;;AACD,gBAAM;AAAEA,YAAAA,GAAF;AAAO6B,YAAAA;AAAP,cAAgB,MAAM,KAAK5C,GAAL,CAASkD,OAAT,CAAiB,qBAAjB,EAAwC;AAChEyB,YAAAA,OADgE;AAEhEE,YAAAA,QAAQ,EAAEC,SAAS,CAAC3B,IAAV,CAAe,sBAAf,CAFsD;AAGhEC,YAAAA,QAAQ,EAAE;AAAA;AAAA,oCAAQC,eAAR;AAHsD,WAAxC,EAIzB,IAJyB,CAA5B;;AAKA,cAAI,CAACtC,GAAL,EAAU;AACN,iBAAKsB,WAAL,CAAiB,CAAAO,IAAI,QAAJ,YAAAA,IAAI,CAAE+B,OAAN,KAAiB,EAAlC;AACH;;AACD,iBAAO,KAAKI,cAAL,CAAoBhE,GAApB,EAAyB6B,IAAzB,oBAAyBA,IAAI,CAAEG,YAA/B,CAAP;AACH,SAtJgD,CAwJjD;;;AACQgC,QAAAA,cAAc,CAAChE,GAAD,EAAcgC,YAAd,EAAyC;AAC3D,cAAIhC,GAAJ,EAAS;AACL,mBAAO;AAAEiC,cAAAA,KAAK,EAAE;AAAA;AAAA,4CAAWmB,OAApB;AAA6BpD,cAAAA,GAAG,EAAEA;AAAlC,aAAP;AACH;;AACDmB,UAAAA,UAAU,CAACI,UAAX,CAAsB,eAAtB,EAAuCS,YAAY,IAAI,GAAvD,EAJ2D,CAK3D;;AACA,iBAAO;AAAEC,YAAAA,KAAK,EAAE;AAAA;AAAA,0CAAWwB,OAApB;AAA6BzB,YAAAA;AAA7B,WAAP;AACH;;AAhKgD,O", "sourcesContent": ["import ca from \"db://assets/scene/ca\"\r\nimport { LoginState } from \"../../common/constant/Enums\"\r\nimport NetEvent from \"../../common/event/NetEvent\"\r\nimport { gHelper } from \"../../common/helper/GameHelper\"\r\nimport NetworkModel from \"../common/NetworkModel\"\r\nimport UserModel from \"../common/UserModel\"\r\nimport { ecode } from \"../../common/constant/ECode\"\r\nimport { log } from \"cc\"\r\nimport GameModel from \"../game/GameModel\"\r\n\r\n/**\r\n * 登录模块\r\n */\r\*************('login')\r\nexport default class LoginModel extends mc.BaseModel {\r\n\r\n    private readonly DEFAULT_MAX_RECONNECT_ATTEMPTS = 60 //最大重连次数\r\n\r\n    private net: NetworkModel = null\r\n    private user: UserModel = null\r\n    private game: GameModel = null\r\n\r\n    private initBaseAsset: boolean = false //是否初始化基础资源\r\n    private initGameAsset: boolean = false //是否初始化游戏资源\r\n    private reconnectAttempts: number = 0\r\n    private reconnectionDelay: number = 5 //重连间隔（秒）\r\n\r\n    public onCreate() {\r\n        this.net = this.getModel('net')\r\n        this.user = this.getModel('user')\r\n        this.game = this.getModel('game')\r\n    }\r\n\r\n    public isInitBaseAsset() { return this.initBaseAsset }\r\n    public setInitBaseAsset(val: boolean) { this.initBaseAsset = val }\r\n    public isInitGameAsset() { return this.initGameAsset }\r\n    public setInitGameAsset(val: boolean) { this.initGameAsset = val }\r\n\r\n    // 服务器断开连接\r\n    private onDisconnect(err: any) {\r\n        this.emit(NetEvent.NET_DISCONNECT, err)\r\n    }\r\n\r\n    // 连接服务器\r\n    public async connect() {\r\n        this.net.offAll()\r\n        // 连接服务器\r\n        const ok = await this.net.connect(gHelper.getServerUrl())\r\n        if (ok) {\r\n            this.net.on('disconnect', this.onDisconnect, this)\r\n        }\r\n        return ok\r\n    }\r\n\r\n    // 重新连接\r\n    public async reconnect() {\r\n        this.reconnectAttempts = 0\r\n        this.reconnectionDelay = 2\r\n        let ok = false\r\n        while (this.reconnectAttempts < this.DEFAULT_MAX_RECONNECT_ATTEMPTS) {\r\n            if (this.net.isKick()) {\r\n                return false\r\n            }\r\n            ok = await this.net.connect(gHelper.getServerUrl())\r\n            if (ok) {\r\n                this.net.on('disconnect', this.onDisconnect, this)\r\n                break\r\n            }\r\n            await ut.wait(this.reconnectionDelay)\r\n            this.reconnectAttempts += 1\r\n            this.reconnectionDelay = Math.min(5, this.reconnectionDelay + 1) //下一次久一点\r\n        }\r\n        return ok\r\n    }\r\n\r\n    // 断开网络\r\n    public disconnect() {\r\n        this.net.off('disconnect')\r\n        this.net.close()\r\n    }\r\n\r\n    // 加载游客id\r\n    private async loadGuestId() {\r\n        let id = storageMgr.loadString('guest_id')\r\n        if (!id) {\r\n            // id = await jsbHelper.getDeviceData('guest_id', 'ca_account')\r\n        }\r\n        return id || ut.UID()\r\n    }\r\n\r\n    private saveGuestId(id: string) {\r\n        storageMgr.saveString('guest_id', id)\r\n        // jsbHelper.saveDeviceData('guest_id', id, 'ca_account')\r\n    }\r\n\r\n    // 获取大厅服务器是否需要排队\r\n    public async getLobbyServerIsNeedQueueUp() {\r\n        const res = await this.net.post({ url: gHelper.getHttpServerUrl() + '/getServerLoad' })\r\n        return !!res?.data?.lobbyFull\r\n    }\r\n\r\n    // 尝试登录\r\n    public async tryLogin(accountToken?: string): Promise<any> {\r\n        accountToken = accountToken || storageMgr.loadString('account_token')\r\n        if (!accountToken || accountToken === '0') {\r\n            if (accountToken !== '0') {\r\n                // taHelper.track('ta_tutorial', { tutorial_step: '0-1' }) //首个场景打开\r\n            }\r\n            return { state: LoginState.NOT_ACCOUNT_TOKEN }\r\n        }\r\n        const { err, data } = await this.net.request('lobby/HD_TryLogin', {\r\n            accountToken,\r\n            lang: mc.lang,\r\n            platform: gHelper.getShopPlatform(),\r\n            version: ca.VERSION,\r\n        })\r\n        if (err === ecode.NOT_ACCOUNT_TOKEN || err === ecode.TOKEN_INVALID) { //没有token或者无效 都重新登录\r\n            return { state: LoginState.NOT_ACCOUNT_TOKEN }\r\n        } else if (err === ecode.VERSION_TOOLOW) { //提示版本过低\r\n            return { state: LoginState.VERSION_TOOLOW, data }\r\n        } else if (err === ecode.CUR_LOBBY_FULL) { //当前大厅服满了 重新登陆\r\n            await ut.wait(0.5)\r\n            return this.tryLogin(accountToken)\r\n        } else if (err === ecode.LOBBY_QUEUE_UP) { //大厅服都满了 需要排队\r\n            const ok = await this.getLobbyServerIsNeedQueueUp()\r\n            if (ok) {\r\n                return { state: LoginState.QUEUE_UP } //满了 需要排队\r\n            }\r\n            return this.tryLogin(accountToken) //没满继续登陆\r\n        } else if (err === ecode.BAN_ACCOUNT) { //被封禁了\r\n            return { state: LoginState.BANACCOUNT_TIME, type: data.banAccountType, time: data.banAccountSurplusTime }\r\n        } else if (err) {\r\n            log('tryLogin err!', err)\r\n            return { state: LoginState.FAILURE, err: err }\r\n        }\r\n        const info = data.user\r\n        this.user.init(info)\r\n        if (data.gameBaseData) {\r\n            this.game.initBaseData(data.gameBaseData)\r\n        }\r\n        // 这里大厅服 不再返回账号token 统一使用登陆服的token 重复使用\r\n        storageMgr.saveString('account_token', data.accountToken || '0')\r\n        return { state: LoginState.SUCCEED, data: { sid: info.sid } }\r\n    }\r\n\r\n    // 游客登录\r\n    public async guestLogin() {\r\n        let guestId = ut.getBrowserParamByKey('id')\r\n        if (!guestId) {\r\n            guestId = await this.loadGuestId()\r\n        }\r\n        const ok = await this.reconnect()\r\n        if (!ok) {\r\n            return { state: LoginState.FAILURE, err: 'login.failed' }\r\n        }\r\n        const { err, data } = await this.net.request('login/HD_GuestLogin', {\r\n            guestId,\r\n            nickname: assetsMgr.lang('login.guest_nickname'),\r\n            platform: gHelper.getShopPlatform(),\r\n        }, true)\r\n        if (!err) {\r\n            this.saveGuestId(data?.guestId || '')\r\n        }\r\n        return this.loginVerifyRet(err, data?.accountToken)\r\n    }\r\n\r\n    // 返回登陆验证结果\r\n    private loginVerifyRet(err: string, accountToken: string): any {\r\n        if (err) {\r\n            return { state: LoginState.FAILURE, err: err }\r\n        }\r\n        storageMgr.saveString('account_token', accountToken || '0')\r\n        //\r\n        return { state: LoginState.SUCCEED, accountToken }\r\n    }\r\n}"]}