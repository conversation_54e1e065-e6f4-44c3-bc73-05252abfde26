{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts"], "names": ["MapNodeObj", "MapNodeType", "type", "NONE", "children", "init", "data"], "mappings": ";;;2CAGqBA,U;;;;;;;;;;;;;;AAHZC,MAAAA,W,iBAAAA,W;;;;;;;AAET;yBACqBD,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,eAErBE,IAFqB,GAED;AAAA;AAAA,0CAAYC,IAFX;AAAA,eAGrBC,QAHqB,GAGA,EAHA;AAAA;;AAKrBC,QAAAA,IAAI,CAACC,IAAD,EAAY;AACnB,eAAKJ,IAAL,GAAYI,IAAI,CAACJ,IAAjB;AACA,eAAKE,QAAL,GAAgBE,IAAI,CAACF,QAAL,IAAiB,EAAjC;AACA,iBAAO,IAAP;AACH;;AAT2B,O", "sourcesContent": ["import { MapNodeType } from \"../../common/constant/Enums\"\r\n\r\n// 地图节点\r\nexport default class MapNodeObj {\r\n\r\n    public type: MapNodeType = MapNodeType.NONE\r\n    public children: number[] = []\r\n\r\n    public init(data: any) {\r\n        this.type = data.type\r\n        this.children = data.children || []\r\n        return this\r\n    }\r\n}"]}