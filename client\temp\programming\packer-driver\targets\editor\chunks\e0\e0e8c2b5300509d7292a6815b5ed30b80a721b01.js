System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, BaseLocale, Node, js, Prefab, error, Color, Label, RichText, UIOpacity, UITransform, Tween, _crd;

  function setItemData(it, data, i, cb, target) {
    if (!cb) {
      return;
    } else if (target) {
      cb.call(target, it, data, i);
    } else {
      cb(it, data, i);
    }
  } // 切换节点


  function _reportPossibleCrUseOfBaseLocale(extras) {
    _reporterNs.report("BaseLocale", "../base/BaseLocale", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Node = _cc.Node;
      js = _cc.js;
      Prefab = _cc.Prefab;
      error = _cc.error;
      Color = _cc.Color;
      Label = _cc.Label;
      RichText = _cc.RichText;
      UIOpacity = _cc.UIOpacity;
      UITransform = _cc.UITransform;
      Tween = _cc.Tween;
    }, function (_unresolved_2) {
      BaseLocale = _unresolved_2.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7e32fdwd6lBTYmLyFEjsuGu", "ExtendNode", undefined);
      /**
       * Node扩展方法
       */


      __checkObsolete__(['Node', 'js', 'Prefab', 'SwihNodeCallback', 'error', 'Size', 'Color', 'Label', 'RichText', 'UIOpacity', 'UITransform', 'Tween']);

      Node.prototype.Data = null;

      if (!Node.prototype.hasOwnProperty('ChildrenCount')) {
        Object.defineProperty(Node.prototype, 'ChildrenCount', {
          get() {
            return this.children.length;
          }

        });
      }

      if (!Node.prototype.hasOwnProperty('opacity')) {
        Object.defineProperty(Node.prototype, 'opacity', {
          get() {
            const cmpt = this.getComponent(UIOpacity) || this.addComponent(UIOpacity);
            return cmpt.opacity;
          },

          set(val) {
            const cmpt = this.getComponent(UIOpacity) || this.addComponent(UIOpacity);
            cmpt.opacity = val;
          }

        });
      }

      if (!Node.prototype.hasOwnProperty('zIndex')) {
        Object.defineProperty(Node.prototype, 'zIndex', {
          get() {
            return this.getSiblingIndex();
          },

          set(val) {
            this.setSiblingIndex(val);
          }

        });
      }

      if (!Node.prototype.hasOwnProperty('transform')) {
        Object.defineProperty(Node.prototype, 'transform', {
          get() {
            return this.Component(UITransform);
          }

        });
      }

      if (!Node.prototype.hasOwnProperty('Height')) {
        Object.defineProperty(Node.prototype, 'Height', {
          get() {
            var _this$Component;

            return ((_this$Component = this.Component(UITransform)) == null ? void 0 : _this$Component.height) || 0;
          },

          set(val) {
            const transform = this.Component(UITransform);

            if (transform) {
              transform.height = val;
            }
          }

        });
      }

      if (!Node.prototype.hasOwnProperty('Width')) {
        Object.defineProperty(Node.prototype, 'Width', {
          get() {
            var _this$Component2;

            return ((_this$Component2 = this.Component(UITransform)) == null ? void 0 : _this$Component2.width) || 0;
          },

          set(val) {
            const transform = this.Component(UITransform);

            if (transform) {
              transform.width = val;
            }
          }

        });
      } // 停止所有动画


      Node.prototype.stopAllActions = function () {
        Tween.stopAllByTarget(this);
      };

      Node.prototype.FindChild = function (name, className) {
        name = String(name);
        let val = this;
        const arr = name.split('/');

        for (let i = 0, l = arr.length; i < l; i++) {
          if (!val.isValid) {
            return null;
          }

          val = val.getChildByName(arr[i]);

          if (!val) {
            return null;
          }
        }

        if (className) {
          val = val.getComponent(className);
        }

        return val;
      };

      Node.prototype.Child = function (name, className) {
        if (!this.isValid) {
          return null;
        }

        name = String(name);
        const cls = typeof className === 'function' ? '_' + js.getClassName(className).replace('.', '') : className ? '_' + className : '';
        const field = '$_' + name.replace(/\//g, '_') + cls;
        let val = this[field];

        if (val === undefined) {
          val = this;

          if (!val.isValid) {
            return null;
          }

          const arr = name.split('/');

          for (let i = 0, l = arr.length; i < l; i++) {
            val = val.getChildByName(arr[i]);

            if (!val) {
              break;
            }
          }

          if (val && className) {
            val = val.getComponent(className);
          }

          this[field] = !!val ? val : null;
        }

        return val;
      };

      Node.prototype.Component = function (className) {
        if (!className) {
          return null;
        }

        const cls = typeof className === 'function' ? js.getClassName(className).replace('.', '') : className;
        const field = '$_' + cls;
        let val = this[field];

        if (val === undefined) {
          val = this.getComponent(className);
          this[field] = val;
        }

        return val;
      };

      Node.prototype.Items = function (list, prefab, cb, target) {
        let i = 0,
            childs = this.children,
            item = childs[0];
        let count = 0;

        if (typeof list === 'number') {
          count = list;
          list = null;
        } else {
          count = list.length;
        }

        if (typeof prefab === 'function') {
          target = cb;
          cb = prefab;
        } else if (prefab instanceof Node || prefab instanceof Prefab) {
          item = prefab;
        }

        if (!item) {
          return logger.error('Items error, not item');
        }

        for (let l = this.children.length; i < l; i++) {
          const it = childs[i];

          if (i < count) {
            it.active = true;
            setItemData(it, list ? list[i] : undefined, i, cb, target);
          } else {
            it.Data = null;
            it.active = false;
          }
        }

        for (; i < count; i++) {
          const it = mc.instantiate(item, this);
          it.active = true;
          setItemData(it, list ? list[i] : undefined, i, cb, target);
        }
      }; // 添加一个


      Node.prototype.AddItem = function (prefab, cb, target) {
        let item = null;

        if (typeof prefab === 'function') {
          target = cb;
          cb = prefab;
        } else if (prefab instanceof Node || prefab instanceof Prefab) {
          item = prefab;
        }

        let i = this.children.findIndex(m => !m.active),
            it = null;

        if (item) {
          it = mc.instantiate(item, this);
        } else if (i !== -1) {
          it = this.children[i];
        } else {
          it = mc.instantiate(this.children[0], this);
        }

        it.active = true;
        const index = i === -1 ? this.children.length : i;

        if (!cb) {
          return {
            it,
            i
          };
        }

        return setItemData(it, index, undefined, cb, target);
      };

      Node.prototype.Swih = function (val, reverse, ignores) {
        let name, cb;

        if (typeof val === 'function') {
          cb = val;
        } else if (typeof val === 'number' || typeof val === 'string') {
          name = String(val);
        } else {
          return [];
        }

        let arr = [];

        for (let i = 0, l = this.children.length; i < l; i++) {
          const m = this.children[i];

          if (ignores != null && ignores.includes(m.name)) {
            continue;
          } else if (reverse) {
            m.active = cb ? !cb(m) : m.name !== name;
          } else {
            m.active = cb ? !!cb(m) : m.name === name;
          }

          if (m.active) {
            arr.push(m);
          }
        }

        return arr;
      }; // 适应大小


      Node.prototype.adaptScale = function (targetSize, selfSize, maxScale = 1) {
        selfSize = selfSize || this.getContentSize(); // 先算出宽度比例

        let scale = targetSize.width / selfSize.width; // 如果高度大了 就用高的比例

        if (selfSize.height * scale > targetSize.height) {
          scale = targetSize.height / selfSize.height;
        }

        this.scale = Math.min(scale, maxScale);
      }; // 适应宽高


      Node.prototype.adaptSize = function (targetSize, selfSize, maxScale = 1) {
        selfSize = selfSize || this.getContentSize();
        let r = Math.min(targetSize.width / selfSize.width, maxScale);
        const h = Math.floor(r * selfSize.height);

        if (h <= targetSize.height) {
          this.width = Math.floor(r * selfSize.width);
          this.height = h;
        } else {
          r = Math.min(targetSize.height / selfSize.height, maxScale);
          this.width = Math.floor(r * selfSize.width);
          this.height = Math.floor(r * selfSize.height);
        }
      }; //


      Node.prototype.getActive = function () {
        return this.active;
      };

      Node.prototype.setActive = function (val) {
        this.active = val;
        return val;
      }; // 设置颜色


      Node.prototype.Color = function (val) {
        if (!val) {} else if (val instanceof Color) {
          this.color = val;
        } else {
          this.color = Color.WHITE.fromHEX(val);
        }

        return this;
      }; // 设置触摸事件穿透


      Node.prototype.SetSwallowTouches = function (val) {
        if (this._touchListener) {
          this._touchListener.setSwallowTouches(val);
        }
      };

      Node.prototype.IsSwallowTouches = function () {
        return this._touchListener && this._touchListener.isSwallowTouches();
      }; // 设置多语言key


      Node.prototype.setLocaleKey = function (key, ...params) {
        const locale = this.Component(_crd && BaseLocale === void 0 ? (_reportPossibleCrUseOfBaseLocale({
          error: Error()
        }), BaseLocale) : BaseLocale);

        if (locale) {
          locale.setKey(key, ...params);
        } else {
          error('setLocaleKey error, not LocaleComponent!');
        }

        return this.Component(Label) || this.Component(RichText);
      };

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e0e8c2b5300509d7292a6815b5ed30b80a721b01.js.map