{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts"], "names": ["_decorator", "Label", "Widget", "v3", "easing", "tween", "<PERSON><PERSON>", "NotEvent", "ccclass", "MessageBoxNotCtrl", "ut", "syncLock", "mc", "BaseNoticeCtrl", "closeNode_", "rootNode_", "titleLbl_", "contentRt_", "buttonsNode_", "okCb", "cancelCb", "clickButtonClose", "listenEventMaps", "OPEN_MESSAGE_BOX", "onEventOpen", "HIDE_MESSAGE_BOX", "onEventHide", "onCreate", "onClickClose", "event", "data", "hide", "onHide", "onClickButtons", "name", "target", "msg", "opts", "open", "show", "lockTouch", "setLocaleKey", "title", "params", "ok", "cancel", "Child", "active", "okText", "cancelText", "stopAllActions", "Height", "Math", "max", "node", "children", "for<PERSON>ach", "m", "Component", "updateAlignment", "isMask", "mask", "playShowAction", "lockClose", "wait", "unlockTouch", "widget", "enabled", "scale", "to", "backOut", "start", "interactable", "opacity", "sineOut"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,K,OAAAA,K;AAA6BC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AACjFC,MAAAA,Q;;;;;;;;;OAED;AAAEC,QAAAA;AAAF,O,GAAcR,U;;yBAGCS,iB,WA6DnBC,EAAE,CAACC,Q,EA9DJH,O,qBAAD,MACqBC,iBADrB,SAC+CG,EAAE,CAACC,cADlD,CACiE;AAAA;AAAA;AAEhE;AAFgE,eAGxDC,UAHwD,GAGrC,IAHqC;AAGhC;AAHgC,eAIxDC,SAJwD,GAItC,IAJsC;AAIjC;AAJiC,eAKxDC,SALwD,GAKrC,IALqC;AAKhC;AALgC,eAMxDC,UANwD,GAMjC,IANiC;AAM5B;AAN4B,eAOxDC,YAPwD,GAOnC,IAPmC;AAO9B;AAClC;AARgE,eAUxDC,IAVwD,GAUvC,IAVuC;AAAA,eAWxDC,QAXwD,GAWnC,IAXmC;AAAA,eAaxDC,gBAbwD,GAa5B,IAb4B;AAAA;;AAavB;AAElCC,QAAAA,eAAe,GAAG;AACxB,iBAAO,CACN;AAAE,aAAC;AAAA;AAAA,sCAASC,gBAAV,GAA6B,KAAKC;AAApC,WADM,EAEN;AAAE,aAAC;AAAA;AAAA,sCAASC,gBAAV,GAA6B,KAAKC;AAApC,WAFM,CAAP;AAIA;;AAEoB,cAARC,QAAQ,GAAG,CACvB,CAvB+D,CAyBhE;AACA;AAEA;;;AACAC,QAAAA,YAAY,CAACC,KAAD,EAAoBC,IAApB,EAAkC;AAC7C,eAAKC,IAAL;AACA,eAAKC,MAAL;AACA,SAhC+D,CAkChE;;;AACAC,QAAAA,cAAc,CAACJ,KAAD,EAAoBC,IAApB,EAAkC;AAC/C,cAAI,KAAKT,gBAAT,EAA2B;AAC1B,iBAAKU,IAAL;AACA;;AACD,gBAAMG,IAAI,GAAGL,KAAK,CAACM,MAAN,CAAaD,IAA1B;;AACA,cAAIA,IAAI,KAAK,IAAb,EAAmB;AAClB,iBAAKf,IAAL,IAAa,KAAKA,IAAL,EAAb;AACA,WAFD,MAEO,IAAIe,IAAI,KAAK,QAAb,EAAuB;AAC7B,iBAAKd,QAAL,IAAiB,KAAKA,QAAL,EAAjB;AACA;;AACD,eAAKY,MAAL;AACA,SA9C+D,CA+ChE;AACA;;;AAEQR,QAAAA,WAAW,CAACY,GAAD,EAAcC,IAAd,EAAqC;AACvD,eAAKC,IAAL;AACA,eAAKC,IAAL,CAAUH,GAAV,EAAeC,IAAf;AACA;;AAEOX,QAAAA,WAAW,GAAG;AACrB,eAAKK,IAAL;AACA,eAAKC,MAAL;AACA,SA1D+D,CA2DhE;;;AAGkB,cAAJO,IAAI,CAACH,GAAD,EAAcC,IAAd,EAAqC;AAAA;;AACtDzB,UAAAA,EAAE,CAAC4B,SAAH,CAAa,sBAAb;AACAH,UAAAA,IAAI,GAAGA,IAAI,IAAI,EAAf;AACA,eAAKrB,SAAL,CAAeyB,YAAf,CAA4BJ,IAAI,CAACK,KAAL,IAAc,iBAA1C;AACA,eAAKzB,UAAL,CAAgBwB,YAAhB,CAA6BL,GAA7B,EAAkC,IAAIC,IAAI,CAACM,MAAL,IAAe,EAAnB,CAAlC;AACA,eAAKxB,IAAL,GAAYkB,IAAI,CAACO,EAAjB;AACA,eAAKxB,QAAL,GAAgBiB,IAAI,CAACQ,MAArB,CANsD,CAOtD;;AACA,eAAK3B,YAAL,CAAkB4B,KAAlB,CAAwB,QAAxB,EAAkCC,MAAlC,GAA2C,CAAC,CAAC,KAAK3B,QAAlD,CARsD,CAStD;;AACA,eAAKF,YAAL,CAAkB4B,KAAlB,CAAwB,QAAxB,EAAkC7C,KAAlC,EAAyCwC,YAAzC,CAAsDJ,IAAI,CAACW,MAAL,IAAe,iBAArE;AACA,eAAK9B,YAAL,CAAkB4B,KAAlB,CAAwB,YAAxB,EAAsC7C,KAAtC,EAA6CwC,YAA7C,CAA0DJ,IAAI,CAACY,UAAL,IAAmB,qBAA7E,EAXsD,CAYtD;;AACA,eAAKlC,SAAL,CAAemC,cAAf;AACA,eAAKnC,SAAL,CAAeoC,MAAf,GAAwBC,IAAI,CAACC,GAAL,CAAS,GAAT,EAAc,KAAKpC,UAAL,CAAgBqC,IAAhB,CAAqBH,MAArB,GAA8B,GAA5C,CAAxB;AACA,eAAKpC,SAAL,CAAewC,QAAf,CAAwBC,OAAxB,CAAgCC,CAAC;AAAA;;AAAA,mCAAIA,CAAC,CAACC,SAAF,CAAYxD,MAAZ,CAAJ,qBAAI,aAAqByD,eAArB,EAAJ;AAAA,WAAjC,EAfsD,CAgBtD;;AACA,gBAAMC,MAAM,GAAG,KAAK9C,UAAL,CAAgBiC,MAAhB,GAAyB,UAAAV,IAAI,SAAJ,kBAAMwB,IAAN,MAAe,KAAf,GAAuB,KAAvB,GAA+B,IAAvE;AACA,eAAKC,cAAL,CAAoBF,MAApB,EAA4B,YAACvB,IAAD,aAAC,OAAM0B,SAAP,CAA5B,EAlBsD,CAmBtD;;AACA,eAAK1C,gBAAL,sCAAwBgB,IAAxB,qBAAwB,OAAMhB,gBAA9B,oCAAkD,IAAlD;AACA,gBAAMX,EAAE,CAACsD,IAAH,CAAQ,IAAR,CAAN;AACApD,UAAAA,EAAE,CAACqD,WAAH,CAAe,sBAAf;AACA;;AAEOH,QAAAA,cAAc,CAACF,MAAD,EAAkBG,SAAlB,EAAsC;AAC3D,gBAAMG,MAAM,GAAG,KAAKR,SAAL,CAAexD,MAAf,CAAf;;AACA,cAAIgE,MAAM,IAAIA,MAAM,CAACC,OAArB,EAA8B;AAC7BD,YAAAA,MAAM,CAACP,eAAP;AACAO,YAAAA,MAAM,CAACC,OAAP,GAAiB,KAAjB;AACA;;AACD,eAAKpD,SAAL,CAAemC,cAAf;AACA,eAAKnC,SAAL,CAAeqD,KAAf,GAAuBjE,EAAE,CAAC,GAAD,CAAzB;AACAE,UAAAA,KAAK,CAAC,KAAKU,SAAN,CAAL,CAAsBsD,EAAtB,CAAyB,IAAzB,EAA+B;AAAED,YAAAA,KAAK,EAAEjE,EAAE,CAAC,CAAD;AAAX,WAA/B,EAAiD;AAAEC,YAAAA,MAAM,EAAEA,MAAM,CAACkE;AAAjB,WAAjD,EAA6EC,KAA7E;;AACA,cAAIX,MAAJ,EAAY;AACX;AACA,iBAAK9C,UAAL,CAAgB4C,SAAhB,CAA0BpD,MAA1B,EAAkCkE,YAAlC,GAAiDT,SAAjD,CAFW,CAGX;;AACA,iBAAKjD,UAAL,CAAgBoC,cAAhB;AACA,iBAAKpC,UAAL,CAAgB2D,OAAhB,GAA0B,CAA1B;AACApE,YAAAA,KAAK,CAAC,KAAKS,UAAN,CAAL,CAAuBuD,EAAvB,CAA0B,GAA1B,EAA+B;AAAEI,cAAAA,OAAO,EAAE;AAAX,aAA/B,EAAiD;AAAErE,cAAAA,MAAM,EAAEA,MAAM,CAACsE;AAAjB,aAAjD,EAA6EH,KAA7E;AACA;AACD;;AAEOvC,QAAAA,MAAM,GAAG;AAChB,eAAKb,IAAL,GAAY,IAAZ;AACA,eAAKC,QAAL,GAAgB,IAAhB;AACA;;AA7G+D,O", "sourcesContent": ["import { _decorator, log, Node, Label, RichText, EventTouch, Widget, v3, easing, tween, Button } from \"cc\";\nimport NotEvent from \"../../common/event/NotEvent\";\nimport { MessageBoxOpts } from \"../../common/constant/DataType\";\nconst { ccclass } = _decorator;\n\n@ccclass\nexport default class MessageBoxNotCtrl extends mc.BaseNoticeCtrl {\n\n\t//@autocode property begin\n\tprivate closeNode_: Node = null // path://close_be_n\n\tprivate rootNode_: Node = null // path://root_n\n\tprivate titleLbl_: Label = null // path://root_n/title/title_l\n\tprivate contentRt_: RichText = null // path://root_n/content_rt\n\tprivate buttonsNode_: Node = null // path://root_n/buttons_nbe_n\n\t//@end\n\n\tprivate okCb: Function = null\n\tprivate cancelCb: Function = null\n\n\tprivate clickButtonClose: boolean = true //点击按钮是否关闭界面\n\n\tpublic listenEventMaps() {\n\t\treturn [\n\t\t\t{ [NotEvent.OPEN_MESSAGE_BOX]: this.onEventOpen },\n\t\t\t{ [NotEvent.HIDE_MESSAGE_BOX]: this.onEventHide },\n\t\t]\n\t}\n\n\tpublic async onCreate() {\n\t}\n\n\t// ----------------------------------------- button listener function -------------------------------------------\n\t//@autocode button listener\n\n\t// path://close_be_n\n\tonClickClose(event: EventTouch, data: string) {\n\t\tthis.hide()\n\t\tthis.onHide()\n\t}\n\n\t// path://root_n/buttons_nbe_n\n\tonClickButtons(event: EventTouch, data: string) {\n\t\tif (this.clickButtonClose) {\n\t\t\tthis.hide()\n\t\t}\n\t\tconst name = event.target.name\n\t\tif (name === 'ok') {\n\t\t\tthis.okCb && this.okCb()\n\t\t} else if (name === 'cancel') {\n\t\t\tthis.cancelCb && this.cancelCb()\n\t\t}\n\t\tthis.onHide()\n\t}\n\t//@end\n\t// ----------------------------------------- event listener function --------------------------------------------\n\n\tprivate onEventOpen(msg: string, opts?: MessageBoxOpts) {\n\t\tthis.open()\n\t\tthis.show(msg, opts)\n\t}\n\n\tprivate onEventHide() {\n\t\tthis.hide()\n\t\tthis.onHide()\n\t}\n\t// ----------------------------------------- custom function ----------------------------------------------------\n\n\*************\n\tprivate async show(msg: string, opts?: MessageBoxOpts) {\n\t\tmc.lockTouch('__open_message_box__')\n\t\topts = opts || {}\n\t\tthis.titleLbl_.setLocaleKey(opts.title || 'login.title_tip')\n\t\tthis.contentRt_.setLocaleKey(msg, ...(opts.params || []))\n\t\tthis.okCb = opts.ok\n\t\tthis.cancelCb = opts.cancel\n\t\t// 是否显示取消按钮\n\t\tthis.buttonsNode_.Child('cancel').active = !!this.cancelCb\n\t\t// 设置按钮名字\n\t\tthis.buttonsNode_.Child('ok/val', Label).setLocaleKey(opts.okText || 'login.button_ok')\n\t\tthis.buttonsNode_.Child('cancel/val', Label).setLocaleKey(opts.cancelText || 'login.button_cancel')\n\t\t// 做动画\n\t\tthis.rootNode_.stopAllActions()\n\t\tthis.rootNode_.Height = Math.max(360, this.contentRt_.node.Height + 200)\n\t\tthis.rootNode_.children.forEach(m => m.Component(Widget)?.updateAlignment())\n\t\t// 是否显示mask 默认显示\n\t\tconst isMask = this.closeNode_.active = opts?.mask === false ? false : true\n\t\tthis.playShowAction(isMask, !opts?.lockClose)\n\t\t// 是否开启点击按钮就关闭 默认开启\n\t\tthis.clickButtonClose = opts?.clickButtonClose ?? true\n\t\tawait ut.wait(0.25)\n\t\tmc.unlockTouch('__open_message_box__')\n\t}\n\n\tprivate playShowAction(isMask: boolean, lockClose: boolean) {\n\t\tconst widget = this.Component(Widget)\n\t\tif (widget && widget.enabled) {\n\t\t\twidget.updateAlignment()\n\t\t\twidget.enabled = false\n\t\t}\n\t\tthis.rootNode_.stopAllActions()\n\t\tthis.rootNode_.scale = v3(0.4)\n\t\ttween(this.rootNode_).to(0.25, { scale: v3(1) }, { easing: easing.backOut }).start()\n\t\tif (isMask) {\n\t\t\t// 禁止点击空白关闭\n\t\t\tthis.closeNode_.Component(Button).interactable = lockClose\n\t\t\t// 做动画\n\t\t\tthis.closeNode_.stopAllActions()\n\t\t\tthis.closeNode_.opacity = 0\n\t\t\ttween(this.closeNode_).to(0.3, { opacity: 120 }, { easing: easing.sineOut }).start()\n\t\t}\n\t}\n\n\tprivate onHide() {\n\t\tthis.okCb = null\n\t\tthis.cancelCb = null\n\t}\n}\n"]}