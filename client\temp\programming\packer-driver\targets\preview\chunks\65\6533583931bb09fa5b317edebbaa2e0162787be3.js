System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Prefab, AnimalCmpt, EventType, AnimPlayCmpt, RoleCmpt, RoleObj, viewHelper, _class, _crd, ccclass, GameWindCtrl;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfAnimalObj(extras) {
    _reporterNs.report("AnimalObj", "../../model/game/AnimalObj", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAnimalCmpt(extras) {
    _reporterNs.report("AnimalCmpt", "./AnimalCmpt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameModel(extras) {
    _reporterNs.report("GameModel", "../../model/game/GameModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventType(extras) {
    _reporterNs.report("EventType", "../../common/event/EventType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAnimPlayCmpt(extras) {
    _reporterNs.report("AnimPlayCmpt", "./AnimPlayCmpt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRoleCmpt(extras) {
    _reporterNs.report("RoleCmpt", "./RoleCmpt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRoleObj(extras) {
    _reporterNs.report("RoleObj", "../../model/game/RoleObj", _context.meta, extras);
  }

  function _reportPossibleCrUseOfviewHelper(extras) {
    _reporterNs.report("viewHelper", "../../common/helper/ViewHelper", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Prefab = _cc.Prefab;
    }, function (_unresolved_2) {
      AnimalCmpt = _unresolved_2.default;
    }, function (_unresolved_3) {
      EventType = _unresolved_3.default;
    }, function (_unresolved_4) {
      AnimPlayCmpt = _unresolved_4.default;
    }, function (_unresolved_5) {
      RoleCmpt = _unresolved_5.default;
    }, function (_unresolved_6) {
      RoleObj = _unresolved_6.default;
    }, function (_unresolved_7) {
      viewHelper = _unresolved_7.viewHelper;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e0786caNBdOXb+Qh/0ZaZAG", "GameWindCtrl", undefined);

      __checkObsolete__(['_decorator', 'log', 'Node', 'Prefab', 'EventTouch']);

      ({
        ccclass
      } = _decorator);

      _export("default", GameWindCtrl = ccclass(_class = class GameWindCtrl extends mc.BaseWindCtrl {
        constructor() {
          super(...arguments);
          //@autocode property begin
          this.mapNode_ = null;
          // path://map_n
          this.itemsNode_ = null;
          // path://items_n
          this.flutterNode_ = null;
          // path://flutter_n
          //@end
          this.animPlay = null;
          this.animals = [];
          this.animalMap = {};
          this.myRole = null;
          this.model = null;
        }

        listenEventMaps() {
          return [{
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).UPDATE_MY_BATTLE_AREA]: this.onUpdateMyBattleArea,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).PLAY_FLUTTER_HP]: this.onPlayFlutterHp,
            enter: true
          }, {
            [(_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
              error: Error()
            }), EventType) : EventType).REMOVE_ANIMAL]: this.onRemoveAnimal,
            enter: true
          }];
        }

        onCreate() {
          var _this = this;

          return _asyncToGenerator(function* () {
            _this.animPlay = _this.node.addComponent(_crd && AnimPlayCmpt === void 0 ? (_reportPossibleCrUseOfAnimPlayCmpt({
              error: Error()
            }), AnimPlayCmpt) : AnimPlayCmpt);
            _this.model = _this.getModel('game');
            _this.myRole = yield _this.createRole(new (_crd && RoleObj === void 0 ? (_reportPossibleCrUseOfRoleObj({
              error: Error()
            }), RoleObj) : RoleObj)().init(110001), 0);
            yield _this.animPlay.init(_this.key);
          })();
        }

        onEnter(data) {
          this.model.enter();
          this.myRole.playAnimation('idle');
          this.updateEncounterInfo();
        }

        onClean() {
          this.model.leave();
          this.animPlay.clean();
          this.animPlay = null;
          assetsMgr.releaseTempResByTag(this.key);
        } // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        // path://map_n/goon_be


        onClickGoon(event, data) {
          this.myRole.playAnimation('attack', () => this.myRole.playAnimation('idle')); // viewHelper.showPnl('game/Map')

          (_crd && viewHelper === void 0 ? (_reportPossibleCrUseOfviewHelper({
            error: Error()
          }), viewHelper) : viewHelper).showPnl('game/Test');
        } //@end
        // ----------------------------------------- event listener function --------------------------------------------


        onUpdateMyBattleArea() {
          this.updateMyBattleArea();
        } // 播放飘字


        onPlayFlutterHp(data) {
          var _this$animals$find;

          var node = (_this$animals$find = this.animals.find(m => m.uid === data.uid)) == null ? void 0 : _this$animals$find.node;

          if (node) {
            this.animPlay.playFlutter(data, this.flutterNode_, ut.convertToNodeAR(node, this.flutterNode_));
          }
        }

        onRemoveAnimal(uid, release) {
          this.cleanAnimal(this.animals.remove('uid', uid), release);
        } // ----------------------------------------- custom function ----------------------------------------------------


        update(dt) {
          var _this$model;

          (_this$model = this.model) == null || _this$model.update(dt);
        } // 创建一个角色


        createRole(data, index) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            if (!_this2.isValid || !data) {
              return null;
            }

            var pfb = yield assetsMgr.loadTempRes(data.getPrefabUrl(), Prefab, _this2.key);

            if (!pfb || !_this2.isValid) {
              return null;
            }

            var pos = _this2.mapNode_.Child('role_pos/' + index).getPosition();

            var role = yield mc.instantiate(pfb, _this2.itemsNode_).getComponent(_crd && RoleCmpt === void 0 ? (_reportPossibleCrUseOfRoleCmpt({
              error: Error()
            }), RoleCmpt) : RoleCmpt).init(data, pos, _this2.key);
            return role;
          })();
        } // 创建一个动物


        createAnimal(data, rootType) {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            if (!_this3.isValid || !data) {
              return null;
            }

            var animal = _this3.animals.find(m => m.uid === data.uid);

            if (!animal) {} else if (data.isDie()) {
              _this3.animals.remove('uid', data.uid);

              _this3.cleanAnimal(animal);

              return null;
            } else {
              return animal.resync(data);
            }

            var pfb = yield assetsMgr.loadTempRes(data.getPrefabUrl(), Prefab, _this3.key);

            if (!pfb || !_this3.isValid) {
              return null;
            } else if (_this3.animalMap[data.uid]) {
              return null; //防止多次创建
            } // 重新获取以防数据不统一


            var uid = data.uid; // data = this.model.getAnimal(uid)

            if (!data || data.isDie()) {
              return null;
            }

            animal = mc.instantiate(pfb, _this3.itemsNode_).getComponent(_crd && AnimalCmpt === void 0 ? (_reportPossibleCrUseOfAnimalCmpt({
              error: Error()
            }), AnimalCmpt) : AnimalCmpt).init(data, _this3.key);

            _this3.animals.push(animal);

            _this3.animalMap[data.uid] = animal;
            return animal;
          })();
        }

        cleanAnimal(animal, release) {
          if (animal) {
            delete this.animalMap[animal.uid];
            animal.clean(release);
          }
        } // 刷新我的战斗区域


        updateMyBattleArea() {
          this.model.getBattleAnimals().forEach(m => this.createAnimal(m, 'my'));
        } // 运行战斗


        runBattle() {
          var _this4 = this;

          return _asyncToGenerator(function* () {
            yield Promise.all(_this4.model.getEnemyAnimals().map(m => _this4.createAnimal(m, 'enemy')));

            _this4.model.battleBegin();
          })();
        } // 刷新遭遇信息


        updateEncounterInfo() {// const data = this.model.getCurrContent()
        }

      }) || _class);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6533583931bb09fa5b317edebbaa2e0162787be3.js.map