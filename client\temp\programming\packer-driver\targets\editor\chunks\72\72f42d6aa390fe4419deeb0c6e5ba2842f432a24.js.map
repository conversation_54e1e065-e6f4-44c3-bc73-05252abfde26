{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts"], "names": ["_decorator", "CCBoolean", "CCInteger", "Component", "error", "<PERSON><PERSON><PERSON><PERSON>", "Layout", "misc", "ScrollView", "UITransform", "v3", "Vec3", "ccclass", "property", "menu", "requireComponent", "disallowMultiple", "executionOrder", "ScrollViewEx", "tooltip", "type", "range", "slide", "scrollView", "viewSize", "content", "contentTransform", "layout", "scrollType", "rowCount", "preContentPosition", "tempContentPosition", "frameCount", "renderStartIndex", "needRenderCount", "currRenderCount", "item", "itemTransform", "itemSize", "pool", "childContentDefaultPosition", "itemDefaultPosition", "datas", "dataCount", "setItemCallback", "callback<PERSON><PERSON><PERSON>", "tempVec", "onLoad", "init", "getComponent", "parent", "transform", "contentSize", "enabled", "children", "childScrollViewName", "sv", "Child", "getPosition", "active", "reset", "sum", "LayoutType", "Type", "setItemPos", "width", "height", "VERTICAL", "spacingY", "paddingTop", "paddingBottom", "setItemPosByVertical", "HORIZONTAL", "spacingX", "paddingLeft", "paddingRight", "setItemPosByHorizontal", "GRID", "startAxis", "AxisDirection", "w", "row", "Math", "floor", "count", "ceil", "setItemPosByGridHorizontal", "h", "setItemPosByGridVertical", "set", "virtual", "vh", "cy", "abs", "y", "clampf", "vw", "cx", "x", "length", "i", "ChildrenCount", "it", "putItem", "destroy", "setContentSize", "horizontalScrollBar", "_onScroll", "ZERO", "verticalScrollBar", "push", "call", "index", "out", "startY", "anchorY", "startX", "anchorX", "getItem", "pop", "mc", "instantiate", "Data", "has", "uuid", "delItems", "start", "d", "node", "childContentPosition", "updateItems", "cnt", "updateRenderCount", "cur", "l", "min", "setPosition", "checkScroll", "dir", "sy", "sx", "idx", "max", "update", "dt", "equals", "updateRate", "list", "len", "cb", "target", "addByList"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAqBC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,O,OAAAA,O;AAASC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;;;;;;;;;OAEjI;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,IAArB;AAA2BC,QAAAA,gBAA3B;AAA6CC,QAAAA,gBAA7C;AAA+DC,QAAAA;AAA/D,O,GAAkFjB,U;;yBAOnEkB,Y,WAJpBF,gBAAgB,E,UAChBD,gBAAgB,CAACP,UAAD,C,UAChBM,IAAI,CAAC,oBAAD,C,UACJG,cAAc,CAAC,CAAC,GAAF,C,UAGVJ,QAAQ,CAACZ,SAAD,C,UAERY,QAAQ,CAAC;AAAEM,QAAAA,OAAO,EAAE;AAAX,OAAD,C,UAERN,QAAQ,CAAC;AAAEO,QAAAA,IAAI,EAAElB,SAAR;AAAmBmB,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,EAAJ,EAAQ,CAAR,CAA1B;AAAsCF,QAAAA,OAAO,EAAE,SAA/C;AAA0DG,QAAAA,KAAK,EAAE;AAAjE,OAAD,C,UAERT,QAAQ,CAAC;AAAEO,QAAAA,IAAI,EAAElB,SAAR;AAAmBmB,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,EAAJ,EAAQ,CAAR,CAA1B;AAAsCF,QAAAA,OAAO,EAAE,eAA/C;AAAgEG,QAAAA,KAAK,EAAE;AAAvE,OAAD,C,EAbZV,O,gFAAD,MAKqBM,YALrB,SAK0Cf,SAL1C,CAKoD;AAAA;AAAA;;AAAA;;AAGhB;AAHgB;;AAKP;AALO;;AAOjB;AAPiB;;AASV;AATU,eAWxCoB,UAXwC,GAWf,IAXe;AAAA,eAYxCC,QAZwC,GAYvB,IAZuB;AAAA,eAaxCC,OAbwC,GAaxB,IAbwB;AAAA,eAcxCC,gBAdwC,GAcR,IAdQ;AAAA,eAexCC,MAfwC,GAevB,IAfuB;AAAA,eAgBxCC,UAhBwC,GAgBnB,CAhBmB;AAgBjB;AAhBiB,eAiBxCC,QAjBwC,GAiBrB,CAjBqB;AAiBnB;AAjBmB,eAkBxCC,kBAlBwC,GAkBnBpB,EAAE,EAlBiB;AAAA,eAmBxCqB,mBAnBwC,GAmBlBrB,EAAE,EAnBgB;AAAA,eAqBxCsB,UArBwC,GAqBnB,CArBmB;AAqBjB;AArBiB,eAsBxCC,gBAtBwC,GAsBb,CAtBa;AAsBX;AAtBW,eAuBxCC,eAvBwC,GAuBd,CAvBc;AAuBZ;AAvBY,eAwBxCC,eAxBwC,GAwBd,CAxBc;AAwBZ;AAxBY,eA0BxCC,IA1BwC,GA0B3B,IA1B2B;AAAA,eA2BxCC,aA3BwC,GA2BX,IA3BW;AAAA,eA4BxCC,QA5BwC,GA4BvB,IA5BuB;AAAA,eA6BxCC,IA7BwC,GA6BzB,EA7ByB;AAAA,eA8BxCC,2BA9BwC,GA8BV9B,EAAE,EA9BQ;AA8BL;AA9BK,eA+BxC+B,mBA/BwC,GA+BlB/B,EAAE,EA/BgB;AA+Bb;AA/Ba,eAiCxCgC,KAjCwC,GAiC0F,EAjC1F;AAAA,eAkCxCC,SAlCwC,GAkCpB,CAlCoB;AAAA,eAmCxCC,eAnCwC,GAmCZ,IAnCY;AAAA,eAoCxCC,cApCwC,GAoClB,IApCkB;AAAA,eAsCxCC,OAtCwC,GAsC9BpC,EAAE,EAtC4B;AAAA;;AAwChDqC,QAAAA,MAAM,GAAG;AACL,eAAKC,IAAL;AACH;;AAEOA,QAAAA,IAAI,GAAG;AAAA;;AACX,cAAI,KAAKzB,UAAT,EAAqB;AACjB;AACH;;AACD,eAAKA,UAAL,GAAkB,KAAK0B,YAAL,CAAkBzC,UAAlB,CAAlB;AACA,eAAKiB,OAAL,GAAe,KAAKF,UAAL,CAAgBE,OAA/B;AACA,eAAKC,gBAAL,GAAwB,KAAKD,OAAL,CAAawB,YAAb,CAA0BxC,WAA1B,CAAxB;AACA,eAAKe,QAAL,oBAAgB,KAAKC,OAArB,8BAAgB,cAAcyB,MAA9B,qBAAgB,cAAsBC,SAAtB,CAAgCC,WAAhD;AACA,eAAKzB,MAAL,GAAc,KAAKF,OAAL,CAAawB,YAAb,CAA0B3C,MAA1B,CAAd;;AACA,cAAI,CAAC,KAAKqB,MAAV,EAAkB;AACd,mBAAOvB,KAAK,CAAC,YAAD,CAAZ;AACH;;AACD,eAAKuB,MAAL,CAAY0B,OAAZ,GAAsB,KAAtB;AACA,eAAKjB,IAAL,GAAY,KAAKX,OAAL,CAAa6B,QAAb,CAAsB,CAAtB,CAAZ;;AACA,cAAI,CAAC,KAAKlB,IAAV,EAAgB;AACZ,mBAAOhC,KAAK,CAAC,uBAAD,CAAZ;AACH;;AACD,cAAI,KAAKmD,mBAAT,EAA8B;AAAA;;AAC1B,kBAAMC,EAAE,GAAG,KAAKpB,IAAL,CAAUqB,KAAV,CAAgB,KAAKF,mBAArB,EAA0C/C,UAA1C,CAAX;AACAgD,YAAAA,EAAE,oBAAIA,EAAE,CAAC/B,OAAP,qBAAI,YAAYiC,WAAZ,CAAwB,KAAKlB,2BAA7B,CAAJ,CAAF;AACH;;AACD,eAAKJ,IAAL,CAAUsB,WAAV,CAAsB,KAAKjB,mBAA3B;AACA,eAAKJ,aAAL,GAAqB,KAAKD,IAAL,CAAUa,YAAV,CAAuBxC,WAAvB,CAArB;AACA,eAAK6B,QAAL,GAAgB,KAAKF,IAAL,CAAUe,SAAV,CAAoBC,WAApC;AACA,eAAKhB,IAAL,CAAUuB,MAAV,GAAmB,KAAnB;AACH;;AAEOC,QAAAA,KAAK,GAAG;AACZ,cAAI,CAAC,KAAKrC,UAAV,EAAsB;AAClB,iBAAKyB,IAAL;AACH;;AACD,gBAAMa,GAAG,GAAG,KAAKlB,SAAjB;AACA,gBAAMhB,MAAM,GAAG,KAAKA,MAApB;AAAA,gBAA4BmC,UAAU,GAAGxD,MAAM,CAACyD,IAAhD;AACA,cAAIC,UAAoB,GAAG,IAA3B;AAAA,cAAkCC,KAAK,GAAG,KAAKvC,gBAAL,CAAsBuC,KAAhE;AAAA,cAAuEC,MAAM,GAAG,KAAKxC,gBAAL,CAAsBwC,MAAtG;;AACA,cAAIvC,MAAM,CAACP,IAAP,KAAgB0C,UAAU,CAACK,QAA/B,EAAyC;AACrCD,YAAAA,MAAM,GAAGL,GAAG,GAAG,KAAKvB,QAAL,CAAc4B,MAApB,GAA6B,CAACL,GAAG,GAAG,CAAP,IAAYlC,MAAM,CAACyC,QAAhD,GAA2DzC,MAAM,CAAC0C,UAAlE,GAA+E1C,MAAM,CAAC2C,aAA/F;AACAN,YAAAA,UAAU,GAAG,KAAKO,oBAAlB;AACA,iBAAK3C,UAAL,GAAkB,CAAlB;AACA,iBAAKC,QAAL,GAAgB,CAAhB;AACH,WALD,MAKO,IAAIF,MAAM,CAACP,IAAP,KAAgB0C,UAAU,CAACU,UAA/B,EAA2C;AAC9CP,YAAAA,KAAK,GAAGJ,GAAG,GAAG,KAAKvB,QAAL,CAAc2B,KAApB,GAA4B,CAACJ,GAAG,GAAG,CAAP,IAAYlC,MAAM,CAAC8C,QAA/C,GAA0D9C,MAAM,CAAC+C,WAAjE,GAA+E/C,MAAM,CAACgD,YAA9F;AACAX,YAAAA,UAAU,GAAG,KAAKY,sBAAlB;AACA,iBAAKhD,UAAL,GAAkB,CAAlB;AACA,iBAAKC,QAAL,GAAgB,CAAhB;AACH,WALM,MAKA,IAAIF,MAAM,CAACP,IAAP,KAAgB0C,UAAU,CAACe,IAA/B,EAAqC;AACxC,gBAAIlD,MAAM,CAACmD,SAAP,KAAqBxE,MAAM,CAACyE,aAAP,CAAqBP,UAA9C,EAA0D;AACtD,oBAAMQ,CAAC,GAAGf,KAAK,GAAGtC,MAAM,CAAC+C,WAAf,GAA6B/C,MAAM,CAACgD,YAApC,GAAmDhD,MAAM,CAAC8C,QAApE;AACA,oBAAMQ,GAAG,GAAG,KAAKpD,QAAL,GAAgBqD,IAAI,CAACC,KAAL,CAAWH,CAAC,IAAI,KAAK1C,QAAL,CAAc2B,KAAd,GAAsBtC,MAAM,CAAC8C,QAAjC,CAAZ,CAA5B;AACA,oBAAMW,KAAK,GAAGF,IAAI,CAACG,IAAL,CAAUxB,GAAG,GAAGoB,GAAhB,CAAd;AACAf,cAAAA,MAAM,GAAGkB,KAAK,GAAG,KAAK9C,QAAL,CAAc4B,MAAtB,GAA+B,CAACkB,KAAK,GAAG,CAAT,IAAczD,MAAM,CAACyC,QAApD,GAA+DzC,MAAM,CAAC0C,UAAtE,GAAmF1C,MAAM,CAAC2C,aAAnG;AACAN,cAAAA,UAAU,GAAG,KAAKsB,0BAAlB;AACA,mBAAK1D,UAAL,GAAkB,CAAlB;AACH,aAPD,MAOO;AACH,oBAAM2D,CAAC,GAAGrB,MAAM,GAAGvC,MAAM,CAAC0C,UAAhB,GAA6B1C,MAAM,CAAC2C,aAApC,GAAoD3C,MAAM,CAACyC,QAArE;AACA,oBAAMa,GAAG,GAAG,KAAKpD,QAAL,GAAgBqD,IAAI,CAACC,KAAL,CAAWI,CAAC,IAAI,KAAKjD,QAAL,CAAc4B,MAAd,GAAuBvC,MAAM,CAACyC,QAAlC,CAAZ,CAA5B;AACA,oBAAMgB,KAAK,GAAGF,IAAI,CAACG,IAAL,CAAUxB,GAAG,GAAGoB,GAAhB,CAAd;AACAhB,cAAAA,KAAK,GAAGmB,KAAK,GAAG,KAAK9C,QAAL,CAAc2B,KAAtB,GAA8B,CAACmB,KAAK,GAAG,CAAT,IAAczD,MAAM,CAAC8C,QAAnD,GAA8D9C,MAAM,CAAC+C,WAArE,GAAmF/C,MAAM,CAACgD,YAAlG;AACAX,cAAAA,UAAU,GAAG,KAAKwB,wBAAlB;AACA,mBAAK5D,UAAL,GAAkB,CAAlB;AACH;AACJ,WAjCW,CAkCZ;;;AACA,eAAKE,kBAAL,CAAwB2D,GAAxB,CAA4B,KAAKhE,OAAL,CAAaiC,WAAb,CAAyB,KAAK3B,mBAA9B,CAA5B,EAnCY,CAoCZ;;AACA,cAAI,CAAC,KAAK2D,OAAV,EAAmB;AACf,iBAAKzD,gBAAL,GAAwB,CAAxB;AACA,iBAAKC,eAAL,GAAuB2B,GAAvB;AACH,WAHD,MAGO,IAAI,KAAKjC,UAAL,KAAoB,CAAxB,EAA2B;AAC9B,kBAAM+D,EAAE,GAAG,KAAKnE,QAAL,CAAc0C,MAAd,GAAuBvC,MAAM,CAAC0C,UAA9B,GAA2C1C,MAAM,CAAC2C,aAAlD,GAAkE3C,MAAM,CAACyC,QAApF;AACA,iBAAKlC,eAAL,GAAuB,CAACgD,IAAI,CAACG,IAAL,CAAUM,EAAE,IAAI,KAAKrD,QAAL,CAAc4B,MAAd,GAAuBvC,MAAM,CAACyC,QAAlC,CAAZ,IAA2D,CAA5D,IAAiE,KAAKvC,QAA7F,CAF8B,CAG9B;;AACA,kBAAM+D,EAAE,GAAGV,IAAI,CAACW,GAAL,CAAS,KAAK/D,kBAAL,CAAwBgE,CAAxB,GAA4BnE,MAAM,CAAC0C,UAAnC,GAAgD1C,MAAM,CAACyC,QAAhE,CAAX;AACA,iBAAKnC,gBAAL,GAAwB1B,IAAI,CAACwF,MAAL,CAAYb,IAAI,CAACC,KAAL,CAAWS,EAAE,IAAI,KAAKtD,QAAL,CAAc4B,MAAd,GAAuBvC,MAAM,CAACyC,QAAlC,CAAb,IAA4D,KAAKvC,QAA7E,EAAuF,CAAvF,EAA0F,KAAKc,SAAL,GAAiB,CAA3G,CAAxB;AACH,WANM,MAMA,IAAI,KAAKf,UAAL,KAAoB,CAAxB,EAA2B;AAC9B,kBAAMoE,EAAE,GAAG,KAAKxE,QAAL,CAAcyC,KAAd,GAAsBtC,MAAM,CAAC+C,WAA7B,GAA2C/C,MAAM,CAACgD,YAAlD,GAAiEhD,MAAM,CAAC8C,QAAnF;AACA,iBAAKvC,eAAL,GAAuB,CAACgD,IAAI,CAACG,IAAL,CAAUW,EAAE,IAAI,KAAK1D,QAAL,CAAc2B,KAAd,GAAsBtC,MAAM,CAAC8C,QAAjC,CAAZ,IAA0D,CAA3D,IAAgE,KAAK5C,QAA5F,CAF8B,CAG9B;;AACA,kBAAMoE,EAAE,GAAGf,IAAI,CAACW,GAAL,CAAS,KAAK/D,kBAAL,CAAwBoE,CAAxB,GAA4BvE,MAAM,CAAC+C,WAAnC,GAAiD/C,MAAM,CAAC8C,QAAjE,CAAX;AACA,iBAAKxC,gBAAL,GAAwB1B,IAAI,CAACwF,MAAL,CAAYb,IAAI,CAACC,KAAL,CAAWc,EAAE,IAAI,KAAK3D,QAAL,CAAc2B,KAAd,GAAsBtC,MAAM,CAAC8C,QAAjC,CAAb,IAA2D,KAAK5C,QAA5E,EAAsF,CAAtF,EAAyF,KAAKc,SAAL,GAAiB,CAA1G,CAAxB;AACH,WApDW,CAqDZ;;;AACA,eAAKJ,IAAL,CAAU4D,MAAV,GAAmB,CAAnB,CAtDY,CAuDZ;;AACA,eAAK,IAAIC,CAAC,GAAG,KAAK3E,OAAL,CAAa4E,aAAb,GAA6B,CAA1C,EAA6CD,CAAC,IAAI,CAAlD,EAAqDA,CAAC,EAAtD,EAA0D;AACtD,kBAAME,EAAE,GAAG,KAAK7E,OAAL,CAAa6B,QAAb,CAAsB8C,CAAtB,CAAX;;AACA,gBAAI,KAAK7D,IAAL,CAAU4D,MAAV,GAAmB,KAAKjE,eAAxB,IAA2C7B,OAAO,CAACiG,EAAD,CAAtD,EAA4D;AACxD,mBAAKC,OAAL,CAAaD,EAAb;AACH,aAFD,MAEO;AACHA,cAAAA,EAAE,CAACE,OAAH;AACH;AACJ,WA/DW,CAgEZ;;;AACA,eAAK/E,OAAL,CAAatB,SAAb,CAAuBM,WAAvB,EAAoCgG,cAApC,CAAmDxC,KAAnD,EAA0DC,MAA1D,EAjEY,CAkEZ;AACA;;AACA,eAAK3C,UAAL,CAAgBmF,mBAAhB,IAAuC,KAAKnF,UAAL,CAAgBmF,mBAAhB,CAAoCC,SAApC,CAA8ChG,IAAI,CAACiG,IAAnD,CAAvC,CApEY,CAqEZ;;AACA,eAAKrF,UAAL,CAAgBsF,iBAAhB,IAAqC,KAAKtF,UAAL,CAAgBsF,iBAAhB,CAAkCF,SAAlC,CAA4ChG,IAAI,CAACiG,IAAjD,CAArC,CAtEY,CAuEZ;;AACA,eAAKlE,KAAL,CAAWyD,MAAX,GAAoB,CAApB;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGvC,GAApB,EAAyBuC,CAAC,EAA1B,EAA8B;AAC1B,iBAAK1D,KAAL,CAAWoE,IAAX,CAAgB9C,UAAU,CAAC+C,IAAX,CAAgB,IAAhB,EAAsBX,CAAtB,EAAyB;AAAEY,cAAAA,KAAK,EAAEZ,CAAT;AAAYF,cAAAA,CAAC,EAAE,CAAf;AAAkBJ,cAAAA,CAAC,EAAE;AAArB,aAAzB,CAAhB;AACH;AACJ,SAnJ+C,CAqJhD;;;AACQvB,QAAAA,oBAAoB,CAAC6B,CAAD,EAAYa,GAAZ,EAAsB;AAC9C,gBAAMtF,MAAM,GAAG,KAAKA,MAApB,CAD8C,CAE9C;;AACAsF,UAAAA,GAAG,CAACf,CAAJ,GAAQ,KAAKzD,mBAAL,CAAyByD,CAAjC,CAH8C,CAI9C;;AACA,gBAAMgB,MAAM,GAAG,KAAKxF,gBAAL,CAAsByF,OAAtB,KAAkC,CAAlC,GAAsCxF,MAAM,CAAC0C,UAA7C,GAA0D1C,MAAM,CAAC2C,aAAhF;AACA2C,UAAAA,GAAG,CAACnB,CAAJ,GAAQoB,MAAM,GAAG,KAAK5E,QAAL,CAAc4B,MAAd,IAAwB,IAAI,KAAK7B,aAAL,CAAmB8E,OAA/C,CAAT,GAAmEf,CAAC,GAAG,KAAK9D,QAAL,CAAc4B,MAArF,GAA8FkC,CAAC,GAAGzE,MAAM,CAACyC,QAAjH;;AACA,cAAI,KAAK1C,gBAAL,CAAsByF,OAAtB,KAAkC,CAAtC,EAAyC;AACrCF,YAAAA,GAAG,CAACnB,CAAJ,GAAQ,CAACmB,GAAG,CAACnB,CAAb;AACH;;AACD,iBAAOmB,GAAP;AACH,SAjK+C,CAmKhD;;;AACQrC,QAAAA,sBAAsB,CAACwB,CAAD,EAAYa,GAAZ,EAAsB;AAChD,gBAAMtF,MAAM,GAAG,KAAKA,MAApB,CADgD,CAEhD;;AACA,gBAAMyF,MAAM,GAAG,KAAK1F,gBAAL,CAAsB2F,OAAtB,KAAkC,CAAlC,GAAsC1F,MAAM,CAACgD,YAA7C,GAA4DhD,MAAM,CAAC+C,WAAlF;AACAuC,UAAAA,GAAG,CAACf,CAAJ,GAAQkB,MAAM,GAAG,KAAK9E,QAAL,CAAc2B,KAAd,GAAsB,KAAK5B,aAAL,CAAmBgF,OAAlD,GAA4DjB,CAAC,GAAG,KAAK9D,QAAL,CAAc2B,KAA9E,GAAsFmC,CAAC,GAAGzE,MAAM,CAAC8C,QAAzG;;AACA,cAAI,KAAK/C,gBAAL,CAAsB2F,OAAtB,KAAkC,CAAtC,EAAyC;AACrCJ,YAAAA,GAAG,CAACf,CAAJ,GAAQ,CAACe,GAAG,CAACf,CAAb;AACH,WAP+C,CAQhD;;;AACAe,UAAAA,GAAG,CAACnB,CAAJ,GAAQ,KAAKrD,mBAAL,CAAyBqD,CAAjC;AACA,iBAAOmB,GAAP;AACH,SA/K+C,CAiLhD;;;AACQ3B,QAAAA,0BAA0B,CAAC0B,KAAD,EAAgBC,GAAhB,EAA0B;AACxD,gBAAMtF,MAAM,GAAG,KAAKA,MAApB,CADwD,CAExD;;AACA,gBAAMyF,MAAM,GAAG,CAAC,KAAK1F,gBAAL,CAAsB2F,OAAvB,GAAiC,KAAK3F,gBAAL,CAAsBuC,KAAvD,GAA+DtC,MAAM,CAAC+C,WAArF;AACA,cAAI0B,CAAC,GAAGY,KAAK,GAAG,KAAKnF,QAArB;AACAoF,UAAAA,GAAG,CAACf,CAAJ,GAAQkB,MAAM,GAAG,KAAK9E,QAAL,CAAc2B,KAAd,GAAsB,KAAK5B,aAAL,CAAmBgF,OAAlD,GAA4DjB,CAAC,GAAG,KAAK9D,QAAL,CAAc2B,KAA9E,GAAsFmC,CAAC,GAAGzE,MAAM,CAAC8C,QAAzG,CALwD,CAMxD;;AACA,gBAAMyC,MAAM,GAAG,KAAKxF,gBAAL,CAAsByF,OAAtB,KAAkC,CAAlC,GAAsCxF,MAAM,CAAC0C,UAA7C,GAA0D1C,MAAM,CAAC2C,aAAhF;AACA8B,UAAAA,CAAC,GAAGlB,IAAI,CAACC,KAAL,CAAW6B,KAAK,GAAG,KAAKnF,QAAxB,CAAJ;AACAoF,UAAAA,GAAG,CAACnB,CAAJ,GAAQoB,MAAM,GAAG,KAAK5E,QAAL,CAAc4B,MAAd,GAAuB,KAAK7B,aAAL,CAAmB8E,OAAnD,GAA6Df,CAAC,GAAG,KAAK9D,QAAL,CAAc4B,MAA/E,GAAwFkC,CAAC,GAAGzE,MAAM,CAACyC,QAA3G;;AACA,cAAI,KAAK1C,gBAAL,CAAsByF,OAAtB,KAAkC,CAAtC,EAAyC;AACrCF,YAAAA,GAAG,CAACnB,CAAJ,GAAQ,CAACmB,GAAG,CAACnB,CAAb;AACH;;AACD,iBAAOmB,GAAP;AACH,SAhM+C,CAkMhD;;;AACQzB,QAAAA,wBAAwB,CAACwB,KAAD,EAAgBC,GAAhB,EAA0B;AACtD,gBAAMtF,MAAM,GAAG,KAAKA,MAApB,CADsD,CAEtD;;AACA,gBAAMuF,MAAM,GAAG,CAAC,KAAKxF,gBAAL,CAAsByF,OAAvB,GAAiC,KAAKzF,gBAAL,CAAsBwC,MAAvD,GAAgEvC,MAAM,CAAC2C,aAAtF;AACA,cAAI8B,CAAC,GAAGY,KAAK,GAAG,KAAKnF,QAArB;AACAoF,UAAAA,GAAG,CAACnB,CAAJ,GAAQoB,MAAM,GAAG,KAAK5E,QAAL,CAAc4B,MAAd,GAAuB,KAAK7B,aAAL,CAAmB8E,OAAnD,GAA6Df,CAAC,GAAG,KAAK9D,QAAL,CAAc4B,MAA/E,GAAwFkC,CAAC,GAAGzE,MAAM,CAACyC,QAA3G,CALsD,CAMtD;;AACA,gBAAMgD,MAAM,GAAG,KAAK1F,gBAAL,CAAsB2F,OAAtB,KAAkC,CAAlC,GAAsC1F,MAAM,CAACgD,YAA7C,GAA4DhD,MAAM,CAAC+C,WAAlF;AACA0B,UAAAA,CAAC,GAAGlB,IAAI,CAACC,KAAL,CAAW6B,KAAK,GAAG,KAAKnF,QAAxB,CAAJ;AACAoF,UAAAA,GAAG,CAACf,CAAJ,GAAQkB,MAAM,GAAG,KAAK9E,QAAL,CAAc2B,KAAd,GAAsB,KAAK5B,aAAL,CAAmBgF,OAAlD,GAA4DjB,CAAC,GAAG,KAAK9D,QAAL,CAAc2B,KAA9E,GAAsFmC,CAAC,GAAGzE,MAAM,CAAC8C,QAAzG;;AACA,cAAI,KAAK/C,gBAAL,CAAsB2F,OAAtB,KAAkC,CAAtC,EAAyC;AACrCJ,YAAAA,GAAG,CAACf,CAAJ,GAAQ,CAACe,GAAG,CAACf,CAAb;AACH;;AACD,iBAAOe,GAAP;AACH;;AAEOK,QAAAA,OAAO,GAAG;AACd,gBAAMhB,EAAE,GAAG,KAAK/D,IAAL,CAAUgF,GAAV,MAAmBC,EAAE,CAACC,WAAH,CAAe,KAAKrF,IAApB,EAA0B,KAAKX,OAA/B,CAA9B;AACA6E,UAAAA,EAAE,CAAC3C,MAAH,GAAY,IAAZ;AACA,iBAAO2C,EAAP;AACH;;AAEOC,QAAAA,OAAO,CAACD,EAAD,EAAW;AACtBA,UAAAA,EAAE,CAAC3C,MAAH,GAAY,KAAZ;AACA2C,UAAAA,EAAE,CAACoB,IAAH,GAAU,IAAV;;AACA,cAAI,CAAC,KAAKnF,IAAL,CAAUoF,GAAV,CAAc,MAAd,EAAsBrB,EAAE,CAACsB,IAAzB,CAAL,EAAqC;AACjC,iBAAKrF,IAAL,CAAUuE,IAAV,CAAeR,EAAf;AACH,WAFD,MAEO;AACHlG,YAAAA,KAAK,CAAC,gCAAD,CAAL;AACH;AACJ,SAjO+C,CAmOhD;;;AACQyH,QAAAA,QAAQ,CAACC,KAAD,EAAgB1C,KAAhB,EAA+B;AAC3CA,UAAAA,KAAK,GAAG0C,KAAK,GAAG1C,KAAhB;;AACA,eAAK,IAAIgB,CAAC,GAAG0B,KAAb,EAAoB1B,CAAC,GAAGhB,KAAJ,IAAagB,CAAC,GAAG,KAAKzD,SAA1C,EAAqDyD,CAAC,EAAtD,EAA0D;AACtD,kBAAM2B,CAAC,GAAG,KAAKrF,KAAL,CAAW0D,CAAX,CAAV;;AACA,gBAAI,CAAC2B,CAAC,CAACC,IAAP,EAAa;AACT;AACH,aAJqD,CAKtD;;;AACA,gBAAI,KAAKzE,mBAAT,EAA8B;AAC1B,oBAAMC,EAAE,GAAGuE,CAAC,CAACC,IAAF,CAAOvE,KAAP,CAAa,KAAKF,mBAAlB,EAAuC/C,UAAvC,CAAX;;AACA,kBAAI,CAACgD,EAAL,EAAS,CAAG,CAAZ,MAAkB,IAAIuE,CAAC,CAACE,oBAAN,EAA4B;AAC1CF,gBAAAA,CAAC,CAACE,oBAAF,CAAuBxC,GAAvB,CAA2BjC,EAAE,CAAC/B,OAAH,CAAYiC,WAAZ,CAAwB,KAAKZ,OAA7B,CAA3B;AACH,eAFiB,MAEX;AACHiF,gBAAAA,CAAC,CAACE,oBAAF,GAAyBzE,EAAE,CAAC/B,OAAH,CAAYiC,WAAZ,EAAzB;AACH;AACJ,aAbqD,CActD;;;AACA,iBAAK6C,OAAL,CAAawB,CAAC,CAACC,IAAf;AACAD,YAAAA,CAAC,CAACC,IAAF,GAAS,IAAT;AACAD,YAAAA,CAAC,CAAC5E,SAAF,GAAc,IAAd;AACH;AACJ,SAzP+C,CA2PhD;;;AACQ+E,QAAAA,WAAW,GAAG;AAClB,cAAIC,GAAG,GAAG,KAAKC,iBAAL,GAAyB,CAAzB,GAA6B,KAAKA,iBAAlC,GAAsD,KAAKlG,eAArE;AAAA,cAAsFmG,GAAG,GAAG,CAA5F;;AACA,cAAI,KAAKlG,eAAL,GAAuBgG,GAAvB,GAA6B,KAAKjG,eAAtC,EAAuD;AACnDiG,YAAAA,GAAG,GAAG,KAAKjG,eAAL,GAAuB,KAAKC,eAAlC;AACH;;AACD,cAAIiE,CAAC,GAAG,KAAKnE,gBAAb;AAAA,cAA+BqG,CAAC,GAAGpD,IAAI,CAACqD,GAAL,CAAS,KAAKtG,gBAAL,GAAwB,KAAKC,eAAtC,EAAuD,KAAKS,SAA5D,CAAnC;;AACA,iBAAOyD,CAAC,GAAGkC,CAAJ,IAASD,GAAG,GAAGF,GAAtB,EAA2B;AACvB,kBAAMJ,CAAC,GAAG,KAAKrF,KAAL,CAAW0D,CAAC,EAAZ,CAAV;;AACA,gBAAI2B,CAAC,CAACC,IAAN,EAAY;AACR;AACH;;AACD,kBAAM1B,EAAE,GAAGyB,CAAC,CAACC,IAAF,GAAS,KAAKV,OAAL,EAApB;AACAS,YAAAA,CAAC,CAAC5E,SAAF,GAAc4E,CAAC,CAACC,IAAF,CAAO7H,SAAP,CAAiBM,WAAjB,CAAd;AACA6F,YAAAA,EAAE,CAACkC,WAAH,CAAeT,CAAC,CAAC7B,CAAjB,EAAoB6B,CAAC,CAACjC,CAAtB,EAPuB,CAQvB;;AACA,gBAAI,KAAKvC,mBAAT,EAA8B;AAC1B,oBAAMC,EAAE,GAAGuE,CAAC,CAACC,IAAF,CAAOvE,KAAP,CAAa,KAAKF,mBAAlB,EAAuC/C,UAAvC,CAAX;;AACA,kBAAI,CAACgD,EAAL,EAAS,CAAG,CAAZ,MAAkB,IAAIuE,CAAC,CAACE,oBAAN,EAA4B;AAC1CzE,gBAAAA,EAAE,CAAC/B,OAAH,CAAY+G,WAAZ,CAAwBT,CAAC,CAACE,oBAA1B;AACH,eAFiB,MAEX;AACHzE,gBAAAA,EAAE,CAAC/B,OAAH,CAAY+G,WAAZ,CAAwB,KAAKhG,2BAA7B;AACH;AACJ,aAhBsB,CAiBvB;;;AACA,gBAAI,KAAKI,eAAT,EAA0B;AACtB,mBAAKC,cAAL,GAAsB,KAAKD,eAAL,CAAqBmE,IAArB,CAA0B,KAAKlE,cAA/B,EAA+CyD,EAA/C,EAAmDyB,CAAC,CAACf,KAArD,CAAtB,GAAoF,KAAKpE,eAAL,CAAqB0D,EAArB,EAAyByB,CAAC,CAACf,KAA3B,CAApF;AACH;;AACDqB,YAAAA,GAAG,IAAI,CAAP;AACH;;AACD,eAAKlG,eAAL,IAAwBgG,GAAxB,CA7BkB,CA8BlB;AACA;AACA;AACH,SA7R+C,CA+RhD;;;AACQM,QAAAA,WAAW,GAAG;AAClB,gBAAMV,CAAC,GAAG,KAAKrF,KAAL,CAAW,KAAKT,gBAAhB,CAAV;;AACA,cAAI,CAAC8F,CAAC,CAACC,IAAP,EAAa;AACT,iBAAK7F,eAAL,GAAuB,CAAvB;AACA;AACH;;AACD,cAAI6E,KAAK,GAAG,KAAK/E,gBAAjB;AAAA,cAAmCyG,GAAG,GAAG,CAAzC;AACA,gBAAMvF,SAAS,GAAG4E,CAAC,CAACC,IAAF,CAAO7H,SAAP,CAAiBM,WAAjB,CAAlB;;AACA,cAAI,KAAKmB,UAAL,KAAoB,CAAxB,EAA2B;AACvB;AACA,kBAAM+G,EAAE,GAAG,KAAK7G,kBAAL,CAAwBgE,CAAxB,IAA6BiC,CAAC,CAACjC,CAAF,GAAM3C,SAAS,CAACe,MAAV,GAAmB6D,CAAC,CAAC5E,SAAF,CAAagE,OAAnE,CAAX;;AACA,gBAAIwB,EAAE,GAAG,CAAT,EAAY;AAAE;AACVD,cAAAA,GAAG,GAAG,CAAN;AACH,aAFD,MAEO,IAAIC,EAAE,GAAG,EAAExF,SAAS,CAACe,MAAV,GAAmB,KAAKvC,MAAL,CAAYyC,QAAjC,CAAT,EAAqD;AACxDsE,cAAAA,GAAG,GAAG,CAAC,CAAP;AACH;AACJ,WARD,MAQO,IAAI,KAAK9G,UAAL,KAAoB,CAAxB,EAA2B;AAC9B;AACA,kBAAMgH,EAAE,GAAG,KAAK9G,kBAAL,CAAwBoE,CAAxB,IAA6B6B,CAAC,CAAC7B,CAAF,GAAM/C,SAAS,CAACc,KAAV,GAAkB8D,CAAC,CAAC5E,SAAF,CAAakE,OAAlE,CAAX;;AACA,gBAAIuB,EAAE,GAAG,CAAT,EAAY;AAAE;AACVF,cAAAA,GAAG,GAAG,CAAN;AACH,aAFD,MAEO,IAAIE,EAAE,GAAGzF,SAAS,CAACc,KAAV,GAAkB,KAAKtC,MAAL,CAAY8C,QAAvC,EAAiD;AACpDiE,cAAAA,GAAG,GAAG,CAAC,CAAP;AACH;AACJ;;AACD,cAAIA,GAAG,KAAK,CAAZ,EAAe;AACX;AACH,WAFD,MAEO,IAAIA,GAAG,KAAK,CAAZ,EAAe;AAClB,iBAAKb,QAAL,CAAcb,KAAd,EAAqB,KAAKnF,QAA1B;AACAmF,YAAAA,KAAK,GAAG9B,IAAI,CAACqD,GAAL,CAASvB,KAAK,GAAG,KAAKnF,QAAtB,EAAgC,KAAKc,SAAL,GAAiB,CAAjD,CAAR,CAFkB,CAGlB;AACH,WAJM,MAIA,IAAI+F,GAAG,KAAK,CAAC,CAAb,EAAgB;AACnB,kBAAMG,GAAG,GAAG3D,IAAI,CAAC4D,GAAL,CAAS9B,KAAK,GAAG,KAAKnF,QAAtB,EAAgC,CAAhC,CAAZ;;AACA,gBAAImF,KAAK,KAAK6B,GAAd,EAAmB;AACf,mBAAKhB,QAAL,CAAcb,KAAK,GAAG,KAAK9E,eAAb,GAA+B,KAAKL,QAAlD,EAA4D,KAAKA,QAAjE;AACAmF,cAAAA,KAAK,GAAG6B,GAAR,CAFe,CAGf;AACH;AACJ;;AACD,cAAI,KAAK5G,gBAAL,KAA0B+E,KAA9B,EAAqC;AACjC,iBAAK/E,gBAAL,GAAwB+E,KAAxB;AACA,iBAAK7E,eAAL,GAAuB,CAAvB;AACH;AACJ;;AAED4G,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf;AACA,cAAI,KAAKtD,OAAL,IAAgB,CAAC,KAAK5D,kBAAL,CAAwBmH,MAAxB,CAA+B,KAAKxH,OAAL,CAAaiC,WAAb,CAAyB,KAAK3B,mBAA9B,CAA/B,CAArB,EAAyG;AACrG,iBAAKD,kBAAL,CAAwB2D,GAAxB,CAA4B,KAAK1D,mBAAjC;AACA,iBAAK0G,WAAL;AACH,WALc,CAMf;;;AACA,cAAI,KAAKtG,eAAL,GAAuB,KAAKD,eAAhC,EAAiD;AAC7C,iBAAKF,UAAL,IAAmB,CAAnB;;AACA,gBAAI,KAAKA,UAAL,IAAmB,KAAKkH,UAA5B,EAAwC;AACpC,mBAAKlH,UAAL,GAAkB,CAAlB;AACA,mBAAKkG,WAAL;AACH;AACJ;AACJ,SA3V+C,CA6VhD;;;AACOiB,QAAAA,IAAI,CAACC,GAAD,EAAcC,EAAd,EAA6BC,MAA7B,EAA2C;AAClD,eAAK3G,SAAL,GAAiByG,GAAjB;AACA,eAAKxG,eAAL,GAAuByG,EAAvB;AACA,eAAKxG,cAAL,GAAsByG,MAAtB;AACA,eAAK1F,KAAL;AACA,eAAKzB,eAAL,GAAuB,CAAvB;AACH,SApW+C,CAsWhD;;;AACOoH,QAAAA,SAAS,GAAG,CAElB;;AAzW+C,O;;;;;iBAGrB,I;;;;;;;iBAEW,E;;;;;;;iBAET,C;;;;;;;iBAEO,C", "sourcesContent": ["import { _decorator, CCBoolean, CCInteger, CCString, Component, error, isValid, Layout, misc, Node, ScrollView, Size, UITransform, v3, Vec3 } from \"cc\";\r\n\r\nconst { ccclass, property, menu, requireComponent, disallowMultiple, executionOrder } = _decorator;\r\n\r\n@ccclass\r\n@disallowMultiple()\r\n@requireComponent(ScrollView)\r\n@menu('自定义组件/ScrollViewEx')\r\n@executionOrder(-100)\r\nexport default class ScrollViewEx extends Component {\r\n\r\n    @property(CCBoolean)\r\n    private virtual: boolean = true //动态列表\r\n    @property({ tooltip: '子的ScrollView,如果有' })\r\n    private childScrollViewName: string = '' //子列表\r\n    @property({ type: CCInteger, range: [1, 10, 1], tooltip: '多少帧渲染一次', slide: true })\r\n    private updateRate: number = 1 //渲染频率 多少帧渲染一个\r\n    @property({ type: CCInteger, range: [0, 12, 1], tooltip: '一次渲染多少个,0渲染所有', slide: true })\r\n    private updateRenderCount: number = 1 //分帧渲染个数\r\n\r\n    private scrollView: ScrollView = null!\r\n    private viewSize: Size = null!\r\n    private content: Node = null!\r\n    private contentTransform: UITransform = null!\r\n    private layout: Layout = null!\r\n    private scrollType: number = 0 //滚动类型 0.纵向 1.横向\r\n    private rowCount: number = 1 //一列或一行的个数\r\n    private preContentPosition = v3()\r\n    private tempContentPosition = v3()\r\n\r\n    private frameCount: number = 0 //多少帧\r\n    private renderStartIndex: number = 0 //渲染起点下标\r\n    private needRenderCount: number = 0 //需要渲染的item个数\r\n    private currRenderCount: number = 0 //当前渲染的item个数\r\n\r\n    private item: Node = null!\r\n    private itemTransform: UITransform = null!\r\n    private itemSize: Size = null!\r\n    private pool: Node[] = []\r\n    private childContentDefaultPosition = v3() //子ScrollView的默认位置\r\n    private itemDefaultPosition = v3() //\r\n\r\n    private datas: { index: number, node: Node | null, transform: UITransform | null, x: number, y: number, childContentPosition?: Vec3 }[] = []\r\n    private dataCount: number = 0\r\n    private setItemCallback: Function = null!\r\n    private callbackTarget: any = null\r\n\r\n    private tempVec = v3()\r\n\r\n    onLoad() {\r\n        this.init()\r\n    }\r\n\r\n    private init() {\r\n        if (this.scrollView) {\r\n            return\r\n        }\r\n        this.scrollView = this.getComponent(ScrollView)!\r\n        this.content = this.scrollView.content!\r\n        this.contentTransform = this.content.getComponent(UITransform)!\r\n        this.viewSize = this.content?.parent?.transform.contentSize!\r\n        this.layout = this.content.getComponent(Layout)!\r\n        if (!this.layout) {\r\n            return error('需要Layout组件')\r\n        }\r\n        this.layout.enabled = false\r\n        this.item = this.content.children[0]\r\n        if (!this.item) {\r\n            return error('必须满足content中有一个可拷贝的节点')\r\n        }\r\n        if (this.childScrollViewName) {\r\n            const sv = this.item.Child(this.childScrollViewName, ScrollView)\r\n            sv && sv.content?.getPosition(this.childContentDefaultPosition)\r\n        }\r\n        this.item.getPosition(this.itemDefaultPosition)\r\n        this.itemTransform = this.item.getComponent(UITransform)!\r\n        this.itemSize = this.item.transform.contentSize\r\n        this.item.active = false\r\n    }\r\n\r\n    private reset() {\r\n        if (!this.scrollView) {\r\n            this.init()\r\n        }\r\n        const sum = this.dataCount\r\n        const layout = this.layout, LayoutType = Layout.Type\r\n        let setItemPos: Function = null!, width = this.contentTransform.width, height = this.contentTransform.height\r\n        if (layout.type === LayoutType.VERTICAL) {\r\n            height = sum * this.itemSize.height + (sum - 1) * layout.spacingY + layout.paddingTop + layout.paddingBottom\r\n            setItemPos = this.setItemPosByVertical\r\n            this.scrollType = 0\r\n            this.rowCount = 1\r\n        } else if (layout.type === LayoutType.HORIZONTAL) {\r\n            width = sum * this.itemSize.width + (sum - 1) * layout.spacingX + layout.paddingLeft + layout.paddingRight\r\n            setItemPos = this.setItemPosByHorizontal\r\n            this.scrollType = 1\r\n            this.rowCount = 1\r\n        } else if (layout.type === LayoutType.GRID) {\r\n            if (layout.startAxis === Layout.AxisDirection.HORIZONTAL) {\r\n                const w = width - layout.paddingLeft - layout.paddingRight + layout.spacingX\r\n                const row = this.rowCount = Math.floor(w / (this.itemSize.width + layout.spacingX))\r\n                const count = Math.ceil(sum / row)\r\n                height = count * this.itemSize.height + (count - 1) * layout.spacingY + layout.paddingTop + layout.paddingBottom\r\n                setItemPos = this.setItemPosByGridHorizontal\r\n                this.scrollType = 0\r\n            } else {\r\n                const h = height - layout.paddingTop - layout.paddingBottom + layout.spacingY\r\n                const row = this.rowCount = Math.floor(h / (this.itemSize.height + layout.spacingY))\r\n                const count = Math.ceil(sum / row)\r\n                width = count * this.itemSize.width + (count - 1) * layout.spacingX + layout.paddingLeft + layout.paddingRight\r\n                setItemPos = this.setItemPosByGridVertical\r\n                this.scrollType = 1\r\n            }\r\n        }\r\n        //\r\n        this.preContentPosition.set(this.content.getPosition(this.tempContentPosition))\r\n        // 设置需要显示的个数\r\n        if (!this.virtual) {\r\n            this.renderStartIndex = 0\r\n            this.needRenderCount = sum\r\n        } else if (this.scrollType === 0) {\r\n            const vh = this.viewSize.height - layout.paddingTop - layout.paddingBottom + layout.spacingY\r\n            this.needRenderCount = (Math.ceil(vh / (this.itemSize.height + layout.spacingY)) + 1) * this.rowCount\r\n            // 下面计算开始位置 先默认 anchorY = 1\r\n            const cy = Math.abs(this.preContentPosition.y - layout.paddingTop + layout.spacingY)\r\n            this.renderStartIndex = misc.clampf(Math.floor(cy / (this.itemSize.height + layout.spacingY)) * this.rowCount, 0, this.dataCount - 1)\r\n        } else if (this.scrollType === 1) {\r\n            const vw = this.viewSize.width - layout.paddingLeft - layout.paddingRight + layout.spacingX\r\n            this.needRenderCount = (Math.ceil(vw / (this.itemSize.width + layout.spacingX)) + 1) * this.rowCount\r\n            // 下面计算开始位置 先默认 anchorY = 0\r\n            const cx = Math.abs(this.preContentPosition.x + layout.paddingLeft - layout.spacingX)\r\n            this.renderStartIndex = misc.clampf(Math.floor(cx / (this.itemSize.width + layout.spacingX)) * this.rowCount, 0, this.dataCount - 1)\r\n        }\r\n        // 清空content\r\n        this.pool.length = 0\r\n        // this.items.length = 0\r\n        for (let i = this.content.ChildrenCount - 1; i >= 0; i--) {\r\n            const it = this.content.children[i]\r\n            if (this.pool.length < this.needRenderCount && isValid(it)) {\r\n                this.putItem(it)\r\n            } else {\r\n                it.destroy()\r\n            }\r\n        }\r\n        // 设置content大小\r\n        this.content.Component(UITransform).setContentSize(width, height)\r\n        // 调整ScrollBar位置\r\n        // @ts-ignore\r\n        this.scrollView.horizontalScrollBar && this.scrollView.horizontalScrollBar._onScroll(Vec3.ZERO)\r\n        // @ts-ignore\r\n        this.scrollView.verticalScrollBar && this.scrollView.verticalScrollBar._onScroll(Vec3.ZERO)\r\n        // 预设item坐标\r\n        this.datas.length = 0\r\n        for (let i = 0; i < sum; i++) {\r\n            this.datas.push(setItemPos.call(this, i, { index: i, x: 0, y: 0 }))\r\n        }\r\n    }\r\n\r\n    // VERTICAL\r\n    private setItemPosByVertical(i: number, out: any) {\r\n        const layout = this.layout\r\n        // x\r\n        out.x = this.itemDefaultPosition.x\r\n        // y\r\n        const startY = this.contentTransform.anchorY === 1 ? layout.paddingTop : layout.paddingBottom\r\n        out.y = startY + this.itemSize.height * (1 - this.itemTransform.anchorY) + i * this.itemSize.height + i * layout.spacingY\r\n        if (this.contentTransform.anchorY === 1) {\r\n            out.y = -out.y\r\n        }\r\n        return out\r\n    }\r\n\r\n    // HORIZONTAL\r\n    private setItemPosByHorizontal(i: number, out: any) {\r\n        const layout = this.layout\r\n        // x\r\n        const startX = this.contentTransform.anchorX === 1 ? layout.paddingRight : layout.paddingLeft\r\n        out.x = startX + this.itemSize.width * this.itemTransform.anchorX + i * this.itemSize.width + i * layout.spacingX\r\n        if (this.contentTransform.anchorX === 1) {\r\n            out.x = -out.x\r\n        }\r\n        // y\r\n        out.y = this.itemDefaultPosition.y\r\n        return out\r\n    }\r\n\r\n    // GRID.HORIZONTAL 需要content.anchorY != 0.5\r\n    private setItemPosByGridHorizontal(index: number, out: any) {\r\n        const layout = this.layout\r\n        // x\r\n        const startX = -this.contentTransform.anchorX * this.contentTransform.width + layout.paddingLeft\r\n        let i = index % this.rowCount\r\n        out.x = startX + this.itemSize.width * this.itemTransform.anchorX + i * this.itemSize.width + i * layout.spacingX\r\n        // y\r\n        const startY = this.contentTransform.anchorY === 1 ? layout.paddingTop : layout.paddingBottom\r\n        i = Math.floor(index / this.rowCount)\r\n        out.y = startY + this.itemSize.height * this.itemTransform.anchorY + i * this.itemSize.height + i * layout.spacingY\r\n        if (this.contentTransform.anchorY === 1) {\r\n            out.y = -out.y\r\n        }\r\n        return out\r\n    }\r\n\r\n    // GRID.VERTICAL\r\n    private setItemPosByGridVertical(index: number, out: any) {\r\n        const layout = this.layout\r\n        // y\r\n        const startY = -this.contentTransform.anchorY * this.contentTransform.height + layout.paddingBottom\r\n        let i = index % this.rowCount\r\n        out.y = startY + this.itemSize.height * this.itemTransform.anchorY + i * this.itemSize.height + i * layout.spacingY\r\n        // x\r\n        const startX = this.contentTransform.anchorX === 1 ? layout.paddingRight : layout.paddingLeft\r\n        i = Math.floor(index / this.rowCount)\r\n        out.x = startX + this.itemSize.width * this.itemTransform.anchorX + i * this.itemSize.width + i * layout.spacingX\r\n        if (this.contentTransform.anchorX === 1) {\r\n            out.x = -out.x\r\n        }\r\n        return out\r\n    }\r\n\r\n    private getItem() {\r\n        const it = this.pool.pop() || mc.instantiate(this.item, this.content)\r\n        it.active = true\r\n        return it\r\n    }\r\n\r\n    private putItem(it: Node) {\r\n        it.active = false\r\n        it.Data = null\r\n        if (!this.pool.has('uuid', it.uuid)) {\r\n            this.pool.push(it)\r\n        } else {\r\n            error('ScrollViewEx.putItem has uuid?')\r\n        }\r\n    }\r\n\r\n    // 删除节点\r\n    private delItems(start: number, count: number) {\r\n        count = start + count\r\n        for (let i = start; i < count && i < this.dataCount; i++) {\r\n            const d = this.datas[i]\r\n            if (!d.node) {\r\n                continue\r\n            }\r\n            // 如果有子列表 这里记录他的位置\r\n            if (this.childScrollViewName) {\r\n                const sv = d.node.Child(this.childScrollViewName, ScrollView)\r\n                if (!sv) { } else if (d.childContentPosition) {\r\n                    d.childContentPosition.set(sv.content!.getPosition(this.tempVec))\r\n                } else {\r\n                    d.childContentPosition = sv.content!.getPosition()\r\n                }\r\n            }\r\n            // 回收\r\n            this.putItem(d.node)\r\n            d.node = null\r\n            d.transform = null\r\n        }\r\n    }\r\n\r\n    // 开始创建item\r\n    private updateItems() {\r\n        let cnt = this.updateRenderCount > 0 ? this.updateRenderCount : this.needRenderCount, cur = 0\r\n        if (this.currRenderCount + cnt > this.needRenderCount) {\r\n            cnt = this.needRenderCount - this.currRenderCount\r\n        }\r\n        let i = this.renderStartIndex, l = Math.min(this.renderStartIndex + this.needRenderCount, this.dataCount)\r\n        while (i < l && cur < cnt) {\r\n            const d = this.datas[i++]\r\n            if (d.node) {\r\n                continue\r\n            }\r\n            const it = d.node = this.getItem()\r\n            d.transform = d.node.Component(UITransform)\r\n            it.setPosition(d.x, d.y)\r\n            // 如果有子列表 这里还原他的位置\r\n            if (this.childScrollViewName) {\r\n                const sv = d.node.Child(this.childScrollViewName, ScrollView)\r\n                if (!sv) { } else if (d.childContentPosition) {\r\n                    sv.content!.setPosition(d.childContentPosition)\r\n                } else {\r\n                    sv.content!.setPosition(this.childContentDefaultPosition)\r\n                }\r\n            }\r\n            // 回调\r\n            if (this.setItemCallback) {\r\n                this.callbackTarget ? this.setItemCallback.call(this.callbackTarget, it, d.index) : this.setItemCallback(it, d.index)\r\n            }\r\n            cur += 1\r\n        }\r\n        this.currRenderCount += cnt\r\n        // if (this.currRenderCount === this.needRenderCount) {\r\n        //     log(this.content.ChildrenCount)\r\n        // }\r\n    }\r\n\r\n    // 检测是否有item被滚动出去或进来\r\n    private checkScroll() {\r\n        const d = this.datas[this.renderStartIndex]\r\n        if (!d.node) {\r\n            this.currRenderCount = 0\r\n            return\r\n        }\r\n        let index = this.renderStartIndex, dir = 0\r\n        const transform = d.node.Component(UITransform)\r\n        if (this.scrollType === 0) {\r\n            // 这里先默认 content.anchorY = 1 和 view.anchorY = 1\r\n            const sy = this.preContentPosition.y + (d.y - transform.height * d.transform!.anchorY)\r\n            if (sy > 0) { //出去了\r\n                dir = 1\r\n            } else if (sy < -(transform.height + this.layout.spacingY)) {\r\n                dir = -1\r\n            }\r\n        } else if (this.scrollType === 1) {\r\n            // 这里先默认 content.anchorX = 0 和 view.anchorX = 0\r\n            const sx = this.preContentPosition.x + (d.x + transform.width * d.transform!.anchorX)\r\n            if (sx < 0) { //出去了\r\n                dir = 1\r\n            } else if (sx > transform.width + this.layout.spacingX) {\r\n                dir = -1\r\n            }\r\n        }\r\n        if (dir === 0) {\r\n            return\r\n        } else if (dir === 1) {\r\n            this.delItems(index, this.rowCount)\r\n            index = Math.min(index + this.rowCount, this.dataCount - 1)\r\n            // log('out ........', index)\r\n        } else if (dir === -1) {\r\n            const idx = Math.max(index - this.rowCount, 0)\r\n            if (index !== idx) {\r\n                this.delItems(index + this.needRenderCount - this.rowCount, this.rowCount)\r\n                index = idx\r\n                // log('in ........', index)\r\n            }\r\n        }\r\n        if (this.renderStartIndex !== index) {\r\n            this.renderStartIndex = index\r\n            this.currRenderCount = 0\r\n        }\r\n    }\r\n\r\n    update(dt: number) {\r\n        // 虚拟列表检测 滑动\r\n        if (this.virtual && !this.preContentPosition.equals(this.content.getPosition(this.tempContentPosition))) {\r\n            this.preContentPosition.set(this.tempContentPosition)\r\n            this.checkScroll()\r\n        }\r\n        // 渲染\r\n        if (this.currRenderCount < this.needRenderCount) {\r\n            this.frameCount += 1\r\n            if (this.frameCount >= this.updateRate) {\r\n                this.frameCount = 0\r\n                this.updateItems()\r\n            }\r\n        }\r\n    }\r\n\r\n    // 填充列表\r\n    public list(len: number, cb?: Function, target?: any) {\r\n        this.dataCount = len\r\n        this.setItemCallback = cb!\r\n        this.callbackTarget = target\r\n        this.reset()\r\n        this.currRenderCount = 0\r\n    }\r\n\r\n    // 添加\r\n    public addByList() {\r\n\r\n    }\r\n}\r\n"]}