{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts"], "names": ["_decorator", "Prefab", "AnimalCmpt", "EventType", "AnimPlayCmpt", "RoleCmpt", "RoleObj", "viewHelper", "ccclass", "GameWindCtrl", "mc", "BaseWindCtrl", "mapNode_", "itemsNode_", "flutterNode_", "animPlay", "animals", "animalMap", "myRole", "model", "listenEventMaps", "UPDATE_MY_BATTLE_AREA", "onUpdateMyBattleArea", "enter", "PLAY_FLUTTER_HP", "onPlayFlutterHp", "REMOVE_ANIMAL", "onRemoveAnimal", "onCreate", "node", "addComponent", "getModel", "createRole", "init", "key", "onEnter", "data", "playAnimation", "updateEncounterInfo", "onClean", "leave", "clean", "assetsMgr", "releaseTempResByTag", "onClickGoon", "event", "showPnl", "updateMyBattleArea", "find", "m", "uid", "playFlutter", "ut", "convertToNodeAR", "release", "cleanAnimal", "remove", "update", "dt", "index", "<PERSON><PERSON><PERSON><PERSON>", "pfb", "loadTempRes", "getPrefabUrl", "pos", "Child", "getPosition", "role", "instantiate", "getComponent", "createAnimal", "rootType", "animal", "isDie", "resync", "push", "getBattleAnimals", "for<PERSON>ach", "runBattle", "Promise", "all", "getEnemyAnimals", "map", "battleBegin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,M,OAAAA,M;;AAEzBC,MAAAA,U;;AAEAC,MAAAA,S;;AACAC,MAAAA,Y;;AACAC,MAAAA,Q;;AACAC,MAAAA,O;;AACEC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcR,U;;yBAGCS,Y,GADpBD,O,UAAD,MACqBC,YADrB,SAC0CC,EAAE,CAACC,YAD7C,CAC0D;AAAA;AAAA;AAEtD;AAFsD,eAG9CC,QAH8C,GAG7B,IAH6B;AAGxB;AAHwB,eAI9CC,UAJ8C,GAI3B,IAJ2B;AAItB;AAJsB,eAK9CC,YAL8C,GAKzB,IALyB;AAKpB;AAClC;AANsD,eAQ9CC,QAR8C,GAQrB,IARqB;AAAA,eAU9CC,OAV8C,GAUtB,EAVsB;AAAA,eAW9CC,SAX8C,GAWH,EAXG;AAAA,eAa9CC,MAb8C,GAa3B,IAb2B;AAAA,eAe9CC,KAf8C,GAe3B,IAf2B;AAAA;;AAiB/CC,QAAAA,eAAe,GAAG;AACrB,iBAAO,CACH;AAAE,aAAC;AAAA;AAAA,wCAAUC,qBAAX,GAAmC,KAAKC,oBAA1C;AAAgEC,YAAAA,KAAK,EAAE;AAAvE,WADG,EAEH;AAAE,aAAC;AAAA;AAAA,wCAAUC,eAAX,GAA6B,KAAKC,eAApC;AAAqDF,YAAAA,KAAK,EAAE;AAA5D,WAFG,EAGH;AAAE,aAAC;AAAA;AAAA,wCAAUG,aAAX,GAA2B,KAAKC,cAAlC;AAAkDJ,YAAAA,KAAK,EAAE;AAAzD,WAHG,CAAP;AAKH;;AAEoB,cAARK,QAAQ,GAAG;AACpB,eAAKb,QAAL,GAAgB,KAAKc,IAAL,CAAUC,YAAV;AAAA;AAAA,2CAAhB;AACA,eAAKX,KAAL,GAAa,KAAKY,QAAL,CAAc,MAAd,CAAb;AACA,eAAKb,MAAL,GAAc,MAAM,KAAKc,UAAL,CAAgB;AAAA;AAAA,oCAAcC,IAAd,CAAmB,MAAnB,CAAhB,EAA4C,CAA5C,CAApB;AACA,gBAAM,KAAKlB,QAAL,CAAckB,IAAd,CAAmB,KAAKC,GAAxB,CAAN;AACH;;AAEMC,QAAAA,OAAO,CAACC,IAAD,EAAY;AACtB,eAAKjB,KAAL,CAAWI,KAAX;AACA,eAAKL,MAAL,CAAYmB,aAAZ,CAA0B,MAA1B;AACA,eAAKC,mBAAL;AACH;;AAEMC,QAAAA,OAAO,GAAG;AACb,eAAKpB,KAAL,CAAWqB,KAAX;AACA,eAAKzB,QAAL,CAAc0B,KAAd;AACA,eAAK1B,QAAL,GAAgB,IAAhB;AACA2B,UAAAA,SAAS,CAACC,mBAAV,CAA8B,KAAKT,GAAnC;AACH,SA3CqD,CA6CtD;AACA;AAEA;;;AACAU,QAAAA,WAAW,CAACC,KAAD,EAAoBT,IAApB,EAAkC;AACzC,eAAKlB,MAAL,CAAYmB,aAAZ,CAA0B,QAA1B,EAAoC,MAAM,KAAKnB,MAAL,CAAYmB,aAAZ,CAA0B,MAA1B,CAA1C,EADyC,CAEzC;;AACA;AAAA;AAAA,wCAAWS,OAAX,CAAmB,WAAnB;AACH,SArDqD,CAsDtD;AACA;;;AAEQxB,QAAAA,oBAAoB,GAAG;AAC3B,eAAKyB,kBAAL;AACH,SA3DqD,CA6DtD;;;AACQtB,QAAAA,eAAe,CAACW,IAAD,EAAY;AAAA;;AAC/B,gBAAMP,IAAI,yBAAG,KAAKb,OAAL,CAAagC,IAAb,CAAkBC,CAAC,IAAIA,CAAC,CAACC,GAAF,KAAUd,IAAI,CAACc,GAAtC,CAAH,qBAAG,mBAA4CrB,IAAzD;;AACA,cAAIA,IAAJ,EAAU;AACN,iBAAKd,QAAL,CAAcoC,WAAd,CAA0Bf,IAA1B,EAAgC,KAAKtB,YAArC,EAAmDsC,EAAE,CAACC,eAAH,CAAmBxB,IAAnB,EAAyB,KAAKf,YAA9B,CAAnD;AACH;AACJ;;AAEOa,QAAAA,cAAc,CAACuB,GAAD,EAAcI,OAAd,EAAgC;AAClD,eAAKC,WAAL,CAAiB,KAAKvC,OAAL,CAAawC,MAAb,CAAoB,KAApB,EAA2BN,GAA3B,CAAjB,EAAkDI,OAAlD;AACH,SAvEqD,CAwEtD;;;AAEAG,QAAAA,MAAM,CAACC,EAAD,EAAa;AAAA;;AACf,8BAAKvC,KAAL,yBAAYsC,MAAZ,CAAmBC,EAAnB;AACH,SA5EqD,CA8EtD;;;AACwB,cAAV1B,UAAU,CAACI,IAAD,EAAgBuB,KAAhB,EAA+B;AACnD,cAAI,CAAC,KAAKC,OAAN,IAAiB,CAACxB,IAAtB,EAA4B;AACxB,mBAAO,IAAP;AACH;;AACD,gBAAMyB,GAAG,GAAG,MAAMnB,SAAS,CAACoB,WAAV,CAAsB1B,IAAI,CAAC2B,YAAL,EAAtB,EAA2C9D,MAA3C,EAAmD,KAAKiC,GAAxD,CAAlB;;AACA,cAAI,CAAC2B,GAAD,IAAQ,CAAC,KAAKD,OAAlB,EAA2B;AACvB,mBAAO,IAAP;AACH;;AACD,gBAAMI,GAAG,GAAG,KAAKpD,QAAL,CAAcqD,KAAd,CAAoB,cAAcN,KAAlC,EAAyCO,WAAzC,EAAZ;AACA,gBAAMC,IAAI,GAAG,MAAMzD,EAAE,CAAC0D,WAAH,CAAeP,GAAf,EAAoB,KAAKhD,UAAzB,EAAqCwD,YAArC;AAAA;AAAA,oCAA4DpC,IAA5D,CAAiEG,IAAjE,EAAuE4B,GAAvE,EAA4E,KAAK9B,GAAjF,CAAnB;AACA,iBAAOiC,IAAP;AACH,SA1FqD,CA4FtD;;;AAC0B,cAAZG,YAAY,CAAClC,IAAD,EAAkBmC,QAAlB,EAAoC;AAC1D,cAAI,CAAC,KAAKX,OAAN,IAAiB,CAACxB,IAAtB,EAA4B;AACxB,mBAAO,IAAP;AACH;;AACD,cAAIoC,MAAM,GAAG,KAAKxD,OAAL,CAAagC,IAAb,CAAkBC,CAAC,IAAIA,CAAC,CAACC,GAAF,KAAUd,IAAI,CAACc,GAAtC,CAAb;;AACA,cAAI,CAACsB,MAAL,EAAa,CACZ,CADD,MACO,IAAIpC,IAAI,CAACqC,KAAL,EAAJ,EAAkB;AACrB,iBAAKzD,OAAL,CAAawC,MAAb,CAAoB,KAApB,EAA2BpB,IAAI,CAACc,GAAhC;AACA,iBAAKK,WAAL,CAAiBiB,MAAjB;AACA,mBAAO,IAAP;AACH,WAJM,MAIA;AACH,mBAAOA,MAAM,CAACE,MAAP,CAActC,IAAd,CAAP;AACH;;AACD,gBAAMyB,GAAG,GAAG,MAAMnB,SAAS,CAACoB,WAAV,CAAsB1B,IAAI,CAAC2B,YAAL,EAAtB,EAA2C9D,MAA3C,EAAmD,KAAKiC,GAAxD,CAAlB;;AACA,cAAI,CAAC2B,GAAD,IAAQ,CAAC,KAAKD,OAAlB,EAA2B;AACvB,mBAAO,IAAP;AACH,WAFD,MAEO,IAAI,KAAK3C,SAAL,CAAemB,IAAI,CAACc,GAApB,CAAJ,EAA8B;AACjC,mBAAO,IAAP,CADiC,CACrB;AACf,WAlByD,CAmB1D;;;AACA,gBAAMA,GAAG,GAAGd,IAAI,CAACc,GAAjB,CApB0D,CAqB1D;;AACA,cAAI,CAACd,IAAD,IAASA,IAAI,CAACqC,KAAL,EAAb,EAA2B;AACvB,mBAAO,IAAP;AACH;;AACDD,UAAAA,MAAM,GAAG9D,EAAE,CAAC0D,WAAH,CAAeP,GAAf,EAAoB,KAAKhD,UAAzB,EAAqCwD,YAArC;AAAA;AAAA,wCAA8DpC,IAA9D,CAAmEG,IAAnE,EAAyE,KAAKF,GAA9E,CAAT;AACA,eAAKlB,OAAL,CAAa2D,IAAb,CAAkBH,MAAlB;AACA,eAAKvD,SAAL,CAAemB,IAAI,CAACc,GAApB,IAA2BsB,MAA3B;AACA,iBAAOA,MAAP;AACH;;AAEOjB,QAAAA,WAAW,CAACiB,MAAD,EAAqBlB,OAArB,EAAwC;AACvD,cAAIkB,MAAJ,EAAY;AACR,mBAAO,KAAKvD,SAAL,CAAeuD,MAAM,CAACtB,GAAtB,CAAP;AACAsB,YAAAA,MAAM,CAAC/B,KAAP,CAAaa,OAAb;AACH;AACJ,SAjIqD,CAmItD;;;AACQP,QAAAA,kBAAkB,GAAG;AACzB,eAAK5B,KAAL,CAAWyD,gBAAX,GAA8BC,OAA9B,CAAsC5B,CAAC,IAAI,KAAKqB,YAAL,CAAkBrB,CAAlB,EAAqB,IAArB,CAA3C;AACH,SAtIqD,CAwItD;;;AACuB,cAAT6B,SAAS,GAAG;AACtB,gBAAMC,OAAO,CAACC,GAAR,CAAY,KAAK7D,KAAL,CAAW8D,eAAX,GAA6BC,GAA7B,CAAiCjC,CAAC,IAAI,KAAKqB,YAAL,CAAkBrB,CAAlB,EAAqB,OAArB,CAAtC,CAAZ,CAAN;AACA,eAAK9B,KAAL,CAAWgE,WAAX;AACH,SA5IqD,CA8ItD;;;AACQ7C,QAAAA,mBAAmB,GAAG,CAC1B;AACH;;AAjJqD,O", "sourcesContent": ["import { _decorator, log, Node, Prefab, EventTouch } from \"cc\";\nimport AnimalObj from \"../../model/game/AnimalObj\";\nimport AnimalCmpt from \"./AnimalCmpt\";\nimport GameModel from \"../../model/game/GameModel\";\nimport EventType from \"../../common/event/EventType\";\nimport AnimPlayCmpt from \"./AnimPlayCmpt\";\nimport RoleCmpt from \"./RoleCmpt\";\nimport RoleObj from \"../../model/game/RoleObj\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nconst { ccclass } = _decorator;\n\n@ccclass\nexport default class GameWindCtrl extends mc.BaseWindCtrl {\n\n    //@autocode property begin\n    private mapNode_: Node = null // path://map_n\n    private itemsNode_: Node = null // path://items_n\n    private flutterNode_: Node = null // path://flutter_n\n    //@end\n\n    private animPlay: AnimPlayCmpt = null\n\n    private animals: AnimalCmpt[] = []\n    private animalMap: { [key: string]: AnimalCmpt } = {}\n\n    private myRole: RoleCmpt = null\n\n    private model: GameModel = null\n\n    public listenEventMaps() {\n        return [\n            { [EventType.UPDATE_MY_BATTLE_AREA]: this.onUpdateMyBattleArea, enter: true },\n            { [EventType.PLAY_FLUTTER_HP]: this.onPlayFlutterHp, enter: true },\n            { [EventType.REMOVE_ANIMAL]: this.onRemoveAnimal, enter: true },\n        ]\n    }\n\n    public async onCreate() {\n        this.animPlay = this.node.addComponent(AnimPlayCmpt)\n        this.model = this.getModel('game')\n        this.myRole = await this.createRole(new RoleObj().init(110001), 0)\n        await this.animPlay.init(this.key)\n    }\n\n    public onEnter(data: any) {\n        this.model.enter()\n        this.myRole.playAnimation('idle')\n        this.updateEncounterInfo()\n    }\n\n    public onClean() {\n        this.model.leave()\n        this.animPlay.clean()\n        this.animPlay = null\n        assetsMgr.releaseTempResByTag(this.key)\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://map_n/goon_be\n    onClickGoon(event: EventTouch, data: string) {\n        this.myRole.playAnimation('attack', () => this.myRole.playAnimation('idle'))\n        // viewHelper.showPnl('game/Map')\n        viewHelper.showPnl('game/Test')\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    private onUpdateMyBattleArea() {\n        this.updateMyBattleArea()\n    }\n\n    // 播放飘字\n    private onPlayFlutterHp(data: any) {\n        const node = this.animals.find(m => m.uid === data.uid)?.node\n        if (node) {\n            this.animPlay.playFlutter(data, this.flutterNode_, ut.convertToNodeAR(node, this.flutterNode_))\n        }\n    }\n\n    private onRemoveAnimal(uid: string, release: boolean) {\n        this.cleanAnimal(this.animals.remove('uid', uid), release)\n    }\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    update(dt: number) {\n        this.model?.update(dt)\n    }\n\n    // 创建一个角色\n    private async createRole(data: RoleObj, index: number) {\n        if (!this.isValid || !data) {\n            return null\n        }\n        const pfb = await assetsMgr.loadTempRes(data.getPrefabUrl(), Prefab, this.key)\n        if (!pfb || !this.isValid) {\n            return null\n        }\n        const pos = this.mapNode_.Child('role_pos/' + index).getPosition()\n        const role = await mc.instantiate(pfb, this.itemsNode_).getComponent(RoleCmpt).init(data, pos, this.key)\n        return role\n    }\n\n    // 创建一个动物\n    private async createAnimal(data: AnimalObj, rootType: string) {\n        if (!this.isValid || !data) {\n            return null\n        }\n        let animal = this.animals.find(m => m.uid === data.uid)\n        if (!animal) {\n        } else if (data.isDie()) {\n            this.animals.remove('uid', data.uid)\n            this.cleanAnimal(animal)\n            return null\n        } else {\n            return animal.resync(data)\n        }\n        const pfb = await assetsMgr.loadTempRes(data.getPrefabUrl(), Prefab, this.key)\n        if (!pfb || !this.isValid) {\n            return null\n        } else if (this.animalMap[data.uid]) {\n            return null //防止多次创建\n        }\n        // 重新获取以防数据不统一\n        const uid = data.uid\n        // data = this.model.getAnimal(uid)\n        if (!data || data.isDie()) {\n            return null\n        }\n        animal = mc.instantiate(pfb, this.itemsNode_).getComponent(AnimalCmpt).init(data, this.key)\n        this.animals.push(animal)\n        this.animalMap[data.uid] = animal\n        return animal\n    }\n\n    private cleanAnimal(animal: AnimalCmpt, release?: boolean) {\n        if (animal) {\n            delete this.animalMap[animal.uid]\n            animal.clean(release)\n        }\n    }\n\n    // 刷新我的战斗区域\n    private updateMyBattleArea() {\n        this.model.getBattleAnimals().forEach(m => this.createAnimal(m, 'my'))\n    }\n\n    // 运行战斗\n    private async runBattle() {\n        await Promise.all(this.model.getEnemyAnimals().map(m => this.createAnimal(m, 'enemy')))\n        this.model.battleBegin()\n    }\n\n    // 刷新遭遇信息\n    private updateEncounterInfo() {\n        // const data = this.model.getCurrContent()\n    }\n}\n"]}