System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, Widget, v3, easing, tween, Button, NotEvent, _dec, _class, _class2, _crd, ccclass, MessageBoxNotCtrl;

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfNotEvent(extras) {
    _reporterNs.report("NotEvent", "../../common/event/NotEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMessageBoxOpts(extras) {
    _reporterNs.report("MessageBoxOpts", "../../common/constant/DataType", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
      Widget = _cc.Widget;
      v3 = _cc.v3;
      easing = _cc.easing;
      tween = _cc.tween;
      Button = _cc.Button;
    }, function (_unresolved_2) {
      NotEvent = _unresolved_2.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "43539tW8zdCMZygcaHlhlgL", "MessageBoxNotCtrl", undefined);

      __checkObsolete__(['_decorator', 'log', 'Node', 'Label', 'RichText', 'EventTouch', 'Widget', 'v3', 'easing', 'tween', 'Button']);

      ({
        ccclass
      } = _decorator);

      _export("default", MessageBoxNotCtrl = (_dec = ut.syncLock, ccclass(_class = (_class2 = class MessageBoxNotCtrl extends mc.BaseNoticeCtrl {
        constructor(...args) {
          super(...args);
          //@autocode property begin
          this.closeNode_ = null;
          // path://close_be_n
          this.rootNode_ = null;
          // path://root_n
          this.titleLbl_ = null;
          // path://root_n/title/title_l
          this.contentRt_ = null;
          // path://root_n/content_rt
          this.buttonsNode_ = null;
          // path://root_n/buttons_nbe_n
          //@end
          this.okCb = null;
          this.cancelCb = null;
          this.clickButtonClose = true;
        }

        //点击按钮是否关闭界面
        listenEventMaps() {
          return [{
            [(_crd && NotEvent === void 0 ? (_reportPossibleCrUseOfNotEvent({
              error: Error()
            }), NotEvent) : NotEvent).OPEN_MESSAGE_BOX]: this.onEventOpen
          }, {
            [(_crd && NotEvent === void 0 ? (_reportPossibleCrUseOfNotEvent({
              error: Error()
            }), NotEvent) : NotEvent).HIDE_MESSAGE_BOX]: this.onEventHide
          }];
        }

        async onCreate() {} // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        // path://close_be_n


        onClickClose(event, data) {
          this.hide();
          this.onHide();
        } // path://root_n/buttons_nbe_n


        onClickButtons(event, data) {
          if (this.clickButtonClose) {
            this.hide();
          }

          const name = event.target.name;

          if (name === 'ok') {
            this.okCb && this.okCb();
          } else if (name === 'cancel') {
            this.cancelCb && this.cancelCb();
          }

          this.onHide();
        } //@end
        // ----------------------------------------- event listener function --------------------------------------------


        onEventOpen(msg, opts) {
          this.open();
          this.show(msg, opts);
        }

        onEventHide() {
          this.hide();
          this.onHide();
        } // ----------------------------------------- custom function ----------------------------------------------------


        async show(msg, opts) {
          var _opts, _opts2, _opts$clickButtonClos, _opts3;

          mc.lockTouch('__open_message_box__');
          opts = opts || {};
          this.titleLbl_.setLocaleKey(opts.title || 'login.title_tip');
          this.contentRt_.setLocaleKey(msg, ...(opts.params || []));
          this.okCb = opts.ok;
          this.cancelCb = opts.cancel; // 是否显示取消按钮

          this.buttonsNode_.Child('cancel').active = !!this.cancelCb; // 设置按钮名字

          this.buttonsNode_.Child('ok/val', Label).setLocaleKey(opts.okText || 'login.button_ok');
          this.buttonsNode_.Child('cancel/val', Label).setLocaleKey(opts.cancelText || 'login.button_cancel'); // 做动画

          this.rootNode_.stopAllActions();
          this.rootNode_.Height = Math.max(360, this.contentRt_.node.Height + 200);
          this.rootNode_.children.forEach(m => {
            var _m$Component;

            return (_m$Component = m.Component(Widget)) == null ? void 0 : _m$Component.updateAlignment();
          }); // 是否显示mask 默认显示

          const isMask = this.closeNode_.active = ((_opts = opts) == null ? void 0 : _opts.mask) === false ? false : true;
          this.playShowAction(isMask, !((_opts2 = opts) != null && _opts2.lockClose)); // 是否开启点击按钮就关闭 默认开启

          this.clickButtonClose = (_opts$clickButtonClos = (_opts3 = opts) == null ? void 0 : _opts3.clickButtonClose) != null ? _opts$clickButtonClos : true;
          await ut.wait(0.25);
          mc.unlockTouch('__open_message_box__');
        }

        playShowAction(isMask, lockClose) {
          const widget = this.Component(Widget);

          if (widget && widget.enabled) {
            widget.updateAlignment();
            widget.enabled = false;
          }

          this.rootNode_.stopAllActions();
          this.rootNode_.scale = v3(0.4);
          tween(this.rootNode_).to(0.25, {
            scale: v3(1)
          }, {
            easing: easing.backOut
          }).start();

          if (isMask) {
            // 禁止点击空白关闭
            this.closeNode_.Component(Button).interactable = lockClose; // 做动画

            this.closeNode_.stopAllActions();
            this.closeNode_.opacity = 0;
            tween(this.closeNode_).to(0.3, {
              opacity: 120
            }, {
              easing: easing.sineOut
            }).start();
          }
        }

        onHide() {
          this.okCb = null;
          this.cancelCb = null;
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "show", [_dec], Object.getOwnPropertyDescriptor(_class2.prototype, "show"), _class2.prototype)), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=64bf718358850e3c4a7677779e1ca468eaad9f28.js.map