{"version": 3, "sources": ["cce:/internal/code-quality/cr.mjs"], "names": ["report", "imported", "moduleRequest", "importMeta", "extras", "console", "warn", "url", "error"], "mappings": ";;;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEO,WAASA,MAAT,CAAgBC,QAAhB,EAA0BC,aAA1B,EAAyCC,UAAzC,EAAqDC,MAArD,EAA6D;AAChEC,IAAAA,OAAO,CAACC,IAAR,6CAC6CH,UAAU,CAACI,GADxD,gCAEiBN,QAFjB,2BAE6CC,aAF7C,UAIIE,MAAM,CAACI,KAJX;AAMH;;oBAPeR,M", "sourcesContent": ["\r\n/**\r\n * This is the module which implements circular-reference detection.\r\n */\r\n\r\n/**\r\n * Reports a circular reference error fired by module import.\r\n * @param imported The binding of the import.\r\n * @param moduleRequest The module request of the import.\r\n * @param importMeta The import.meta of the source module.\r\n * @param extras Extra data passed by circular reference detection implementation.\r\n */\r\n\r\nexport function report(imported, moduleRequest, importMeta, extras) {\r\n    console.warn(\r\n        `Found possible circular reference in \"${importMeta.url}\", \\\r\nhappened when use \"${imported}\" imported from \"${moduleRequest}\" \\\r\n`,\r\n        extras.error,\r\n    );\r\n}\r\n"]}