{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts"], "names": ["getRoleFrameAnimConf", "id", "conf", "ROLE_FRAME_ANIM_CONF", "error", "url", "anims", "name", "interval", "loop", "frameIndexs"], "mappings": ";;;;;AAEA;AACA,WAASA,oBAAT,CAA8BC,EAA9B,EAA0C;AACtC,UAAMC,IAAI,GAAGC,oBAAoB,CAACF,EAAD,CAAjC;;AACA,QAAI,CAACC,IAAL,EAAW;AACPE,MAAAA,KAAK,CAAC,qCAAqCH,EAAtC,CAAL;AACA,aAAO,IAAP;AACH,KAHD,MAGO,IAAI,CAACC,IAAI,CAACG,GAAV,EAAe;AAClBH,MAAAA,IAAI,CAACG,GAAL,GAAY,QAAOJ,EAAG,SAAQA,EAAG,GAAjC;AACH;;AACD,WAAOC,IAAP;AACH,G,CAED;;;kCAWIF,oB;;;;;;;AAzBKI,MAAAA,K,OAAAA,K;;;;;;;;;AAeHD,MAAAA,oB,GAAuB;AACzB,gBAAQ;AACJG,UAAAA,KAAK,EAAE,CACH;AAAEC,YAAAA,IAAI,EAAE,MAAR;AAAgBC,YAAAA,QAAQ,EAAE,GAA1B;AAA+BC,YAAAA,IAAI,EAAE,IAArC;AAA2CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB;AAAxD,WADG,EAEH;AAAEH,YAAAA,IAAI,EAAE,QAAR;AAAkBC,YAAAA,QAAQ,EAAE,GAA5B;AAAiCC,YAAAA,IAAI,EAAE,KAAvC;AAA8CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB,EAA+B,IAA/B,EAAqC,IAArC,EAA2C,IAA3C;AAA3D,WAFG;AADH;AADiB,O", "sourcesContent": ["import { error } from \"cc\"\r\n\r\n// 获取角色的帧动画配置\r\nfunction getRoleFrameAnimConf(id: number) {\r\n    const conf = ROLE_FRAME_ANIM_CONF[id]\r\n    if (!conf) {\r\n        error('getRoleFrameAnimConf error. id: ' + id)\r\n        return null\r\n    } else if (!conf.url) {\r\n        conf.url = `role/${id}/role_${id}_`\r\n    }\r\n    return conf\r\n}\r\n\r\n// 角色动画帧配置\r\nconst ROLE_FRAME_ANIM_CONF = {\r\n    110001: {\r\n        anims: [\r\n            { name: 'idle', interval: 160, loop: true, frameIndexs: ['02', '03', '04', '05'] },\r\n            { name: 'attack', interval: 140, loop: false, frameIndexs: ['12', '13', '14', '15', '21', '22', '23', '01'] },\r\n        ]\r\n    },\r\n}\r\n\r\nexport {\r\n    getRoleFrameAnimConf,\r\n}"]}