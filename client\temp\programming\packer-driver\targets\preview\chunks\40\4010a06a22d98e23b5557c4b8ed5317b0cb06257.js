System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCBoolean, CCInteger, Component, NodeEventType, ScrollView, UITransform, v3, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, menu, requireComponent, disallowMultiple, ScrollViewPlus;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCBoolean = _cc.CCBoolean;
      CCInteger = _cc.CCInteger;
      Component = _cc.Component;
      NodeEventType = _cc.NodeEventType;
      ScrollView = _cc.ScrollView;
      UITransform = _cc.UITransform;
      v3 = _cc.v3;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d98f6RUODxJzKxQOJKmmYjR", "ScrollViewPlus", undefined);

      __checkObsolete__(['_decorator', 'CCBoolean', 'CCInteger', 'Component', 'Node', 'NodeEventType', 'ScrollView', 'UITransform', 'v3', 'Vec3']);

      ({
        ccclass,
        property,
        menu,
        requireComponent,
        disallowMultiple
      } = _decorator);

      _export("default", ScrollViewPlus = (_dec = disallowMultiple(), _dec2 = requireComponent(ScrollView), _dec3 = menu('自定义组件/ScrollViewPlus'), _dec4 = property(CCBoolean), _dec5 = property({
        type: CCInteger,
        range: [1, 10, 1],
        tooltip: '多少帧渲染一次',
        slide: true
      }), _dec6 = property({
        type: CCInteger,
        range: [0, 30, 1],
        tooltip: '一次渲染多少个,0渲染所有',
        slide: true
      }), ccclass(_class = _dec(_class = _dec2(_class = _dec3(_class = (_class2 = class ScrollViewPlus extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "isFrameRender", _descriptor, this);

          //是否开启分帧渲染
          _initializerDefineProperty(this, "updateRate", _descriptor2, this);

          //渲染频率 多少帧渲染一个
          _initializerDefineProperty(this, "updateRenderCount", _descriptor3, this);

          //分帧渲染个数
          this.scrollView = null;
          this.viewNode = null;
          this.contentTransform = null;
          this.viewTransform = null;
          this.content = null;
          this.item = null;
          this.frameCount = 0;
          //多少帧
          this.renderStartIndex = 0;
          //渲染起点下标
          this.needRenderCount = 0;
          //需要渲染的item个数
          this.currRenderCount = 0;
          //当前渲染的item个数
          this.setItemCallback = null;
          this.callbackTarget = null;
          this.datas = null;
          this.isCanScrollingUpdate = false;
          //是否可以滚动刷新了
          this.canScrollingUpdate = false;
          this._temp_vec2_1 = v3();
          this._temp_vec2_2 = v3();
        }

        onLoad() {
          this.init();
        }

        init() {
          if (this.scrollView) {
            return;
          }

          this.scrollView = this.getComponent(ScrollView);
          this.content = this.scrollView.content;
          this.viewNode = this.content.parent;
          this.contentTransform = this.content.getComponent(UITransform);
          this.viewTransform = this.viewNode.getComponent(UITransform);
          this.item = this.content.children[0];

          if (this.item) {
            this.item.active = false;
          }

          this.content.on(NodeEventType.CHILD_REMOVED, this.onScrolling, this);
          this.content.on(NodeEventType.CHILDREN_ORDER_CHANGED, this.onScrolling, this);
          this.node.on('scrolling', this.onScrolling, this);
        } // 滚动


        onScrolling() {
          if (!this.scrollView || this.content.ChildrenCount === 0 || !this.isCanScrollingUpdate) {
            return;
          } // log('111 count=' + this.content.children.filter(m => m.active && m.opacity === 255).length + '/' + this.content.ChildrenCount)


          var contentX = this.contentTransform.width * this.contentTransform.anchorX;
          var contentY = this.contentTransform.height * this.contentTransform.anchorY;
          var pos = this.content.getPosition(this._temp_vec2_1);
          pos.x += this.viewTransform.width * this.viewTransform.anchorX - contentX;
          pos.y += this.viewTransform.height * this.viewTransform.anchorY - contentY; // 遍历 ScrollView Content 内容节点的子节点，对每个子节点的包围盒做和 ScrollView 可视区域包围盒做碰撞判断

          this.content.children.forEach(node => {
            if (!node.active) {
              return;
            }

            var p = node.getPosition(this._temp_vec2_2);
            var transform = node.Component(UITransform);
            p.x = pos.x + p.x + (contentX - transform.width * transform.anchorX);
            p.y = pos.y + p.y + (contentY - transform.height * transform.anchorY);

            if (p.x < -transform.width || p.x > this.viewTransform.width || p.y < -transform.height || p.y > this.viewTransform.height) {
              //如果没有相交就隐藏
              if (node.opacity !== 0) {
                node.opacity = 0;
              }
            } else if (node.opacity !== 255) {
              node.opacity = 255;
            }
          }); // log('222 count=' + this.content.children.filter(m => m.active && m.opacity === 255).length + '/' + this.content.ChildrenCount)
        }

        update(dt) {
          // 渲染
          if (!this.item) {
            return;
          } else if (this.isFrameRender && this.currRenderCount < this.needRenderCount) {
            this.frameCount += 1;

            if (this.frameCount >= this.updateRate) {
              this.frameCount = 0;
              this.updateItems();
            }
          } else if (this.canScrollingUpdate && !this.isCanScrollingUpdate) {
            this.isCanScrollingUpdate = true;
            this.onScrolling();
          }
        } // 开始创建item


        updateItems() {
          var cnt = this.updateRenderCount > 0 ? this.updateRenderCount : this.needRenderCount,
              cur = 0;

          if (this.currRenderCount + cnt > this.needRenderCount) {
            cnt = this.needRenderCount - this.currRenderCount;
          }

          var i = this.renderStartIndex;

          for (; i < this.needRenderCount && cur < cnt; i++) {
            this.setItemData(this.content.children[i] || mc.instantiate(this.item, this.content), i);
            cur += 1;
          }

          this.currRenderCount += cnt;
          this.renderStartIndex = i;

          if (this.currRenderCount >= this.needRenderCount) {
            // log('updateItems done...', i, this.content.ChildrenCount)
            // 将剩余的隐藏
            for (var l = this.content.ChildrenCount; i < l; i++) {
              var node = this.content.children[i];
              node.active = false;
              node.Data = null;
            }
          }
        }

        setItemData(it, i) {
          it.active = true;
          it.Data = null;
          it.opacity = 255;

          if (!this.setItemCallback) {} else if (this.callbackTarget) {
            var _this$datas;

            this.setItemCallback.call(this.callbackTarget, it, (_this$datas = this.datas) == null ? void 0 : _this$datas[i], i);
          } else {
            var _this$datas2;

            this.setItemCallback(it, (_this$datas2 = this.datas) == null ? void 0 : _this$datas2[i], i);
          }

          return it;
        }

        reset() {
          this.isCanScrollingUpdate = false;
          this.canScrollingUpdate = false;
        }

        items(list, prefab, cb, target) {
          if (typeof list === 'number') {
            this.needRenderCount = list;
            this.datas = null;
          } else {
            this.needRenderCount = list.length;
            this.datas = list;
          }

          if (prefab) {
            this.item = prefab;
          }

          this.setItemCallback = cb;
          this.callbackTarget = target;
          this.currRenderCount = 0;
          this.renderStartIndex = 0;
          this.canScrollingUpdate = true;

          if (this.needRenderCount === 0) {
            this.content.children.forEach(m => {
              m.active = false;
              m.Data = null;
            });
          }
        }

        updateNodeShow() {
          this.canScrollingUpdate = true;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "isFrameRender", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "updateRate", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 1;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "updateRenderCount", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 1;
        }
      })), _class2)) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4010a06a22d98e23b5557c4b8ed5317b0cb06257.js.map