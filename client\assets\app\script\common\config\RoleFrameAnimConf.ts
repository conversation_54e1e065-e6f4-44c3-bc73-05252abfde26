import { error } from "cc"

// 获取角色的帧动画配置
function getRoleFrameAnimConf(id: number) {
    const conf = ROLE_FRAME_ANIM_CONF[id]
    if (!conf) {
        error('getRoleFrameAnimConf error. id: ' + id)
        return null
    } else if (!conf.url) {
        conf.url = `role/${id}/role_${id}_`
    }
    return conf
}

// 角色动画帧配置
const ROLE_FRAME_ANIM_CONF = {
    110001: {
        anims: [
            { name: 'idle', interval: 160, loop: true, frameIndexs: ['02', '03', '04', '05'] },
            { name: 'attack', interval: 140, loop: false, frameIndexs: ['12', '13', '14', '15', '21', '22', '23', '01'] },
        ]
    },
}

export {
    getRoleFrameAnimConf,
}