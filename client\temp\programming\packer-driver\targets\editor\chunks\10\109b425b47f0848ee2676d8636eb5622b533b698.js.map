{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts"], "names": ["ViewCtrlMgr", "easing", "instantiate", "js", "Prefab", "Tween", "tween", "UIOpacity", "v3", "view", "Widget", "BasePnlCtrl", "CoreEventType", "loader", "node", "caches", "Map", "opened", "masks", "pnlOpenIndex", "pnlIndexConf", "loadQueues", "loadId", "getOpened", "setPnlIndexConf", "conf", "addLoadQueue", "name", "add", "id", "url", "hasLoadQueue", "has", "__load", "info", "pfb", "mod", "wind", "pnl", "split", "head", "ut", "initialUpperCase", "replace", "mc", "currWind<PERSON>ame", "error", "loadRes", "remove", "Promise", "resolve", "__loadPnl", "it", "className", "getClassByName", "logger", "getComponent", "addComponent", "key", "_isLoadProperty", "loadProperty", "active", "__create", "set", "__getF<PERSON><PERSON>ache", "ui", "get", "preloadPnl", "getMask", "pop", "assetsMgr", "getPrefab", "parent", "opacity", "zIndex", "mask", "putMask", "push", "playShowAction", "widget", "Component", "enabled", "updateAlignment", "stopAllByTarget", "scale", "to", "backOut", "start", "uiOpacity", "sineOut", "wait", "<PERSON><PERSON><PERSON><PERSON>", "onPlayActionComplete", "eventCenter", "emit", "PNL_ENTER_PLAY_DONE", "adaptRootSize", "root", "Child", "wsize", "getVisibleSize", "dsize", "getDesignResolutionSize", "rsize", "transform", "contentSize", "width", "height", "adaptHeight", "Math", "min", "getNextOpenIndex", "pushPnl", "__open_index", "updatePnlIndex", "popPnl", "list", "sort", "a", "b", "map", "m", "i", "index", "initIndex", "sortIndex", "for<PERSON>ach", "lt", "slice", "gt", "arr", "filter", "temp", "delete", "s", "setIndex", "show", "params", "lockTouch", "data", "LOAD_BEGIN_PNL", "LOAD_END_PNL", "_state", "unlockTouch", "getActive", "setActive", "isMask", "__enter", "setOpacity", "PNL_ENTER", "isAct", "then", "hide", "val", "__remove", "PNL_LEAVE", "hide<PERSON>ll", "ignores", "length", "isClean", "indexOf", "splice", "clean", "force", "__clean", "destroy", "releaseRes", "cleanAll", "cleanLoadQueue", "giveupLoadByName", "cleanByMod", "cleanAllUnused", "isUnlock", "giveupLoad", "giveupLoadById"], "mappings": ";;;+<PERSON><PERSON>q<PERSON>,W;;;;;;;;;;;;;;;;;;;;;;;AALZC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;AAAUC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AAC5EC,MAAAA,W;;AACAC,MAAAA,a;;AACEC,MAAAA,M,iBAAAA,M;;;;;;;;;yBAEYb,W,GAAN,MAAMA,WAAN,CAAkB;AAAA;AAAA,eAEtBc,IAFsB,GAET,IAFS;AAAA,eAIrBC,MAJqB,GAIc,IAAIC,GAAJ,EAJd;AAAA,eAKrBC,MALqB,GAKG,EALH;AAAA,eAMrBC,KANqB,GAML,EANK;AAMF;AANE,eAOrBC,YAPqB,GAOE,CAPF;AAOI;AAPJ,eAQrBC,YARqB,GAQD,EARC;AAQE;AARF,eAUrBC,UAVqB,GAUO,EAVP;AAUU;AAVV,eAWrBC,MAXqB,GAWJ,CAXI;AAAA;;AAatBC,QAAAA,SAAS,GAAG;AACf,iBAAO,KAAKN,MAAZ;AACH;;AAEMO,QAAAA,eAAe,CAACC,IAAD,EAAY;AAC9B,eAAKL,YAAL,GAAoBK,IAApB;AACH,SAnB4B,CAqB7B;;;AACQC,QAAAA,YAAY,CAACC,IAAD,EAAe;AAC/B,iBAAO,KAAKN,UAAL,CAAgBO,GAAhB,CAAoB;AAAEC,YAAAA,EAAE,EAAE,EAAE,KAAKP,MAAb;AAAqBK,YAAAA,IAAI,EAAEA,IAA3B;AAAiCG,YAAAA,GAAG,EAAE;AAAtC,WAApB,CAAP;AACH;;AAEOC,QAAAA,YAAY,CAACJ,IAAD,EAAe;AAC/B,iBAAO,KAAKN,UAAL,CAAgBW,GAAhB,CAAoB,MAApB,EAA4BL,IAA5B,CAAP;AACH;;AAEmB,cAANM,MAAM,CAACN,IAAD,EAAeO,IAAf,EAAkC;AAClD;AACA,cAAIC,GAAW,GAAG,IAAlB;AAAA,cAAwBL,GAAW,GAAG,EAAtC;AAAA,cAA0CM,GAAW,GAAG,EAAxD;AACA,cAAI,CAACC,IAAD,EAAOC,GAAP,IAAcX,IAAI,CAACY,KAAL,CAAW,GAAX,CAAlB;;AACA,cAAI,CAACD,GAAL,EAAU;AAAC;AACP;AACA,kBAAME,IAAI,GAAGC,EAAE,CAACC,gBAAH,CAAoBL,IAApB,EAA0BM,OAA1B,CAAkC,KAAlC,EAAyC,EAAzC,CAAb;AACAP,YAAAA,GAAG,GAAGQ,EAAE,CAACC,YAAT;AACAX,YAAAA,IAAI,CAACJ,GAAL,GAAWA,GAAG,GAAI,QAAOM,GAAI,IAAGI,IAAK,KAArC;AACA;AAAA;AAAA,kCAAOM,KAAP,GAAe,KAAf;AACAX,YAAAA,GAAG,GAAG,MAAM;AAAA;AAAA,kCAAOY,OAAP,CAAejB,GAAf,EAAoB1B,MAApB,CAAZ;AACA;AAAA;AAAA,kCAAO0C,KAAP,GAAe,IAAf;;AACA,gBAAI,CAACX,GAAL,EAAU;AAAE;AACRC,cAAAA,GAAG,GAAG,QAAN;AACAF,cAAAA,IAAI,CAACJ,GAAL,GAAWA,GAAG,GAAI,QAAOM,GAAI,IAAGI,IAAK,KAArC;AACAL,cAAAA,GAAG,GAAG,MAAM;AAAA;AAAA,oCAAOY,OAAP,CAAejB,GAAf,EAAoB1B,MAApB,CAAZ;AACH;AACJ,WAbD,MAaO;AACH,kBAAMoC,IAAI,GAAGC,EAAE,CAACC,gBAAH,CAAoBJ,GAApB,EAAyBK,OAAzB,CAAiC,KAAjC,EAAwC,EAAxC,CAAb;AACAP,YAAAA,GAAG,GAAGC,IAAN;AACAH,YAAAA,IAAI,CAACJ,GAAL,GAAWA,GAAG,GAAI,QAAOM,GAAI,IAAGI,IAAK,KAArC;AACAL,YAAAA,GAAG,GAAG,MAAM;AAAA;AAAA,kCAAOY,OAAP,CAAejB,GAAf,EAAoB1B,MAApB,CAAZ;AACH,WAtBiD,CAuBlD;;;AACA,cAAI,CAAC,KAAKiB,UAAL,CAAgB2B,MAAhB,CAAuB,IAAvB,EAA6Bd,IAAI,CAACL,EAAlC,CAAL,EAA4C;AACxCM,YAAAA,GAAG,GAAG,IAAN;AACH;;AACDD,UAAAA,IAAI,CAACL,EAAL,GAAU,CAAV,CA3BkD,CA2BtC;;AACZ,iBAAOoB,OAAO,CAACC,OAAR,CAAgB;AAAEd,YAAAA,GAAF;AAAON,YAAAA,GAAP;AAAYK,YAAAA;AAAZ,WAAhB,CAAP;AACH,SA3D4B,CA6D7B;;;AACsB,cAATgB,SAAS,CAACxB,IAAD,EAAeO,IAAf,EAAwD;AAC1E,gBAAM;AAAEE,YAAAA,GAAF;AAAON,YAAAA,GAAP;AAAYK,YAAAA;AAAZ,cAAoB,MAAM,KAAKF,MAAL,CAAYN,IAAZ,EAAkBO,IAAlB,CAAhC;;AACA,cAAI,CAACC,GAAL,EAAU;AACN,mBAAOc,OAAO,CAACC,OAAR,CAAgB,IAAhB,CAAP;AACH;;AACD,gBAAME,EAAE,GAAGR,EAAE,CAAC1C,WAAH,CAAeiC,GAAf,EAAoB,KAAKrB,IAAzB,CAAX;AACA,gBAAMuC,SAAS,GAAGD,EAAE,CAACzB,IAAH,GAAU,MAA5B;;AACA,cAAI,CAACxB,EAAE,CAACmD,cAAH,CAAkBD,SAAlB,CAAL,EAAmC;AAC/BE,YAAAA,MAAM,CAACT,KAAP,CAAa,oCAAoCO,SAAjD;AACA,mBAAOJ,OAAO,CAACC,OAAR,CAAgB,IAAhB,CAAP;AACH;;AACD,cAAIZ,GAAG,GAAGc,EAAE,CAACI,YAAH,CAAgBH,SAAhB,CAAV;;AACA,cAAI,CAACf,GAAL,EAAU;AACNA,YAAAA,GAAG,GAAGc,EAAE,CAACK,YAAH,CAAgBJ,SAAhB,CAAN;AACH;;AACD,cAAI,CAACf,GAAD,IAAQ,EAAEA,GAAG;AAAA;AAAA,yCAAL,CAAZ,EAA2C;AACvCiB,YAAAA,MAAM,CAACT,KAAP,CAAa,oCAAoCO,SAAjD;AACA,mBAAOJ,OAAO,CAACC,OAAR,CAAgB,IAAhB,CAAP;AACH;;AACDZ,UAAAA,GAAG,CAACoB,GAAJ,GAAU/B,IAAV;AACAW,UAAAA,GAAG,CAACF,GAAJ,GAAUA,GAAV;AACAE,UAAAA,GAAG,CAACR,GAAJ,GAAUA,GAAV,CArB0E,CAsB1E;;AACA,cAAI,CAACQ,GAAG,CAACqB,eAAT,EAA0B;AACtBJ,YAAAA,MAAM,CAACT,KAAP,CAAa,2CAA2CO,SAAxD;AACAf,YAAAA,GAAG,CAACsB,YAAJ;AACH;;AACDR,UAAAA,EAAE,CAACS,MAAH,GAAY,KAAZ;AACA,gBAAMvB,GAAG,CAACwB,QAAJ,EAAN;AACA,eAAK/C,MAAL,CAAYgD,GAAZ,CAAgBjC,GAAhB,EAAqBQ,GAArB;AACA,iBAAOW,OAAO,CAACC,OAAR,CAAgBZ,GAAhB,CAAP;AACH,SA7F4B,CA+F7B;;;AACQ0B,QAAAA,aAAa,CAACrC,IAAD,EAAe;AAChC,cAAI,CAACU,IAAD,EAAOqB,GAAP,IAAc/B,IAAI,CAACY,KAAL,CAAW,GAAX,CAAlB;AAAA,cAAmC0B,EAAe,GAAG,IAArD;;AACA,cAAI,CAACP,GAAL,EAAU;AACN,kBAAMlB,IAAI,GAAGC,EAAE,CAACC,gBAAH,CAAoBL,IAApB,EAA0BM,OAA1B,CAAkC,KAAlC,EAAyC,EAAzC,CAAb;AACAsB,YAAAA,EAAE,GAAG,KAAKlD,MAAL,CAAYmD,GAAZ,CAAiB,QAAOtB,EAAE,CAACC,YAAa,IAAGL,IAAK,KAAhD,CAAL;;AACA,gBAAI,CAACyB,EAAL,EAAS;AACLA,cAAAA,EAAE,GAAG,KAAKlD,MAAL,CAAYmD,GAAZ,CAAiB,eAAc1B,IAAK,KAApC,CAAL;AACH;AACJ,WAND,MAMO;AACH,kBAAMA,IAAI,GAAGC,EAAE,CAACC,gBAAH,CAAoBgB,GAApB,EAAyBf,OAAzB,CAAiC,KAAjC,EAAwC,EAAxC,CAAb;AACAsB,YAAAA,EAAE,GAAG,KAAKlD,MAAL,CAAYmD,GAAZ,CAAiB,QAAO7B,IAAK,IAAGG,IAAK,KAArC,CAAL;AACH;;AACD,iBAAOyB,EAAP;AACH,SA7G4B,CA+G7B;;;AACuB,cAAVE,UAAU,CAACxC,IAAD,EAAe;AAClC,cAAIW,GAAG,GAAG,KAAK0B,aAAL,CAAmBrC,IAAnB,CAAV;;AACA,cAAI,CAACW,GAAD,IAAQ,CAAC,KAAKP,YAAL,CAAkBJ,IAAlB,CAAb,EAAsC;AAClCW,YAAAA,GAAG,GAAG,MAAM,KAAKa,SAAL,CAAexB,IAAf,EAAqB,KAAKD,YAAL,CAAkBC,IAAlB,CAArB,CAAZ;AACH;;AACD,iBAAOW,GAAP;AACH,SAtH4B,CAwH7B;;;AACQ8B,QAAAA,OAAO,CAACH,EAAD,EAAkB;AAC7B,cAAIb,EAAE,GAAG,KAAKlC,KAAL,CAAWmD,GAAX,EAAT;;AACA,cAAI,CAACjB,EAAL,EAAS;AACL,kBAAMjB,GAAG,GAAGmC,SAAS,CAACC,SAAV,CAAoB,UAApB,CAAZ;;AACA,gBAAIpC,GAAJ,EAAS;AACLiB,cAAAA,EAAE,GAAGlD,WAAW,CAACiC,GAAD,CAAhB;AACH;AACJ;;AACD,cAAIiB,EAAJ,EAAQ;AACJA,YAAAA,EAAE,CAACoB,MAAH,GAAY,KAAK1D,IAAjB;AACAsC,YAAAA,EAAE,CAACS,MAAH,GAAY,IAAZ;AACAT,YAAAA,EAAE,CAACqB,OAAH,GAAa,GAAb,CAHI,CAIJ;;AACArB,YAAAA,EAAE,CAACsB,MAAH,GAAYT,EAAE,CAACnD,IAAH,CAAQ4D,MAApB;AACH;;AACDT,UAAAA,EAAE,CAACU,IAAH,GAAUvB,EAAV;AACH;;AAEOwB,QAAAA,OAAO,CAACX,EAAD,EAAkB;AAC7B,cAAIA,EAAE,IAAIA,EAAE,CAACU,IAAb,EAAmB;AACfV,YAAAA,EAAE,CAACU,IAAH,CAAQH,MAAR,GAAiB,IAAjB;AACA,iBAAKtD,KAAL,CAAW2D,IAAX,CAAgBZ,EAAE,CAACU,IAAnB;AACAV,YAAAA,EAAE,CAACU,IAAH,GAAU,IAAV;AACH;AACJ,SAjJ4B,CAmJ7B;;;AAC4B,cAAdG,cAAc,CAACb,EAAD,EAAkB;AAC1C,gBAAMc,MAAM,GAAGd,EAAE,CAACe,SAAH,CAAatE,MAAb,CAAf;;AACA,cAAIqE,MAAM,IAAIA,MAAM,CAACE,OAArB,EAA8B;AAC1BF,YAAAA,MAAM,CAACG,eAAP;AACAH,YAAAA,MAAM,CAACE,OAAP,GAAiB,KAAjB;AACH;;AACD5E,UAAAA,KAAK,CAAC8E,eAAN,CAAsBlB,EAAE,CAACnD,IAAzB;AACAmD,UAAAA,EAAE,CAACnD,IAAH,CAAQsE,KAAR,GAAgB5E,EAAE,CAAC,GAAD,EAAM,GAAN,CAAlB;AACAF,UAAAA,KAAK,CAAC2D,EAAE,CAACnD,IAAJ,CAAL,CAAeuE,EAAf,CAAkB,IAAlB,EAAwB;AAAED,YAAAA,KAAK,EAAE5E,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,WAAxB,EAA6C;AAAEP,YAAAA,MAAM,EAAEA,MAAM,CAACqF;AAAjB,WAA7C,EAAyEC,KAAzE;;AACA,cAAItB,EAAE,CAACU,IAAP,EAAa;AACT,kBAAMa,SAAS,GAAGvB,EAAE,CAACU,IAAH,CAAQK,SAAR,CAAkBzE,SAAlB,CAAlB;AACAF,YAAAA,KAAK,CAAC8E,eAAN,CAAsBK,SAAtB;AACAA,YAAAA,SAAS,CAACf,OAAV,GAAoB,CAApB;AACAnE,YAAAA,KAAK,CAACkF,SAAD,CAAL,CAAiBH,EAAjB,CAAoB,GAApB,EAAyB;AAAEZ,cAAAA,OAAO,EAAE;AAAX,aAAzB,EAA2C;AAAExE,cAAAA,MAAM,EAAEA,MAAM,CAACwF;AAAjB,aAA3C,EAAuEF,KAAvE;AACH;;AACD,gBAAM9C,EAAE,CAACiD,IAAH,CAAQ,IAAR,CAAN;;AACA,cAAIzB,EAAE,CAAC0B,OAAP,EAAgB;AACZ1B,YAAAA,EAAE,CAAC2B,oBAAH;AACAC,YAAAA,WAAW,CAACC,IAAZ,CAAiB;AAAA;AAAA,gDAAcC,mBAA/B,EAAoD9B,EAApD;AACH;AACJ,SAxK4B,CA0K7B;;;AACQ+B,QAAAA,aAAa,CAAC/B,EAAD,EAAkB;AACnC,cAAI,CAACA,EAAL,EAAS;AACL;AACH;;AACD,gBAAMgC,IAAI,GAAGhC,EAAE,CAACiC,KAAH,CAAS,MAAT,KAAoBjC,EAAE,CAACiC,KAAH,CAAS,QAAT,CAAjC;;AACA,cAAI,CAACD,IAAL,EAAW;AACP;AACH;;AACD,gBAAME,KAAK,GAAG1F,IAAI,CAAC2F,cAAL,EAAd;AACA,gBAAMC,KAAK,GAAG5F,IAAI,CAAC6F,uBAAL,EAAd;AACA,gBAAMC,KAAK,GAAGN,IAAI,CAACO,SAAL,CAAeC,WAA7B,CAVmC,CAWnC;;AACA,cAAIrB,KAAK,GAAImB,KAAK,CAACG,KAAN,GAAcL,KAAK,CAACK,KAApB,GAA4BP,KAAK,CAACO,KAAnC,GAA4CH,KAAK,CAACG,KAA9D,CAZmC,CAanC;;AACA,gBAAMC,MAAM,GAAGR,KAAK,CAACQ,MAAN,GAAe1C,EAAE,CAAC2C,WAAjC;;AACA,cAAIL,KAAK,CAACI,MAAN,GAAevB,KAAf,GAAuBuB,MAA3B,EAAmC;AAC/BvB,YAAAA,KAAK,GAAGuB,MAAM,GAAGJ,KAAK,CAACI,MAAvB;AACH;;AACDV,UAAAA,IAAI,CAACb,KAAL,GAAa5E,EAAE,CAACqG,IAAI,CAACC,GAAL,CAAS,GAAT,EAAc1B,KAAd,CAAD,CAAf;AACH;;AAEO2B,QAAAA,gBAAgB,GAAG;AACvB,iBAAO,EAAE,KAAK5F,YAAd;AACH;;AAEO6F,QAAAA,OAAO,CAAC/C,EAAD,EAAkB;AAC7BA,UAAAA,EAAE,CAACgD,YAAH,GAAkB,KAAKF,gBAAL,EAAlB;AACA,eAAK9F,MAAL,CAAY+B,MAAZ,CAAmB,KAAnB,EAA0BiB,EAAE,CAACnC,GAA7B;AACA,eAAKb,MAAL,CAAY4D,IAAZ,CAAiBZ,EAAjB;AACA,eAAKiD,cAAL;AACH;;AAEOC,QAAAA,MAAM,CAAClD,EAAD,EAAkB;AAC5B,eAAKhD,MAAL,CAAY+B,MAAZ,CAAmB,KAAnB,EAA0BiB,EAAE,CAACnC,GAA7B;AACA,eAAKoF,cAAL;AACH,SA9M4B,CAgN7B;;;AACQA,QAAAA,cAAc,GAAG;AACrB;AACA,gBAAME,IAAI,GAAG,KAAKnG,MAAL,CAAYoG,IAAZ,CAAiB,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACL,YAAF,GAAiBM,CAAC,CAACN,YAA9C,EAA4DO,GAA5D,CAAgE,CAACC,CAAD,EAAIC,CAAJ,KAAU;AACnF,kBAAMC,KAAK,GAAGD,CAAC,GAAG,CAAlB;AACA,mBAAO;AACHzD,cAAAA,EAAE,EAAEwD,CADD;AAEH/D,cAAAA,GAAG,EAAE+D,CAAC,CAAC/D,GAFJ;AAGHkE,cAAAA,SAAS,EAAED,KAHR;AAIHE,cAAAA,SAAS,EAAEF,KAAK,GAAG;AAJhB,aAAP;AAMH,WARY,CAAb;AASAP,UAAAA,IAAI,CAACU,OAAL,CAAaL,CAAC,IAAI;AAAA;;AACd,kBAAMhG,IAAI,GAAG,KAAKL,YAAL,CAAkBqG,CAAC,CAAC/D,GAApB,CAAb;;AACA,gBAAI,CAACjC,IAAL,EAAW;AACP;AACH;;AACD,kBAAMsG,EAAY,eAAGtG,IAAI,CAACsG,EAAR,qBAAG,SAASC,KAAT,EAArB,CALc,CAMd;;AACA,gBAAIvG,IAAI,CAACwG,EAAT,EAAa;AACT,oBAAMC,GAAG,GAAGd,IAAI,CAACe,MAAL,CAAY7F,GAAG,IAAIb,IAAI,CAACwG,EAAL,CAAQjG,GAAR,CAAYM,GAAG,CAACoB,GAAhB,CAAnB,EAAyC2D,IAAzC,CAA8C,CAACC,CAAD,EAAIC,CAAJ,KAAUA,CAAC,CAACM,SAAF,GAAcP,CAAC,CAACO,SAAxE,CAAZ;AAAA,oBAAgGO,IAAI,GAAGF,GAAG,CAAC,CAAD,CAA1G;;AACA,kBAAIE,IAAI,IAAIX,CAAC,CAACI,SAAF,GAAcO,IAAI,CAACP,SAA/B,EAA0C;AACtCJ,gBAAAA,CAAC,CAACI,SAAF,GAAcO,IAAI,CAACP,SAAL,GAAiBJ,CAAC,CAACG,SAAjC;AACH,eAJQ,CAKT;;;AACAG,cAAAA,EAAE,IAAIG,GAAG,CAACJ,OAAJ,CAAYL,CAAC,IAAI;AAAA;;AACnB,sBAAMQ,EAAE,4BAAG,KAAK7G,YAAL,CAAkBqG,CAAC,CAAC/D,GAApB,CAAH,qBAAG,sBAA0BuE,EAArC;AACAA,gBAAAA,EAAE,IAAIF,EAAE,CAACM,MAAH,CAAUC,CAAC,IAAIL,EAAE,CAACjG,GAAH,CAAOsG,CAAP,CAAf,CAAN;AACH,eAHK,CAAN;AAIH,aAjBa,CAkBd;;;AACAP,YAAAA,EAAE,IAAIX,IAAI,CAACe,MAAL,CAAY7F,GAAG,IAAIyF,EAAE,CAAC/F,GAAH,CAAOM,GAAG,CAACoB,GAAX,CAAnB,EAAoCoE,OAApC,CAA4CM,IAAI,IAAI;AACtD,kBAAIA,IAAI,CAACP,SAAL,GAAiBJ,CAAC,CAACI,SAAvB,EAAkC;AAC9BO,gBAAAA,IAAI,CAACP,SAAL,GAAiBJ,CAAC,CAACI,SAAF,GAAcO,IAAI,CAACR,SAApC;AACH;AACJ,aAJK,CAAN;AAKH,WAxBD;AAyBAR,UAAAA,IAAI,CAACC,IAAL,CAAU,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACO,SAAF,GAAcN,CAAC,CAACM,SAApC,EAA+CC,OAA/C,CAAuD,CAACL,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACxD,EAAF,CAAKsE,QAAL,CAAcb,CAAC,GAAG,EAAlB,CAAjE;AACA,eAAKzG,MAAL,CAAYoG,IAAZ,CAAiB,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACxG,IAAF,CAAO4D,MAAP,GAAgB6C,CAAC,CAACzG,IAAF,CAAO4D,MAAlD;AACH,SAvP4B,CAyP7B;;;AACiB,cAAJ8D,IAAI,CAAClG,GAAD,EAA4B,GAAGmG,MAA/B,EAAkE;AAC/E7F,UAAAA,EAAE,CAAC8F,SAAH,CAAa,cAAb;AACA,cAAIzE,EAAe,GAAG,IAAtB;;AACA,cAAI,OAAQ3B,GAAR,KAAiB,QAArB,EAA+B;AAC3B2B,YAAAA,EAAE,GAAG,KAAKD,aAAL,CAAmB1B,GAAnB,CAAL;;AACA,gBAAI,CAAC2B,EAAD,IAAO,CAAC,KAAKlC,YAAL,CAAkBO,GAAlB,CAAZ,EAAoC;AAChC,oBAAMqG,IAAI,GAAG,KAAKjH,YAAL,CAAkBY,GAAlB,CAAb;AACAqG,cAAAA,IAAI,CAACF,MAAL,GAAcA,MAAd;AACA5C,cAAAA,WAAW,CAACC,IAAZ,CAAiB;AAAA;AAAA,kDAAc8C,cAA/B,EAA+CD,IAA/C;AACA1E,cAAAA,EAAE,GAAG,MAAM,KAAKd,SAAL,CAAeb,GAAf,EAAoBqG,IAApB,CAAX;AACA9C,cAAAA,WAAW,CAACC,IAAZ,CAAiB;AAAA;AAAA,kDAAc+C,YAA/B,EAA6CF,IAA7C;AACH;AACJ,WATD,MASO,IAAIrG,GAAG,CAACqD,OAAJ,IAAerD,GAAG,CAACwG,MAAJ,KAAe,OAAlC,EAA2C;AAC9C7E,YAAAA,EAAE,GAAG3B,GAAL;AACH,WAFM,MAEA;AACH,mBAAO,KAAKkG,IAAL,CAAUlG,GAAG,CAACoB,GAAd,EAAmB,GAAG+E,MAAtB,CAAP;AACH;;AACD,cAAI,CAACxE,EAAD,IAAO,CAACA,EAAE,CAAC0B,OAAf,EAAwB;AACpB/C,YAAAA,EAAE,CAACmG,WAAH,CAAe,cAAf;AACA,mBAAO,IAAP;AACH,WAHD,MAGO,IAAI,CAAC9E,EAAE,CAAC+E,SAAH,EAAL,EAAqB;AACxB,iBAAKhC,OAAL,CAAa/C,EAAb;AACAA,YAAAA,EAAE,CAACgF,SAAH,CAAa,IAAb;AACAhF,YAAAA,EAAE,CAACiF,MAAH,IAAa,KAAK9E,OAAL,CAAaH,EAAb,CAAb;;AACAA,YAAAA,EAAE,CAACkF,OAAH,CAAW,GAAGV,MAAd,EAJwB,CAKxB;;;AACAxE,YAAAA,EAAE,CAACmF,UAAH,CAAc,GAAd,EANwB,CAOxB;;AACAvD,YAAAA,WAAW,CAACC,IAAZ,CAAiB;AAAA;AAAA,gDAAcuD,SAA/B,EAA0CpF,EAA1C;;AACA,gBAAIA,EAAE,CAACqF,KAAP,EAAc;AACV,mBAAKxE,cAAL,CAAoBb,EAApB,EAAwBsF,IAAxB,CAA6B,MAAM3G,EAAE,CAACmG,WAAH,CAAe,cAAf,CAAnC;AACH,aAFD,MAEO;AACH9E,cAAAA,EAAE,CAACnD,IAAH,CAAQsE,KAAR,GAAgB5E,EAAE,CAAC,CAAD,EAAI,CAAJ,CAAlB;AACAyD,cAAAA,EAAE,CAAC2B,oBAAH;AACAC,cAAAA,WAAW,CAACC,IAAZ,CAAiB;AAAA;AAAA,kDAAcC,mBAA/B,EAAoD9B,EAApD;AACArB,cAAAA,EAAE,CAACmG,WAAH,CAAe,cAAf;AACH;AACJ,WAjBM,MAiBA;AACHnG,YAAAA,EAAE,CAACmG,WAAH,CAAe,cAAf;AACH;;AACD,iBAAO9E,EAAP;AACH,SAnS4B,CAqS7B;;;AACOuF,QAAAA,IAAI,CAACC,GAAD,EAA4B;AACnC,gBAAMxF,EAAe,GAAGwF,GAAG;AAAA;AAAA,yCAAH,GAA6BA,GAA7B,GAAmC,KAAKzF,aAAL,CAAmByF,GAAnB,CAA3D;;AACA,cAAI,CAACxF,EAAD,IAAO,CAACA,EAAE,CAAC0B,OAAf,EAAwB;AACpB;AACH;;AACD,eAAKwB,MAAL,CAAYlD,EAAZ;AACA,eAAKW,OAAL,CAAaX,EAAb;;AACA,cAAIA,EAAE,CAAC+E,SAAH,EAAJ,EAAoB;AAChB/E,YAAAA,EAAE,CAACyF,QAAH;;AACAzF,YAAAA,EAAE,CAACgF,SAAH,CAAa,KAAb;AACApD,YAAAA,WAAW,CAACC,IAAZ,CAAiB;AAAA;AAAA,gDAAc6D,SAA/B,EAA0C1F,EAA1C;AACH;AACJ,SAlT4B,CAoT7B;;;AACO2F,QAAAA,OAAO,CAACH,GAAD,EAAeI,OAAf,EAAiC;AAC3C,cAAI,CAACJ,GAAL,EAAU;AACN,kBAAMvB,GAAG,GAAG2B,OAAO,GAAGA,OAAO,CAACtH,KAAR,CAAc,GAAd,CAAH,GAAwB,EAA3C;;AACA,iBAAK,IAAImF,CAAC,GAAG,KAAKzG,MAAL,CAAY6I,MAAZ,GAAqB,CAAlC,EAAqCpC,CAAC,IAAI,CAA1C,EAA6CA,CAAC,EAA9C,EAAkD;AAC9C,oBAAMD,CAAC,GAAG,KAAKxG,MAAL,CAAYyG,CAAZ,CAAV,CAD8C,CAE9C;;AACA,kBAAID,CAAC,CAACsC,OAAF,IAAa7B,GAAG,CAAC8B,OAAJ,CAAYvC,CAAC,CAAC/D,GAAd,MAAuB,CAAC,CAAzC,EAA4C;AACxC,qBAAKzC,MAAL,CAAYgJ,MAAZ,CAAmBvC,CAAnB,EAAsB,CAAtB;AACA,qBAAK9C,OAAL,CAAa6C,CAAb;;AACA,oBAAIA,CAAC,CAACuB,SAAF,EAAJ,EAAmB;AACfvB,kBAAAA,CAAC,CAACiC,QAAF;;AACAjC,kBAAAA,CAAC,CAACwB,SAAF,CAAY,KAAZ;AACApD,kBAAAA,WAAW,CAACC,IAAZ,CAAiB;AAAA;AAAA,sDAAc6D,SAA/B,EAA0ClC,CAA1C;AACH;AACJ;AACJ;;AACD,iBAAKP,cAAL;AACH,WAhBD,MAgBO;AACHuC,YAAAA,GAAG,CAAClH,KAAJ,CAAU,GAAV,EAAeuF,OAAf,CAAuBL,CAAC,IAAI,KAAK+B,IAAL,CAAU/B,CAAV,CAA5B;AACH;AACJ,SAzU4B,CA2U7B;;;AACOyC,QAAAA,KAAK,CAACT,GAAD,EAA4BU,KAA5B,EAA6C;AACrD,gBAAMlG,EAAe,GAAGwF,GAAG;AAAA;AAAA,yCAAH,GAA6BA,GAA7B,GAAmC,KAAKzF,aAAL,CAAmByF,GAAnB,CAA3D;;AACA,cAAI,CAACxF,EAAD,IAAQ,CAACA,EAAE,CAAC8F,OAAJ,IAAe,CAACI,KAA5B,EAAoC;AAChC;AACH;;AACD,eAAKX,IAAL,CAAUvF,EAAV;;AACAA,UAAAA,EAAE,CAACmG,OAAH;;AACAnG,UAAAA,EAAE,CAACnD,IAAH,CAAQuJ,OAAR;AACA,eAAKtJ,MAAL,CAAYsH,MAAZ,CAAmBpE,EAAE,CAACnC,GAAtB;AACA;AAAA;AAAA,gCAAOwI,UAAP,CAAkBrG,EAAE,CAACnC,GAArB,EAA0B1B,MAA1B;AACH,SAtV4B,CAwV7B;;;AACOmK,QAAAA,QAAQ,CAACd,GAAD,EAAeU,KAAf,EAAgC;AAC3C,cAAI,CAACV,GAAL,EAAU;AACN,iBAAK1I,MAAL,CAAY+G,OAAZ,CAAoBL,CAAC,IAAI,KAAKyC,KAAL,CAAWzC,CAAX,EAAc0C,KAAd,CAAzB;AACA,iBAAKK,cAAL;AACH,WAHD,MAGO;AACHf,YAAAA,GAAG,CAAClH,KAAJ,CAAU,GAAV,EAAeuF,OAAf,CAAuBL,CAAC,IAAI;AACxB,mBAAKyC,KAAL,CAAWzC,CAAX,EAAc0C,KAAd;AACA,mBAAKM,gBAAL,CAAsBhD,CAAtB;AACH,aAHD;AAIH;AACJ,SAnW4B,CAqW7B;;;AACOiD,QAAAA,UAAU,CAACtI,GAAD,EAAc;AAC3B,eAAKrB,MAAL,CAAY+G,OAAZ,CAAoBL,CAAC,IAAIA,CAAC,CAACrF,GAAF,KAAUA,GAAV,IAAiB,KAAK8H,KAAL,CAAWzC,CAAX,CAA1C;AACH,SAxW4B,CA0W7B;;;AACOkD,QAAAA,cAAc,GAAG;AACpB,eAAK5J,MAAL,CAAY+G,OAAZ,CAAoBL,CAAC,IAAI,CAACA,CAAC,CAACuB,SAAF,EAAD,IAAkB,KAAKkB,KAAL,CAAWzC,CAAX,CAA3C;AACA,eAAK+C,cAAL;AACH,SA9W4B,CAgX7B;;;AACOA,QAAAA,cAAc,CAACI,QAAD,EAAqB;AACtC,iBAAO,KAAKvJ,UAAL,CAAgByI,MAAhB,GAAyB,CAAhC,EAAmC;AAC/B;AAAA;AAAA,kCAAOe,UAAP,CAAkB,KAAKxJ,UAAL,CAAgBgD,GAAhB,GAAsBvC,GAAxC;AACH;;AACD,cAAI8I,QAAJ,EAAc;AACVhI,YAAAA,EAAE,CAACmG,WAAH,CAAe,cAAf;AACH;AACJ,SAxX4B,CA0X7B;;;AACQ0B,QAAAA,gBAAgB,CAAC9I,IAAD,EAAe;AACnC,gBAAMgH,IAAI,GAAG,KAAKtH,UAAL,CAAgB2B,MAAhB,CAAuB,MAAvB,EAA+BrB,IAA/B,CAAb;AACAgH,UAAAA,IAAI,IAAI;AAAA;AAAA,gCAAOkC,UAAP,CAAkBlC,IAAI,CAAC7G,GAAvB,CAAR;AACH,SA9X4B,CAgY7B;;;AACOgJ,QAAAA,cAAc,CAACjJ,EAAD,EAAa;AAC9B,gBAAM8G,IAAI,GAAG,KAAKtH,UAAL,CAAgB2B,MAAhB,CAAuB,IAAvB,EAA6BnB,EAA7B,CAAb;AACA8G,UAAAA,IAAI,IAAI;AAAA;AAAA,gCAAOkC,UAAP,CAAkBlC,IAAI,CAAC7G,GAAvB,CAAR;AACH;;AApY4B,O", "sourcesContent": ["import { easing, instantiate, js, Node, Prefab, Tween, tween, UIOpacity, v3, view, Widget } from \"cc\"\r\nimport BasePnlCtrl from \"../base/BasePnlCtrl\"\r\nimport CoreEventType from \"../event/CoreEventType\"\r\nimport { loader } from \"../utils/ResLoader\"\r\n\r\nexport default class ViewCtrlMgr {\r\n\r\n    public node: Node = null\r\n\r\n    private caches: Map<string, BasePnlCtrl> = new Map<string, BasePnlCtrl>()\r\n    private opened: BasePnlCtrl[] = []\r\n    private masks: Node[] = [] // 遮罩列表\r\n    private pnlOpenIndex: number = 0 // 打开顺序id\r\n    private pnlIndexConf: any = {} // ui的层级关系配置\r\n\r\n    private loadQueues: LoadPnlInfo[] = [] // 当前加载队列\r\n    private loadId: number = 0\r\n\r\n    public getOpened() {\r\n        return this.opened\r\n    }\r\n\r\n    public setPnlIndexConf(conf: any) {\r\n        this.pnlIndexConf = conf\r\n    }\r\n\r\n    // 添加到加载队列\r\n    private addLoadQueue(name: string) {\r\n        return this.loadQueues.add({ id: ++this.loadId, name: name, url: '' })\r\n    }\r\n\r\n    private hasLoadQueue(name: string) {\r\n        return this.loadQueues.has('name', name)\r\n    }\r\n\r\n    private async __load(name: string, info: LoadPnlInfo) {\r\n        // console.time('load ' + name)\r\n        let pfb: Prefab = null, url: string = '', mod: string = ''\r\n        let [wind, pnl] = name.split('/')\r\n        if (!pnl) {// 没有输入模块的情况下\r\n            // 先默认从当前模块找\r\n            const head = ut.initialUpperCase(wind).replace('Pnl', '')\r\n            mod = mc.currWindName\r\n            info.url = url = `view/${mod}/${head}Pnl`\r\n            loader.error = false\r\n            pfb = await loader.loadRes(url, Prefab)\r\n            loader.error = true\r\n            if (!pfb) { //如果没有就从common里面找\r\n                mod = 'common'\r\n                info.url = url = `view/${mod}/${head}Pnl`\r\n                pfb = await loader.loadRes(url, Prefab)\r\n            }\r\n        } else {\r\n            const head = ut.initialUpperCase(pnl).replace('Pnl', '')\r\n            mod = wind\r\n            info.url = url = `view/${mod}/${head}Pnl`\r\n            pfb = await loader.loadRes(url, Prefab)\r\n        }\r\n        // console.timeEnd('load ' + name)\r\n        if (!this.loadQueues.remove('id', info.id)) {\r\n            pfb = null\r\n        }\r\n        info.id = 0 //这里表示已经加载完成了 用于后续弹出是否重试检测是的时候\r\n        return Promise.resolve({ mod, url, pfb })\r\n    }\r\n\r\n    // 加载一个Pnl\r\n    public async __loadPnl(name: string, info: LoadPnlInfo): Promise<BasePnlCtrl> {\r\n        const { mod, url, pfb } = await this.__load(name, info)\r\n        if (!pfb) {\r\n            return Promise.resolve(null)\r\n        }\r\n        const it = mc.instantiate(pfb, this.node)\r\n        const className = it.name + 'Ctrl'\r\n        if (!js.getClassByName(className)) {\r\n            logger.error('loadPnl error! not found class ' + className)\r\n            return Promise.resolve(null)\r\n        }\r\n        let pnl = it.getComponent(className)\r\n        if (!pnl) {\r\n            pnl = it.addComponent(className)\r\n        }\r\n        if (!pnl || !(pnl instanceof BasePnlCtrl)) {\r\n            logger.error('loadPnl error! not found class ' + className)\r\n            return Promise.resolve(null)\r\n        }\r\n        pnl.key = name\r\n        pnl.mod = mod\r\n        pnl.url = url\r\n        // 这里检查一下 是否还没有加载属性\r\n        if (!pnl._isLoadProperty) {\r\n            logger.error('load pnl error! not load property. at=' + className)\r\n            pnl.loadProperty()\r\n        }\r\n        it.active = false\r\n        await pnl.__create()\r\n        this.caches.set(url, pnl)\r\n        return Promise.resolve(pnl)\r\n    }\r\n\r\n    // 获取缓存的Pnl\r\n    private __getForCache(name: string) {\r\n        let [wind, key] = name.split('/'), ui: BasePnlCtrl = null\r\n        if (!key) {\r\n            const head = ut.initialUpperCase(wind).replace('Pnl', '')\r\n            ui = this.caches.get(`view/${mc.currWindName}/${head}Pnl`)\r\n            if (!ui) {\r\n                ui = this.caches.get(`view/common/${head}Pnl`)\r\n            }\r\n        } else {\r\n            const head = ut.initialUpperCase(key).replace('Pnl', '')\r\n            ui = this.caches.get(`view/${wind}/${head}Pnl`)\r\n        }\r\n        return ui\r\n    }\r\n\r\n    // 预加载\r\n    public async preloadPnl(name: string) {\r\n        let pnl = this.__getForCache(name)\r\n        if (!pnl && !this.hasLoadQueue(name)) {\r\n            pnl = await this.__loadPnl(name, this.addLoadQueue(name))\r\n        }\r\n        return pnl\r\n    }\r\n\r\n    // 获取一个遮罩\r\n    private getMask(ui: BasePnlCtrl) {\r\n        let it = this.masks.pop()\r\n        if (!it) {\r\n            const pfb = assetsMgr.getPrefab('PNL_MASK')\r\n            if (pfb) {\r\n                it = instantiate(pfb)\r\n            }\r\n        }\r\n        if (it) {\r\n            it.parent = this.node\r\n            it.active = true\r\n            it.opacity = 150\r\n            // it.zIndex = ui.node.zIndex - 1\r\n            it.zIndex = ui.node.zIndex\r\n        }\r\n        ui.mask = it\r\n    }\r\n\r\n    private putMask(ui: BasePnlCtrl) {\r\n        if (ui && ui.mask) {\r\n            ui.mask.parent = null\r\n            this.masks.push(ui.mask)\r\n            ui.mask = null\r\n        }\r\n    }\r\n\r\n    // 播放显示的动作\r\n    private async playShowAction(ui: BasePnlCtrl) {\r\n        const widget = ui.Component(Widget)\r\n        if (widget && widget.enabled) {\r\n            widget.updateAlignment()\r\n            widget.enabled = false\r\n        }\r\n        Tween.stopAllByTarget(ui.node)\r\n        ui.node.scale = v3(0.4, 0.4)\r\n        tween(ui.node).to(0.25, { scale: v3(1, 1) }, { easing: easing.backOut }).start()\r\n        if (ui.mask) {\r\n            const uiOpacity = ui.mask.Component(UIOpacity)\r\n            Tween.stopAllByTarget(uiOpacity)\r\n            uiOpacity.opacity = 0\r\n            tween(uiOpacity).to(0.3, { opacity: 120 }, { easing: easing.sineOut }).start()\r\n        }\r\n        await ut.wait(0.25)\r\n        if (ui.isValid) {\r\n            ui.onPlayActionComplete()\r\n            eventCenter.emit(CoreEventType.PNL_ENTER_PLAY_DONE, ui)\r\n        }\r\n    }\r\n\r\n    // 适应大小\r\n    private adaptRootSize(ui: BasePnlCtrl) {\r\n        if (!ui) {\r\n            return\r\n        }\r\n        const root = ui.Child('root') || ui.Child('root_n')\r\n        if (!root) {\r\n            return\r\n        }\r\n        const wsize = view.getVisibleSize()\r\n        const dsize = view.getDesignResolutionSize()\r\n        const rsize = root.transform.contentSize\r\n        // 算出宽度比例\r\n        let scale = (rsize.width / dsize.width * wsize.width) / rsize.width\r\n        // 如果高度超过了\r\n        const height = wsize.height - ui.adaptHeight\r\n        if (rsize.height * scale > height) {\r\n            scale = height / rsize.height\r\n        }\r\n        root.scale = v3(Math.min(1.2, scale))\r\n    }\r\n\r\n    private getNextOpenIndex() {\r\n        return ++this.pnlOpenIndex\r\n    }\r\n\r\n    private pushPnl(ui: BasePnlCtrl) {\r\n        ui.__open_index = this.getNextOpenIndex()\r\n        this.opened.remove('url', ui.url)\r\n        this.opened.push(ui)\r\n        this.updatePnlIndex()\r\n    }\r\n\r\n    private popPnl(ui: BasePnlCtrl) {\r\n        this.opened.remove('url', ui.url)\r\n        this.updatePnlIndex()\r\n    }\r\n\r\n    // 刷新ui层级关系\r\n    private updatePnlIndex() {\r\n        // 排个序根据打开顺序\r\n        const list = this.opened.sort((a, b) => a.__open_index - b.__open_index).map((m, i) => {\r\n            const index = i + 1\r\n            return {\r\n                ui: m,\r\n                key: m.key,\r\n                initIndex: index,\r\n                sortIndex: index * 1000\r\n            }\r\n        })\r\n        list.forEach(m => {\r\n            const conf = this.pnlIndexConf[m.key]\r\n            if (!conf) {\r\n                return\r\n            }\r\n            const lt: string[] = conf.lt?.slice()\r\n            // 找出大于的 调整自己的位置 在大于的上面\r\n            if (conf.gt) {\r\n                const arr = list.filter(pnl => conf.gt.has(pnl.key)).sort((a, b) => b.sortIndex - a.sortIndex), temp = arr[0]\r\n                if (temp && m.sortIndex < temp.sortIndex) {\r\n                    m.sortIndex = temp.sortIndex + m.initIndex\r\n                }\r\n                // 这里如果小于的也在 当前大于的大于里面 就删除掉\r\n                lt && arr.forEach(m => {\r\n                    const gt = this.pnlIndexConf[m.key]?.gt\r\n                    gt && lt.delete(s => gt.has(s))\r\n                })\r\n            }\r\n            // 找出小于的 调整小于的位置 在自己的上面\r\n            lt && list.filter(pnl => lt.has(pnl.key)).forEach(temp => {\r\n                if (temp.sortIndex < m.sortIndex) {\r\n                    temp.sortIndex = m.sortIndex + temp.initIndex\r\n                }\r\n            })\r\n        })\r\n        list.sort((a, b) => a.sortIndex - b.sortIndex).forEach((m, i) => m.ui.setIndex(i * 10))\r\n        this.opened.sort((a, b) => a.node.zIndex - b.node.zIndex)\r\n    }\r\n\r\n    // 显示一个UI\r\n    public async show(pnl: string | BasePnlCtrl, ...params: any): Promise<BasePnlCtrl> {\r\n        mc.lockTouch('__show_pnl__')\r\n        let ui: BasePnlCtrl = null\r\n        if (typeof (pnl) === 'string') {\r\n            ui = this.__getForCache(pnl)\r\n            if (!ui && !this.hasLoadQueue(pnl)) {\r\n                const data = this.addLoadQueue(pnl)\r\n                data.params = params\r\n                eventCenter.emit(CoreEventType.LOAD_BEGIN_PNL, data)\r\n                ui = await this.__loadPnl(pnl, data)\r\n                eventCenter.emit(CoreEventType.LOAD_END_PNL, data)\r\n            }\r\n        } else if (pnl.isValid && pnl._state !== 'clean') {\r\n            ui = pnl\r\n        } else {\r\n            return this.show(pnl.key, ...params)\r\n        }\r\n        if (!ui || !ui.isValid) {\r\n            mc.unlockTouch('__show_pnl__')\r\n            return null\r\n        } else if (!ui.getActive()) {\r\n            this.pushPnl(ui)\r\n            ui.setActive(true)\r\n            ui.isMask && this.getMask(ui)\r\n            ui.__enter(...params)\r\n            // this.adaptRootSize(ui)\r\n            ui.setOpacity(255)\r\n            // 发送进入事件\r\n            eventCenter.emit(CoreEventType.PNL_ENTER, ui)\r\n            if (ui.isAct) {\r\n                this.playShowAction(ui).then(() => mc.unlockTouch('__show_pnl__'))\r\n            } else {\r\n                ui.node.scale = v3(1, 1)\r\n                ui.onPlayActionComplete()\r\n                eventCenter.emit(CoreEventType.PNL_ENTER_PLAY_DONE, ui)\r\n                mc.unlockTouch('__show_pnl__')\r\n            }\r\n        } else {\r\n            mc.unlockTouch('__show_pnl__')\r\n        }\r\n        return ui\r\n    }\r\n\r\n    // 隐藏一个Pnl\r\n    public hide(val: BasePnlCtrl | string) {\r\n        const ui: BasePnlCtrl = val instanceof BasePnlCtrl ? val : this.__getForCache(val)\r\n        if (!ui || !ui.isValid) {\r\n            return\r\n        }\r\n        this.popPnl(ui)\r\n        this.putMask(ui)\r\n        if (ui.getActive()) {\r\n            ui.__remove()\r\n            ui.setActive(false)\r\n            eventCenter.emit(CoreEventType.PNL_LEAVE, ui)\r\n        }\r\n    }\r\n\r\n    // 隐藏所有Pnl\r\n    public hideAll(val?: string, ignores?: string) {\r\n        if (!val) {\r\n            const arr = ignores ? ignores.split('|') : []\r\n            for (let i = this.opened.length - 1; i >= 0; i--) {\r\n                const m = this.opened[i]\r\n                // 这里关闭所有的时候 忽略掉不清理的UI\r\n                if (m.isClean && arr.indexOf(m.key) === -1) {\r\n                    this.opened.splice(i, 1)\r\n                    this.putMask(m)\r\n                    if (m.getActive()) {\r\n                        m.__remove()\r\n                        m.setActive(false)\r\n                        eventCenter.emit(CoreEventType.PNL_LEAVE, m)\r\n                    }\r\n                }\r\n            }\r\n            this.updatePnlIndex()\r\n        } else {\r\n            val.split('|').forEach(m => this.hide(m))\r\n        }\r\n    }\r\n\r\n    // 清理一个Pnl\r\n    public clean(val: BasePnlCtrl | string, force?: boolean) {\r\n        const ui: BasePnlCtrl = val instanceof BasePnlCtrl ? val : this.__getForCache(val)\r\n        if (!ui || (!ui.isClean && !force)) {\r\n            return\r\n        }\r\n        this.hide(ui)\r\n        ui.__clean()\r\n        ui.node.destroy()\r\n        this.caches.delete(ui.url)\r\n        loader.releaseRes(ui.url, Prefab)\r\n    }\r\n\r\n    // 清理所有Pnl\r\n    public cleanAll(val?: string, force?: boolean) {\r\n        if (!val) {\r\n            this.caches.forEach(m => this.clean(m, force))\r\n            this.cleanLoadQueue()\r\n        } else {\r\n            val.split('|').forEach(m => {\r\n                this.clean(m, force)\r\n                this.giveupLoadByName(m)\r\n            })\r\n        }\r\n    }\r\n\r\n    // 清理pnl根据模块\r\n    public cleanByMod(mod: string) {\r\n        this.caches.forEach(m => m.mod === mod && this.clean(m))\r\n    }\r\n\r\n    // 清理所有未打开的Pnl\r\n    public cleanAllUnused() {\r\n        this.caches.forEach(m => !m.getActive() && this.clean(m))\r\n        this.cleanLoadQueue()\r\n    }\r\n\r\n    // 清理加载队列\r\n    public cleanLoadQueue(isUnlock?: boolean) {\r\n        while (this.loadQueues.length > 0) {\r\n            loader.giveupLoad(this.loadQueues.pop().url)\r\n        }\r\n        if (isUnlock) {\r\n            mc.unlockTouch('__show_pnl__')\r\n        }\r\n    }\r\n\r\n    // 放弃加载\r\n    private giveupLoadByName(name: string) {\r\n        const data = this.loadQueues.remove('name', name)\r\n        data && loader.giveupLoad(data.url)\r\n    }\r\n\r\n    // 放弃当前加载\r\n    public giveupLoadById(id: number) {\r\n        const data = this.loadQueues.remove('id', id)\r\n        data && loader.giveupLoad(data.url)\r\n    }\r\n}"]}