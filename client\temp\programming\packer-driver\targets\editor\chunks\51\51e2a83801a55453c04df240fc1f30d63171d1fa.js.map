{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts"], "names": ["setItemData", "it", "data", "i", "cb", "target", "active", "opacity", "call", "error", "Layout", "misc", "Node", "Prefab", "ScrollView", "ScrollViewEx", "ScrollViewPlus", "prototype", "Items", "list", "prefab", "childs", "content", "children", "item", "count", "length", "plus", "Component", "reset", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "items", "l", "ChildrenCount", "Data", "mc", "instantiate", "updateNodeShow", "AddItem", "findIndex", "m", "undefined", "Find", "predicate", "thisArg", "find", "IsEmpty", "some", "List", "len", "ex", "GetItemNode", "getItemNode", "SelectItemToCentre", "index", "stopAutoScroll", "lay", "updateLayout", "width", "tx", "spacingX", "paddingLeft", "pw", "parent", "cx", "x", "clampf", "Math", "min", "scrollToLeft"], "mappings": ";;;;;AA8DA,WAASA,WAAT,CAAqBC,EAArB,EAA+BC,IAA/B,EAA0CC,CAA1C,EAAqDC,EAArD,EAAmEC,MAAnE,EAAgF;AAC5EJ,IAAAA,EAAE,CAACK,MAAH,GAAY,IAAZ;AACAL,IAAAA,EAAE,CAACM,OAAH,GAAa,GAAb;;AACA,QAAI,CAACH,EAAL,EAAS;AACL;AACH,KAFD,MAEO,IAAIC,MAAJ,EAAY;AACfD,MAAAA,EAAE,CAACI,IAAH,CAAQH,MAAR,EAAgBJ,EAAhB,EAAoBC,IAApB,EAA0BC,CAA1B;AACH,KAFM,MAEA;AACHC,MAAAA,EAAE,CAACH,EAAD,EAAKC,IAAL,EAAWC,CAAX,CAAF;AACH;AACJ,G,CAED;;;;;;;;;;;;;;;;;;AAtESM,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,U,OAAAA,U;;AACrCC,MAAAA,Y;;AACAC,MAAAA,c;;;;;;AANP;AACA;AACA;;;;;AAMA;AACAF,MAAAA,UAAU,CAACG,SAAX,CAAqBC,KAArB,GAA6B,UAAaC,IAAb,EAAiCC,MAAjC,EAA+ChB,EAA/C,EAA4FC,MAA5F,EAA0G;AACnI,YAAIF,CAAC,GAAG,CAAR;AAAA,YAAWkB,MAAM,GAAG,KAAKC,OAAL,CAAaC,QAAjC;AAAA,YAA2CC,IAAI,GAAGH,MAAM,CAAC,CAAD,CAAxD;AACA,YAAII,KAAK,GAAG,CAAZ;;AACA,YAAI,OAAQN,IAAR,KAAkB,QAAtB,EAAgC;AAC5BM,UAAAA,KAAK,GAAGN,IAAR;AACAA,UAAAA,IAAI,GAAG,IAAP;AACH,SAHD,MAGO;AACHM,UAAAA,KAAK,GAAGN,IAAI,CAACO,MAAb;AACH;;AACD,YAAI,OAAQN,MAAR,KAAoB,UAAxB,EAAoC;AAChCf,UAAAA,MAAM,GAAGD,EAAT;AACAA,UAAAA,EAAE,GAAGgB,MAAL;AACH,SAHD,MAGO,IAAIA,MAAM,YAAYR,IAAlB,IAA0BQ,MAAM,YAAYP,MAAhD,EAAwD;AAC3DW,UAAAA,IAAI,GAAGJ,MAAP;AACH;;AACD,YAAI,CAACI,IAAL,EAAW;AACP,iBAAOf,KAAK,CAAC,uBAAD,CAAZ;AACH;;AACD,cAAMkB,IAAI,GAAG,KAAKC,SAAL;AAAA;AAAA,6CAAb;AACAD,QAAAA,IAAI,QAAJ,IAAAA,IAAI,CAAEE,KAAN;;AACA,YAAIF,IAAJ,YAAIA,IAAI,CAAEG,aAAV,EAAyB;AACrB,iBAAOH,IAAI,CAACI,KAAL,CAAWZ,IAAX,EAAiBK,IAAjB,EAAuBpB,EAAvB,EAA2BC,MAA3B,CAAP;AACH;;AACD,aAAK,IAAI2B,CAAC,GAAG,KAAKV,OAAL,CAAaW,aAA1B,EAAyC9B,CAAC,GAAG6B,CAA7C,EAAgD7B,CAAC,EAAjD,EAAqD;AACjD,gBAAMF,EAAE,GAAGoB,MAAM,CAAClB,CAAD,CAAjB;;AACA,cAAIA,CAAC,GAAGsB,KAAR,EAAe;AACXzB,YAAAA,WAAW,CAACC,EAAD,EAAKkB,IAAI,IAAIA,IAAI,CAAChB,CAAD,CAAjB,EAAsBA,CAAtB,EAAyBC,EAAzB,EAA6BC,MAA7B,CAAX;AACH,WAFD,MAEO;AACHJ,YAAAA,EAAE,CAACiC,IAAH,GAAU,IAAV;AACAjC,YAAAA,EAAE,CAACK,MAAH,GAAY,KAAZ;AACH;AACJ;;AACD,eAAOH,CAAC,GAAGsB,KAAX,EAAkBtB,CAAC,EAAnB,EAAuB;AACnBH,UAAAA,WAAW,CAACmC,EAAE,CAACC,WAAH,CAAeZ,IAAf,EAAqB,KAAKF,OAA1B,CAAD,EAAqCH,IAAI,IAAIA,IAAI,CAAChB,CAAD,CAAjD,EAAsDA,CAAtD,EAAyDC,EAAzD,EAA6DC,MAA7D,CAAX;AACH,SAlCkI,CAmCnI;;;AACAsB,QAAAA,IAAI,QAAJ,IAAAA,IAAI,CAAEU,cAAN;AACH,OArCD,C,CAuCA;;;AACAvB,MAAAA,UAAU,CAACG,SAAX,CAAqBqB,OAArB,GAA+B,UAAUlC,EAAV,EAA6CC,MAA7C,EAA2D;AACtF,YAAIF,CAAC,GAAG,KAAKmB,OAAL,CAAaC,QAAb,CAAsBgB,SAAtB,CAAgCC,CAAC,IAAI,CAACA,CAAC,CAAClC,MAAxC,CAAR;AACA,YAAIL,EAAE,GAAG,IAAT;;AACA,YAAIE,CAAC,KAAK,CAAC,CAAX,EAAc;AACVF,UAAAA,EAAE,GAAG,KAAKqB,OAAL,CAAaC,QAAb,CAAsBpB,CAAtB,CAAL;AACH,SAFD,MAEO;AACHA,UAAAA,CAAC,GAAG,KAAKmB,OAAL,CAAaW,aAAjB;AACAhC,UAAAA,EAAE,GAAGkC,EAAE,CAACC,WAAH,CAAe,KAAKd,OAAL,CAAcC,QAAd,CAAuB,CAAvB,CAAf,EAA0C,KAAKD,OAA/C,CAAL;AACH;;AACDrB,QAAAA,EAAE,CAACK,MAAH,GAAY,IAAZ;AACAN,QAAAA,WAAW,CAACC,EAAD,EAAKE,CAAL,EAAQsC,SAAR,EAAmBrC,EAAnB,EAAuBC,MAAvB,CAAX;AACH,OAXD;;AA0BAS,MAAAA,UAAU,CAACG,SAAX,CAAqByB,IAArB,GAA4B,UAAUC,SAAV,EAA2EC,OAA3E,EAAgG;AACxH,eAAO,KAAKtB,OAAL,CAAaC,QAAb,CAAsBsB,IAAtB,CAA2BF,SAA3B,EAAsCC,OAAtC,CAAP;AACH,OAFD,C,CAIA;;;AACA9B,MAAAA,UAAU,CAACG,SAAX,CAAqB6B,OAArB,GAA+B,YAAY;AACvC,eAAO,CAAC,KAAKxB,OAAL,CAAaC,QAAb,CAAsBwB,IAAtB,CAA2BP,CAAC,IAAIA,CAAC,CAAClC,MAAlC,CAAR;AACH,OAFD,C,CAIA;;;AACAQ,MAAAA,UAAU,CAACG,SAAX,CAAqB+B,IAArB,GAA4B,UAAUC,GAAV,EAAuB7C,EAAvB,EAA2DC,MAA3D,EAAyE;AACjG,cAAM6C,EAAE,GAAG,KAAKtB,SAAL;AAAA;AAAA,yCAAX;;AACA,YAAIsB,EAAJ,EAAQ;AACJ,iBAAOA,EAAE,CAAC/B,IAAH,CAAQ8B,GAAR,EAAa7C,EAAb,EAAiBC,MAAjB,CAAP;AACH,SAFD,MAEO;AACHI,UAAAA,KAAK,CAAC,+BAAD,CAAL;AACH;AACJ,OAPD,C,CASA;;;AACAK,MAAAA,UAAU,CAACG,SAAX,CAAqBkC,WAArB,GAAmC,YAAY;AAAA;;AAC3C,eAAO,yBAAKvB,SAAL;AAAA;AAAA,8EAA8BwB,WAA9B,OAA+C,KAAK9B,OAAL,CAAaC,QAAb,CAAsB,CAAtB,CAAtD;AACH,OAFD,C,CAIA;;;AACAT,MAAAA,UAAU,CAACG,SAAX,CAAqBoC,kBAArB,GAA0C,UAAUC,KAAV,EAAyB;AAC/D,aAAKC,cAAL;;AACA,YAAID,KAAK,KAAK,CAAC,CAAf,EAAkB;AACd,gBAAME,GAAG,GAAG,KAAKlC,OAAL,CAAaM,SAAb,CAAuBlB,MAAvB,CAAZ;AACA8C,UAAAA,GAAG,CAACC,YAAJ;AACA,gBAAMC,KAAK,GAAG,KAAKpC,OAAL,CAAaC,QAAb,CAAsB,CAAtB,EAAyBmC,KAAvC;AACA,gBAAMC,EAAE,GAAG,CAACD,KAAK,GAAGF,GAAG,CAACI,QAAb,IAAyBN,KAAzB,GAAiCI,KAAK,GAAG,GAAzC,GAA+CF,GAAG,CAACK,WAA9D,CAJc,CAI4D;;AAC1E,gBAAMC,EAAE,GAAG,KAAKxC,OAAL,CAAayC,MAAb,CAAoBL,KAA/B;AACA,gBAAMM,EAAE,GAAGF,EAAE,GAAG,GAAhB,CANc,CAMM;;AACpB,eAAKxC,OAAL,CAAa2C,CAAb,GAAiBtD,IAAI,CAACuD,MAAL,CAAYF,EAAE,GAAGL,EAAjB,EAAqBQ,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYN,EAAE,GAAG,KAAKxC,OAAL,CAAaoC,KAA9B,CAArB,EAA2D,CAA3D,CAAjB;AACH,SARD,MAQO;AACH,eAAKW,YAAL;AACH;AACJ,OAbD", "sourcesContent": ["/**\r\n * ScrollView 扩展方法\r\n */\r\n\r\nimport { error, Layout, misc, Node, Prefab, ScrollView } from \"cc\"\r\nimport ScrollViewEx from \"../component/ScrollViewEx\"\r\nimport ScrollViewPlus from \"../component/ScrollViewPlus\"\r\n\r\n// 填充列表\r\nScrollView.prototype.Items = function <T>(list: T[] | number, prefab?: any, cb?: (it: Node, data: T, i: number) => void, target?: any) {\r\n    let i = 0, childs = this.content.children, item = childs[0]\r\n    let count = 0\r\n    if (typeof (list) === 'number') {\r\n        count = list\r\n        list = null\r\n    } else {\r\n        count = list.length\r\n    }\r\n    if (typeof (prefab) === 'function') {\r\n        target = cb\r\n        cb = prefab\r\n    } else if (prefab instanceof Node || prefab instanceof Prefab) {\r\n        item = prefab\r\n    }\r\n    if (!item) {\r\n        return error('必须满足content中有一个可拷贝的节点')\r\n    }\r\n    const plus = this.Component(ScrollViewPlus)\r\n    plus?.reset()\r\n    if (plus?.isFrameRender) {\r\n        return plus.items(list, item, cb, target)\r\n    }\r\n    for (let l = this.content.ChildrenCount; i < l; i++) {\r\n        const it = childs[i]\r\n        if (i < count) {\r\n            setItemData(it, list && list[i], i, cb, target)\r\n        } else {\r\n            it.Data = null\r\n            it.active = false\r\n        }\r\n    }\r\n    for (; i < count; i++) {\r\n        setItemData(mc.instantiate(item, this.content), list && list[i], i, cb, target)\r\n    }\r\n    // 刷新一下显示\r\n    plus?.updateNodeShow()\r\n}\r\n\r\n// 添加一个\r\nScrollView.prototype.AddItem = function (cb: (it: Node, i: number) => void, target?: any) {\r\n    let i = this.content.children.findIndex(m => !m.active)\r\n    let it = null\r\n    if (i !== -1) {\r\n        it = this.content.children[i]\r\n    } else {\r\n        i = this.content.ChildrenCount\r\n        it = mc.instantiate(this.content!.children[0], this.content!)\r\n    }\r\n    it.active = true\r\n    setItemData(it, i, undefined, cb, target)\r\n}\r\n\r\nfunction setItemData(it: Node, data: any, i: number, cb: Function, target: any) {\r\n    it.active = true\r\n    it.opacity = 255\r\n    if (!cb) {\r\n        return\r\n    } else if (target) {\r\n        cb.call(target, it, data, i)\r\n    } else {\r\n        cb(it, data, i)\r\n    }\r\n}\r\n\r\n// 查找content的子节点\r\nScrollView.prototype.Find = function (predicate: (value: Node, index: number, obj: Node[]) => unknown, thisArg?: any): Node {\r\n    return this.content.children.find(predicate, thisArg)!\r\n}\r\n\r\n//\r\nScrollView.prototype.IsEmpty = function () {\r\n    return !this.content.children.some(m => m.active)\r\n}\r\n\r\n// list填充\r\nScrollView.prototype.List = function (len: number, cb?: (it: Node, i: number) => void, target?: any) {\r\n    const ex = this.Component(ScrollViewEx)\r\n    if (ex) {\r\n        return ex.list(len, cb, target)\r\n    } else {\r\n        error('List error, not ScrollViewEx!')\r\n    }\r\n}\r\n\r\n//\r\nScrollView.prototype.GetItemNode = function () {\r\n    return this.Component(ScrollViewEx)?.getItemNode() || this.content.children[0]\r\n}\r\n\r\n// 将选中的移动到中间\r\nScrollView.prototype.SelectItemToCentre = function (index: number) {\r\n    this.stopAutoScroll()\r\n    if (index !== -1) {\r\n        const lay = this.content.Component(Layout)\r\n        lay.updateLayout()\r\n        const width = this.content.children[0].width\r\n        const tx = (width + lay.spacingX) * index + width * 0.5 + lay.paddingLeft //当前位置\r\n        const pw = this.content.parent.width\r\n        const cx = pw * 0.5 //中间位置\r\n        this.content.x = misc.clampf(cx - tx, Math.min(0, pw - this.content.width), 0)\r\n    } else {\r\n        this.scrollToLeft()\r\n    }\r\n}\r\n"]}