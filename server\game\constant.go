package game

const (
	INIT_GOLD          = 10 //初始的金币
	INIT_EARNINGS      = 1  //初始的收益
	BEGIN_GAME_SHOP_ID = 1  //开始游戏的商店id
)

// 商店类型
const (
	_               = iota
	SHOP_TYPE_BEGIN //开始
)

// 商店状态
const (
	_                 = iota
	SHOP_STATE_NONE   //等待选择
	SHOP_STATE_SELECT //已选择
)

// 单独处理的物品id
const (
	SPECIAL_ITEM_RAND_ANIMAL   = 200000 //随机青铜变异动物
	SPECIAL_ITEM_GOLD_EARNINGS = 200001 //+金币和收益
	SPECIAL_ITEM_RAND_REMAINS  = 200002 //随机青铜遗物
)

// 地图节点类型
const (
	MAP_NODE_TYPE_BATTLE   = 1 //战斗节点
	MAP_NODE_TYPE_SHOP     = 2 //商店节点
	MAP_NODE_TYPE_EVENT    = 3 //事件节点
	MAP_NODE_TYPE_TREASURE = 4 //宝箱节点
	MAP_NODE_TYPE_PLAYER   = 5 //玩家节点
)
