System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, log, _class, _crd, ccclass, TestPnlCtrl;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      log = _cc.log;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "fc334MEzJNMJot2RLpqq1TV", "TestPnlCtrl", undefined);

      __checkObsolete__(['_decorator', 'log', 'EventTouch']);

      ({
        ccclass
      } = _decorator);

      _export("default", TestPnlCtrl = ccclass(_class = class TestPnlCtrl extends mc.BasePnlCtrl {
        //@autocode property begin
        //@end
        listenEventMaps() {
          return [];
        }

        onCreate() {
          var _this = this;

          return _asyncToGenerator(function* () {
            _this.setParam({
              isMask: false
            });
          })();
        }

        onEnter(data) {
          log('TestPnlCtrl onEnter');
        }

        onRemove() {
          log('TestPnlCtrl onRemove');
        }

        onClean() {
          log('TestPnlCtrl onClean');
        } // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        // path://button_be


        onClickButton(event, data) {
          log('onClickButton', data);
        } //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------


      }) || _class);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4c4c64c6b982b7d11aef9f2cc0d8f0cf5f06f143.js.map