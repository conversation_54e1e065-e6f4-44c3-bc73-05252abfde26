{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts"], "names": ["LoginType", "LoginState", "NotifyType", "AnimalCamp", "AnimalState", "MapNodeType"], "mappings": ";;;;;;;;;;;;;;AACA;2BACKA,S,0BAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;eAAAA,S;QAAAA,S,UAYL;;;4BACKC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;QAAAA,U,UAWL;;;4BACKC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;QAAAA,U,UAIL;;;4BACKC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;QAAAA,U,UAML;;;6BACKC,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;QAAAA,W,UAOL;;;6BACKC,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;QAAAA,W", "sourcesContent": ["\r\n// 登陆类型\r\nenum LoginType {\r\n    NONE = 'none',\r\n    GUEST = 'guest', //游客\r\n    ACCOUNT = 'account', //账号\r\n    WX = 'wx', //微信\r\n    GOOGLE = 'google',\r\n    APPLE = 'apple',\r\n    FACEBOOK = 'facebook',\r\n    TWITTER = 'twitter',\r\n    LINE = 'line',\r\n}\r\n\r\n// 登录状态\r\nenum LoginState {\r\n    SUCCEED,            //成功\r\n    FAILURE,            //登录失败\r\n    NOT_ACCOUNT_TOKEN,  //本地还没有token 显示登录按钮\r\n    VERSION_TOOLOW,     //版本过低\r\n    VERSION_TOOTALL,    //版本过高\r\n    BANACCOUNT_TIME,    //封禁时间\r\n    QUEUE_UP,           //需要排队\r\n    ENTER_GAME,         //有游戏数据\r\n}\r\n\r\n// 服务器的通知类型\r\nenum NotifyType {\r\n    NONE,\r\n}\r\n\r\n// 阵营\r\nenum AnimalCamp {\r\n    NONE,\r\n    FRIENDLY,\r\n    ENEMY,\r\n}\r\n\r\n// 动物状态\r\nenum AnimalState {\r\n    NONE,\r\n    STAND, //待机 以下都表示战斗中\r\n    ATTACK, //攻击\r\n    HIT, //受击\r\n}\r\n\r\n// 地图节点类型\r\nenum MapNodeType {\r\n    NONE,\r\n    BATTLE, //战斗节点\r\n    SHOP, //商店节点\r\n    EVENT, //事件节点\r\n    TREASURE, //宝箱节点\r\n    PLAYER, //玩家节点\r\n}\r\n\r\nexport {\r\n    LoginType,\r\n    LoginState,\r\n    NotifyType,\r\n    AnimalCamp,\r\n    AnimalState,\r\n    MapNodeType,\r\n}"]}