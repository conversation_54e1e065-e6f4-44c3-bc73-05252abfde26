System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, MapNodeType, MapNodeObj, _crd;

  function _reportPossibleCrUseOfMapNodeType(extras) {
    _reporterNs.report("MapNodeType", "../../common/constant/Enums", _context.meta, extras);
  }

  _export("default", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      MapNodeType = _unresolved_2.MapNodeType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3606amyNcVFOoHctjzjGsTX", "MapNodeObj", undefined);

      // 地图节点
      _export("default", MapNodeObj = class MapNodeObj {
        constructor() {
          this.type = (_crd && MapNodeType === void 0 ? (_reportPossibleCrUseOfMapNodeType({
            error: Error()
          }), MapNodeType) : MapNodeType).NONE;
          this.children = [];
        }

        init(data) {
          this.type = data.type;
          this.children = data.children || [];
          return this;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7fc74967813a9ddce1f02b9445f6fa31cbe3370e.js.map