import { error } from "cc"

// 获取动物的帧动画配置
function getAnimalFrameAnimConf(id: number) {
    const conf = ANIMAL_FRAME_ANIM_CONF[id]
    if (!conf) {
        error('getAnimalFrameAnimConf error. id: ' + id)
        return null
    } else if (!conf.url) {
        conf.url = `animal/${id}/animal_${id}_`
    }
    return conf
}

// 动物动画帧配置
const ANIMAL_FRAME_ANIM_CONF = {
    211001: { //狮子
        anims: [
            { name: 'idle', interval: 100, loop: false, frameIndexs: ['01'] },
            { name: 'attack', interval: 140, loop: false, frameIndexs: ['01', '07', '08', '09', '10'] },
            { name: 'hit', interval: 140, loop: false, frameIndexs: ['01', '12', '12'] },
            { name: 'die', interval: 160, loop: false, frameIndexs: ['01', '12', '13', '14'] },
        ]
    },
}

export {
    getAnimalFrameAnimConf,
}