19:04:02.922 debug: 2025/8/8 19:04:02
19:04:02.922 debug: Project: D:\Projects\cute-animals-client\client
19:04:02.922 debug: Targets: editor,preview
19:04:02.930 debug: Incremental file seems great.
19:04:02.931 debug: Engine path: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
19:04:02.943 debug: Initializing target [Editor]
19:04:02.943 debug: Loading cache
19:04:02.956 debug: Loading cache costs 13.01339999999982ms.
19:04:02.957 debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,gfx-empty,gfx-webgpu,3d,animation,skeletal-animation,2d,rich-text,mask,graphics,ui-skew,ui,affine-transform,particle,particle-2d,physics-framework,physics-cannon,physics-physx,physics-ammo,physics-builtin,physics-2d-framework,physics-2d-box2d-jsb,physics-2d-box2d,physics-2d-builtin,physics-2d-box2d-wasm,intersection-2d,primitive,profiler,occlusion-query,geometry-renderer,debug-renderer,audio,video,xr,light-probe,terrain,webview,tween,tiled-map,vendor-google,spine-3.8,spine-4.2,dragon-bones,marionette,procedural-animation,custom-pipeline,custom-pipeline-builtin-scripts,custom-pipeline-post-process,legacy-pipeline,websocket,websocket-server,meshopt
19:04:02.957 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
19:04:02.957 debug: Initializing target [Preview]
19:04:02.958 debug: Loading cache
19:04:02.969 debug: Loading cache costs 11.121799999998984ms.
19:04:02.969 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
19:04:03.021 debug: Sync engine features: 2d,affine-transform,animation,audio,base,custom-pipeline,dragon-bones,gfx-webgl,gfx-webgl2,graphics,intersection-2d,mask,particle-2d,physics-2d-box2d,profiler,rich-text,spine-3.8,tiled-map,tween,ui,video,webview,custom-pipeline
19:04:03.024 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "C:\\ProgramData\\cocos\\editors\\Creator\\3.8.6\\resources\\resources\\3d\\engine\\editor\\assets"
  },
  {
    "root": "db://assets/",
    "physical": "D:\\Projects\\cute-animals-client\\client\\assets"
  }
]
19:04:03.024 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///D:/Projects/cute-animals-client/client/assets/"
  }
}
19:04:03.025 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///D:/Projects/cute-animals-client/client/assets/"
  }
}
19:04:03.026 debug: Pulling asset-db.
19:04:03.093 debug: Fetch asset-db cost: 67.07419999999911ms.
19:04:03.094 debug: Build iteration starts.
Number of accumulated asset changes: 124
Feature changed: false
19:04:03.095 debug: Target(editor) build started.
19:04:03.098 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:14 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:14 GMT+0800 (中国标准时间)
19:04:03.098 debug: Inspect cce:/internal/x/cc
19:04:03.158 debug: transform url: 'cce:/internal/x/cc' costs: 60.30 ms
19:04:03.160 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
19:04:03.160 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
19:04:03.160 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
19:04:03.160 debug: Resolve cce:/internal/x/cc-fu/gfx-empty from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-empty.
19:04:03.160 debug: Resolve cce:/internal/x/cc-fu/gfx-webgpu from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgpu.
19:04:03.160 debug: Resolve cce:/internal/x/cc-fu/3d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/3d.
19:04:03.160 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
19:04:03.160 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
19:04:03.160 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
19:04:03.160 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
19:04:03.160 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
19:04:03.160 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
19:04:03.161 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
19:04:03.161 debug: Resolve cce:/internal/x/cc-fu/ui-skew from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui-skew.
19:04:03.161 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
19:04:03.161 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
19:04:03.161 debug: Resolve cce:/internal/x/cc-fu/particle from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle.
19:04:03.161 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
19:04:03.161 debug: Resolve cce:/internal/x/cc-fu/physics-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-framework.
19:04:03.161 debug: Resolve cce:/internal/x/cc-fu/physics-cannon from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-cannon.
19:04:03.161 debug: Resolve cce:/internal/x/cc-fu/physics-physx from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-physx.
19:04:03.161 debug: Resolve cce:/internal/x/cc-fu/physics-ammo from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-ammo.
19:04:03.161 debug: Resolve cce:/internal/x/cc-fu/physics-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-builtin.
19:04:03.161 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
19:04:03.161 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-jsb from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-jsb.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-wasm from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-wasm.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/primitive from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/primitive.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/geometry-renderer from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/geometry-renderer.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/xr from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/xr.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/light-probe from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/light-probe.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/terrain from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/terrain.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/vendor-google from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/vendor-google.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline-post-process from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline-post-process.
19:04:03.162 debug: Resolve cce:/internal/x/cc-fu/legacy-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/legacy-pipeline.
19:04:03.163 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:21 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:14 GMT+0800 (中国标准时间)
19:04:03.163 debug: Inspect cce:/internal/x/prerequisite-imports
19:04:03.207 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 44.70 ms
19:04:03.209 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
19:04:03.210 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
19:04:03.211 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
19:04:03.212 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
19:04:03.212 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
19:04:03.213 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
19:04:03.214 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/App.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/App.ts.
19:04:03.214 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts.
19:04:03.214 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts.
19:04:03.215 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts.
19:04:03.215 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseModel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseModel.ts.
19:04:03.216 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts.
19:04:03.216 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts.
19:04:03.217 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts.
19:04:03.217 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts.
19:04:03.218 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWdtCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWdtCtrl.ts.
19:04:03.218 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts.
19:04:03.219 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts.
19:04:03.219 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelRollNumber.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelRollNumber.ts.
19:04:03.219 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelTimer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelTimer.ts.
19:04:03.220 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelWaitDot.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelWaitDot.ts.
19:04:03.220 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts.
19:04:03.221 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts.
19:04:03.221 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts.
19:04:03.221 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts.
19:04:03.222 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiColor.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiColor.ts.
19:04:03.223 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiFrame.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiFrame.ts.
19:04:03.223 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts.
19:04:03.223 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts.
19:04:03.224 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.224 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendAnimation.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendAnimation.ts.
19:04:03.225 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendArray.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendArray.ts.
19:04:03.225 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendButton.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendButton.ts.
19:04:03.226 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendCC.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendCC.ts.
19:04:03.226 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendComponent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendComponent.ts.
19:04:03.226 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts.
19:04:03.227 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts.
19:04:03.227 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts.
19:04:03.227 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts.
19:04:03.228 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts.
19:04:03.228 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendToggleContainer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendToggleContainer.ts.
19:04:03.229 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendVec.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendVec.ts.
19:04:03.229 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts.
19:04:03.229 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts.
19:04:03.230 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts.
19:04:03.230 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts.
19:04:03.230 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AudioMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AudioMgr.ts.
19:04:03.231 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts.
19:04:03.231 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NodePoolMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NodePoolMgr.ts.
19:04:03.232 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts.
19:04:03.232 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/StorageMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/StorageMgr.ts.
19:04:03.233 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts.
19:04:03.233 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts.
19:04:03.234 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/EventCenter.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/EventCenter.ts.
19:04:03.234 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts.
19:04:03.235 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts.
19:04:03.235 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Utils.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Utils.ts.
19:04:03.235 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/proto/ProtoHelper.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/proto/ProtoHelper.ts.
19:04:03.236 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/LocalConfig.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/LocalConfig.ts.
19:04:03.236 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/AnimalFrameAnimConf.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/AnimalFrameAnimConf.ts.
19:04:03.237 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts.
19:04:03.237 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Constant.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Constant.ts.
19:04:03.237 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/DataType.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/DataType.ts.
19:04:03.238 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/ECode.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/ECode.ts.
19:04:03.238 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:03.239 debug: Detected change: file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts. Last mtime: Thu Jul 31 2025 17:53:45 GMT+0800 (中国标准时间)@39e2e7e8-12e8-4bd3-b593-a301ca5b3dc7, Current mtime: Fri Aug 08 2025 18:34:31 GMT+0800 (中国标准时间)@39e2e7e8-12e8-4bd3-b593-a301ca5b3dc7
19:04:03.239 debug: Inspect file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts
19:04:03.268 debug: transform url: 'file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts' costs: 29.30 ms
19:04:03.269 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/interface.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/interface.ts.
19:04:03.270 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts.
19:04:03.270 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/JsbEvent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/JsbEvent.ts.
19:04:03.270 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts.
19:04:03.271 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NotEvent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NotEvent.ts.
19:04:03.272 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts.
19:04:03.272 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/LoadProgressHelper.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/LoadProgressHelper.ts.
19:04:03.273 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts.
19:04:03.273 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts.
19:04:03.273 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts.
19:04:03.274 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts.
19:04:03.274 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts.
19:04:03.275 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts.
19:04:03.275 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts.
19:04:03.276 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts.
19:04:03.276 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:03.276 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts.
19:04:03.277 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts.
19:04:03.277 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts.
19:04:03.278 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseCondition.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseCondition.ts.
19:04:03.278 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseDecorator.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseDecorator.ts.
19:04:03.278 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts.
19:04:03.279 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts.
19:04:03.279 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts.
19:04:03.280 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts.
19:04:03.281 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/BuffObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/BuffObj.ts.
19:04:03.281 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts.
19:04:03.282 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/RandomObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/RandomObj.ts.
19:04:03.282 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts.
19:04:03.282 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts.
19:04:03.283 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalStateObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalStateObj.ts.
19:04:03.283 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/EncounterObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/EncounterObj.ts.
19:04:03.284 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/FoodObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/FoodObj.ts.
19:04:03.284 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts.
19:04:03.284 debug: Detected change: file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts. Last mtime: Mon Aug 04 2025 20:13:32 GMT+0800 (中国标准时间)@ce653eb4-22b7-427a-8bfe-bf9a030a7d28, Current mtime: Fri Aug 08 2025 18:38:08 GMT+0800 (中国标准时间)@ce653eb4-22b7-427a-8bfe-bf9a030a7d28
19:04:03.284 debug: Inspect file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts
19:04:03.344 debug: transform url: 'file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts' costs: 59.30 ms
19:04:03.345 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts.
19:04:03.345 debug: Inspect file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts
19:04:03.362 debug: transform url: 'file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts' costs: 17.40 ms
19:04:03.365 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/RoleObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/RoleObj.ts.
19:04:03.365 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts.
19:04:03.365 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts.
19:04:03.366 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts.
19:04:03.366 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts.
19:04:03.367 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts.
19:04:03.367 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts.
19:04:03.368 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/LoadingMaskCmpt.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/LoadingMaskCmpt.ts.
19:04:03.368 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts.
19:04:03.368 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts.
19:04:03.368 debug: Detected change: file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts. Last mtime: Mon Aug 04 2025 20:13:37 GMT+0800 (中国标准时间)@fb0cd3e7-4241-4db0-9e3f-6da38cffbef3, Current mtime: Fri Aug 08 2025 18:28:28 GMT+0800 (中国标准时间)@fb0cd3e7-4241-4db0-9e3f-6da38cffbef3
19:04:03.369 debug: Inspect file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts
19:04:03.401 debug: transform url: 'file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts' costs: 32.10 ms
19:04:03.402 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts.
19:04:03.403 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts.
19:04:03.404 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts.
19:04:03.404 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts.
19:04:03.405 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts.
19:04:03.406 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/WindWaitNotCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/WindWaitNotCtrl.ts.
19:04:03.406 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts.
19:04:03.406 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.407 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Thu Aug 07 2025 23:20:49 GMT+0800 (中国标准时间), Current mtime: Fri Aug 08 2025 19:03:49 GMT+0800 (中国标准时间)
19:04:03.407 debug: Inspect cce:/internal/code-quality/cr.mjs
19:04:03.420 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 12.60 ms
19:04:03.421 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
19:04:03.421 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
19:04:03.421 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
19:04:03.421 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
19:04:03.422 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
19:04:03.423 debug: Resolve ./builtin-pipeline-pass from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
19:04:03.423 debug: Resolve ./builtin-pipeline from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
19:04:03.424 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.424 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
19:04:03.424 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
19:04:03.424 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
19:04:03.424 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
19:04:03.425 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
19:04:03.425 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
19:04:03.425 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
19:04:03.425 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
19:04:03.425 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.425 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
19:04:03.425 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
19:04:03.425 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
19:04:03.426 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
19:04:03.426 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
19:04:03.426 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.426 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
19:04:03.426 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
19:04:03.426 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
19:04:03.426 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
19:04:03.427 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
19:04:03.427 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
19:04:03.427 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
19:04:03.427 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
19:04:03.427 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/App.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.427 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/App.ts as cce:/internal/x/cc.
19:04:03.428 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/App.ts as cce:/internal/x/cc.
19:04:03.428 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/App.ts as cce:/internal/x/cc.
19:04:03.428 debug: Resolve ./script/common/LocalConfig from file:///D:/Projects/cute-animals-client/client/assets/app/App.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/LocalConfig.ts.
19:04:03.429 debug: Resolve ../scene/ca from file:///D:/Projects/cute-animals-client/client/assets/app/App.ts as file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts.
19:04:03.429 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/LocalConfig.ts as cce:/internal/x/cc.
19:04:03.429 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts as cce:/internal/x/cc.
19:04:03.429 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts as cce:/internal/x/cc.
19:04:03.429 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts as cce:/internal/x/cc.
19:04:03.429 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.429 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as cce:/internal/x/cc.
19:04:03.429 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as cce:/internal/x/cc.
19:04:03.569 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as cce:/internal/x/cc.
19:04:03.569 debug: Resolve ./base/BasePnlCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts.
19:04:03.569 debug: Resolve ./base/BaseWindCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts.
19:04:03.569 debug: Resolve ./base/BaseNoticeCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts.
19:04:03.569 debug: Resolve ./base/BaseWdtCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWdtCtrl.ts.
19:04:03.569 debug: Resolve ./base/BaseModel from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseModel.ts.
19:04:03.569 debug: Resolve ./manage/WindCtrlMgr from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts.
19:04:03.569 debug: Resolve ./manage/ViewCtrlMgr from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts.
19:04:03.569 debug: Resolve ./manage/ModelMgr from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts.
19:04:03.569 debug: Resolve ./event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.569 debug: Resolve ./layer/ViewLayerCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts.
19:04:03.569 debug: Resolve ./layer/WindLayerCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts.
19:04:03.569 debug: Resolve ./layer/NoticeLayerCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts.
19:04:03.569 debug: Resolve ./manage/NoticeCtrlMgr from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts.
19:04:03.569 debug: Resolve ./component/ButtonEx from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts.
19:04:03.570 debug: Resolve ./component/ScrollViewEx from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts.
19:04:03.570 debug: Resolve ./component/LabelWaitDot from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelWaitDot.ts.
19:04:03.570 debug: Resolve ./component/LabelRollNumber from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelRollNumber.ts.
19:04:03.570 debug: Resolve ./component/LabelTimer from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelTimer.ts.
19:04:03.570 debug: Resolve ./component/MultiColor from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiColor.ts.
19:04:03.570 debug: Resolve ./component/MultiFrame from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiFrame.ts.
19:04:03.570 debug: Resolve ./component/LocaleLabel from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts.
19:04:03.570 debug: Resolve ./component/LocaleRichText from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts.
19:04:03.570 debug: Resolve ./component/LocaleSprite from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts.
19:04:03.570 debug: Resolve ./component/ScrollViewPlus from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts.
19:04:03.570 debug: Resolve ./component/LocaleFont from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts.
19:04:03.570 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.570 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts as cce:/internal/x/cc.
19:04:03.570 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts as cce:/internal/x/cc.
19:04:03.570 debug: Resolve ./BaseViewCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts.
19:04:03.570 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.570 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts as cce:/internal/x/cc.
19:04:03.570 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.570 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts as cce:/internal/x/cc.
19:04:03.570 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts as cce:/internal/x/cc.
19:04:03.570 debug: Resolve ./BaseMvcCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts.
19:04:03.570 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts as cce:/internal/x/cc.
19:04:03.570 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts as cce:/internal/x/cc.
19:04:03.570 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts as cce:/internal/x/cc.
19:04:03.570 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts as cce:/internal/x/cc.
19:04:03.570 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.570 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts as cce:/internal/x/cc.
19:04:03.570 debug: Resolve ./BaseViewCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts.
19:04:03.570 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.570 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts as cce:/internal/x/cc.
19:04:03.570 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts as cce:/internal/x/cc.
19:04:03.570 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts as cce:/internal/x/cc.
19:04:03.570 debug: Resolve ./BaseViewCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts.
19:04:03.570 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWdtCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.570 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWdtCtrl.ts as cce:/internal/x/cc.
19:04:03.570 debug: Resolve ./BaseViewCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWdtCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseModel.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve ../base/BaseWindCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts.
19:04:03.571 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.571 debug: Resolve ../utils/ResLoader from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve ../base/BasePnlCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts.
19:04:03.571 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.571 debug: Resolve ../utils/ResLoader from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts.
19:04:03.571 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve ../base/BaseLayerCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts.
19:04:03.571 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.571 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve ./BaseMvcCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts.
19:04:03.571 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts as cce:/internal/x/cc.
19:04:03.571 debug: Resolve ../base/BaseLayerCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts.
19:04:03.571 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.572 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve ../base/BaseLayerCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts.
19:04:03.572 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.572 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve ../base/BaseNoticeCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts.
19:04:03.572 debug: Resolve ../utils/ResLoader from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts.
19:04:03.572 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve cc/env from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts as external dependency cc/env.
19:04:03.572 debug: Resolve ../base/BasePnlCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts.
19:04:03.572 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelWaitDot.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelWaitDot.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelWaitDot.ts as cce:/internal/x/cc.
19:04:03.572 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelRollNumber.ts as cce:/internal/x/cc.
19:04:03.573 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelRollNumber.ts as cce:/internal/x/cc.
19:04:03.573 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelRollNumber.ts as cce:/internal/x/cc.
19:04:03.573 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelTimer.ts as cce:/internal/x/cc.
19:04:03.573 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelTimer.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelTimer.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiColor.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiColor.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiColor.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiFrame.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiFrame.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiFrame.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve ../base/BaseLocale from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts.
19:04:03.574 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve ../base/BaseLocale from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts.
19:04:03.574 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.574 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve ../base/BaseLocale from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts.
19:04:03.574 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve ../base/BaseLocale from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts.
19:04:03.574 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendAnimation.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendAnimation.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendAnimation.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendArray.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendButton.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendButton.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendButton.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendCC.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendCC.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendCC.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendComponent.ts as cce:/internal/x/cc.
19:04:03.574 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendComponent.ts as cce:/internal/x/cc.
19:04:03.575 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendComponent.ts as cce:/internal/x/cc.
19:04:03.575 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.575 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts as cce:/internal/x/cc.
19:04:03.575 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts as cce:/internal/x/cc.
19:04:03.575 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts as cce:/internal/x/cc.
19:04:03.575 debug: Resolve ../base/BaseLocale from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts.
19:04:03.575 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.575 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts as cce:/internal/x/cc.
19:04:03.575 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts as cce:/internal/x/cc.
19:04:03.575 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts as cce:/internal/x/cc.
19:04:03.575 debug: Resolve ../component/LocaleLabel from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts.
19:04:03.575 debug: Resolve ../component/LocaleRichText from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts.
19:04:03.575 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.575 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts as cce:/internal/x/cc.
19:04:03.575 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts as cce:/internal/x/cc.
19:04:03.575 debug: Resolve ../base/BaseLocale from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts.
19:04:03.575 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts as cce:/internal/x/cc.
19:04:03.575 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.575 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve ../component/ScrollViewEx from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts.
19:04:03.580 debug: Resolve ../component/ScrollViewPlus from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts.
19:04:03.580 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve ../component/LocaleSprite from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendToggleContainer.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendToggleContainer.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendToggleContainer.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendVec.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendVec.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendVec.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.580 debug: Resolve ../utils/ResLoader from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AudioMgr.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NodePoolMgr.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NodePoolMgr.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NodePoolMgr.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/StorageMgr.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/EventCenter.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/EventCenter.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/EventCenter.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Utils.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Utils.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Utils.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/proto/ProtoHelper.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/AnimalFrameAnimConf.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/AnimalFrameAnimConf.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/AnimalFrameAnimConf.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts as cce:/internal/x/cc.
19:04:03.580 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts as cce:/internal/x/cc.
19:04:03.581 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Constant.ts as cce:/internal/x/cc.
19:04:03.581 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/DataType.ts as cce:/internal/x/cc.
19:04:03.581 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/ECode.ts as cce:/internal/x/cc.
19:04:03.581 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts as cce:/internal/x/cc.
19:04:03.581 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/interface.ts as cce:/internal/x/cc.
19:04:03.581 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts as cce:/internal/x/cc.
19:04:03.581 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/JsbEvent.ts as cce:/internal/x/cc.
19:04:03.581 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts as cce:/internal/x/cc.
19:04:03.581 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NotEvent.ts as cce:/internal/x/cc.
19:04:03.581 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.581 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as cce:/internal/x/cc.
19:04:03.581 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as cce:/internal/x/cc.
19:04:03.581 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as cce:/internal/x/cc.
19:04:03.581 debug: Resolve ../../model/game/GameModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts.
19:04:03.581 debug: Resolve ../LocalConfig from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/LocalConfig.ts.
19:04:03.581 debug: Resolve db://assets/scene/ca from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts.
19:04:03.581 debug: Resolve ../../model/common/UserModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts.
19:04:03.581 debug: Resolve ../../model/common/NetworkModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts.
19:04:03.581 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.581 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts as cce:/internal/x/cc.
19:04:03.581 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:03.581 debug: Resolve ../../common/event/EventType from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts.
19:04:03.581 debug: Resolve ../battle/FSPModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts.
19:04:03.581 debug: Resolve ./AnimalObj from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts.
19:04:03.581 debug: Resolve ./MapNodeObj from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts.
19:04:03.581 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.581 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts as cce:/internal/x/cc.
19:04:03.582 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts as cce:/internal/x/cc.
19:04:03.582 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts as cce:/internal/x/cc.
19:04:03.582 debug: Resolve ./FSPController from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts.
19:04:03.582 debug: Resolve ../../common/constant/Constant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Constant.ts.
19:04:03.583 debug: Resolve ../game/GameModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts.
19:04:03.583 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.583 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts as cce:/internal/x/cc.
19:04:03.583 debug: Resolve ../common/RandomObj from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/RandomObj.ts.
19:04:03.583 debug: Resolve ../game/GameModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts.
19:04:03.583 debug: Resolve ./FSPFighter from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts.
19:04:03.583 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/RandomObj.ts as cce:/internal/x/cc.
19:04:03.583 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.583 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts as cce:/internal/x/cc.
19:04:03.583 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts as cce:/internal/x/cc.
19:04:03.583 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts as cce:/internal/x/cc.
19:04:03.583 debug: Resolve ./BehaviorTree from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts.
19:04:03.583 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:03.583 debug: Resolve ../../common/event/EventType from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts.
19:04:03.583 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.583 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts as cce:/internal/x/cc.
19:04:03.583 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts as cce:/internal/x/cc.
19:04:03.583 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts as cce:/internal/x/cc.
19:04:03.588 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:03.588 debug: Resolve ./_BevTreeFactory from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts.
19:04:03.588 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts as cce:/internal/x/cc.
19:04:03.588 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.588 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as cce:/internal/x/cc.
19:04:03.588 debug: Resolve ./BTAttack from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts.
19:04:03.588 debug: Resolve ./BTRoundBegin from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts.
19:04:03.588 debug: Resolve ./BTRoundEnd from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts.
19:04:03.588 debug: Resolve ./_Parallel from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts.
19:04:03.588 debug: Resolve ./_Priority from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts.
19:04:03.588 debug: Resolve ./_Sequence from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts.
19:04:03.588 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.588 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts as cce:/internal/x/cc.
19:04:03.588 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:03.588 debug: Resolve ./_BaseAction from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts.
19:04:03.588 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:03.588 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.588 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts as cce:/internal/x/cc.
19:04:03.588 debug: Resolve ./_BaseBTNode from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts.
19:04:03.588 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:03.588 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.588 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts as cce:/internal/x/cc.
19:04:03.588 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:03.588 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.588 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve ./_BaseAction from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts.
19:04:03.589 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:03.589 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve ./_BaseAction from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts.
19:04:03.589 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:03.589 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve ./_BaseComposite from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts.
19:04:03.589 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:03.589 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve ./_BaseBTNode from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts.
19:04:03.589 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:03.589 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve ./_BaseComposite from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts.
19:04:03.589 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:03.589 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve ./_BaseComposite from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts.
19:04:03.589 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:03.589 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:03.589 debug: Resolve ./AnimalStateObj from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalStateObj.ts.
19:04:03.589 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalStateObj.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalStateObj.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:03.589 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:03.589 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve ../../common/event/EventType from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts.
19:04:03.589 debug: Resolve ../../common/event/NetEvent from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts.
19:04:03.589 debug: Resolve ../../common/helper/ViewHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts.
19:04:03.589 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve ../event/NetEvent from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts.
19:04:03.589 debug: Resolve ../event/NotEvent from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NotEvent.ts.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/LoadProgressHelper.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseCondition.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.589 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseCondition.ts as cce:/internal/x/cc.
19:04:03.589 debug: Resolve ./_BaseBTNode from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseCondition.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts.
19:04:03.589 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseCondition.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:03.589 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseDecorator.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseDecorator.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve ./_BaseBTNode from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseDecorator.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts.
19:04:03.591 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseDecorator.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/BuffObj.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/EncounterObj.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/FoodObj.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/RoleObj.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve db://assets/scene/ca from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts.
19:04:03.591 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:03.591 debug: Resolve ../../common/event/NetEvent from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts.
19:04:03.591 debug: Resolve ../../common/helper/GameHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts.
19:04:03.591 debug: Resolve ../../common/constant/ECode from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/ECode.ts.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve ../../common/helper/GameHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts.
19:04:03.591 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve ../cmpt/FrameAnimationCmpt from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts.
19:04:03.591 debug: Resolve ../../common/config/AnimalFrameAnimConf from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/AnimalFrameAnimConf.ts.
19:04:03.591 debug: Resolve ../../model/game/GameModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts.
19:04:03.591 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:03.591 debug: Resolve ../../common/event/EventType from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts.
19:04:03.591 debug: Resolve ./AttrBarCmpt from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts.
19:04:03.591 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as cce:/internal/x/cc.
19:04:03.591 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve ./AnimalCmpt from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts.
19:04:03.592 debug: Resolve ../../common/event/EventType from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts.
19:04:03.592 debug: Resolve ./AnimPlayCmpt from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts.
19:04:03.592 debug: Resolve ./LoadingMaskCmpt from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/LoadingMaskCmpt.ts.
19:04:03.592 debug: Resolve ./RoleCmpt from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts.
19:04:03.592 debug: Resolve ../../model/game/RoleObj from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/RoleObj.ts.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/LoadingMaskCmpt.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/LoadingMaskCmpt.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/LoadingMaskCmpt.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve ../cmpt/FrameAnimationCmpt from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts.
19:04:03.592 debug: Resolve ../../model/game/GameModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts.
19:04:03.592 debug: Resolve ../../common/config/RoleFrameAnimConf from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts.
19:04:03.592 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve ../../common/helper/GameHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts.
19:04:03.592 debug: Resolve ../../common/helper/ViewHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts.
19:04:03.592 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve ../../common/helper/ViewHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts.
19:04:03.592 debug: Resolve db://assets/scene/ca from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts.
19:04:03.592 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:03.592 debug: Resolve ../../common/constant/ECode from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/ECode.ts.
19:04:03.592 debug: Resolve ../../common/helper/LoadProgressHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/LoadProgressHelper.ts.
19:04:03.592 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve ../../model/game/GameModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts.
19:04:03.592 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts as cce:/internal/x/cc.
19:04:03.592 debug: Resolve ../../common/event/NotEvent from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NotEvent.ts.
19:04:03.592 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.593 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:03.593 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:03.593 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:03.593 debug: Resolve ../../common/event/NetEvent from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts.
19:04:03.593 debug: Resolve ../../common/helper/ViewHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts.
19:04:03.593 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:03.593 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:03.593 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:03.593 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:03.593 debug: Resolve ../../common/helper/ViewHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts.
19:04:03.593 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/WindWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:03.593 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/WindWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:03.593 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/WindWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:03.593 debug: Target(editor) ends with cost 490.15439999999944ms.
19:04:03.593 debug: Target(preview) build started.
19:04:03.594 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:14 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:14 GMT+0800 (中国标准时间)
19:04:03.594 debug: Inspect cce:/internal/x/cc
19:04:03.621 debug: transform url: 'cce:/internal/x/cc' costs: 26.50 ms
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
19:04:03.622 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
19:04:03.623 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
19:04:03.623 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
19:04:03.623 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
19:04:03.623 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
19:04:03.623 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
19:04:03.623 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
19:04:03.623 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:22 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:14 GMT+0800 (中国标准时间)
19:04:03.623 debug: Inspect cce:/internal/x/prerequisite-imports
19:04:03.647 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 23.70 ms
19:04:03.648 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
19:04:03.649 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
19:04:03.649 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
19:04:03.650 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
19:04:03.651 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
19:04:03.651 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
19:04:03.652 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/App.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/App.ts.
19:04:03.652 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts.
19:04:03.653 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts.
19:04:03.653 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts.
19:04:03.654 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseModel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseModel.ts.
19:04:03.654 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts.
19:04:03.655 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts.
19:04:03.655 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts.
19:04:03.656 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts.
19:04:03.656 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWdtCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWdtCtrl.ts.
19:04:03.657 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts.
19:04:03.657 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts.
19:04:03.658 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelRollNumber.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelRollNumber.ts.
19:04:03.658 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelTimer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelTimer.ts.
19:04:03.659 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelWaitDot.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelWaitDot.ts.
19:04:03.659 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts.
19:04:03.660 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts.
19:04:03.660 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts.
19:04:03.661 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts.
19:04:03.661 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiColor.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiColor.ts.
19:04:03.662 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiFrame.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiFrame.ts.
19:04:03.663 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts.
19:04:03.663 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts.
19:04:03.663 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:03.664 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendAnimation.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendAnimation.ts.
19:04:03.664 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendArray.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendArray.ts.
19:04:03.665 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendButton.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendButton.ts.
19:04:03.665 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendCC.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendCC.ts.
19:04:03.666 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendComponent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendComponent.ts.
19:04:03.666 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts.
19:04:03.667 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts.
19:04:03.667 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts.
19:04:03.668 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts.
19:04:03.668 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts.
19:04:03.669 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendToggleContainer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendToggleContainer.ts.
19:04:03.669 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendVec.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendVec.ts.
19:04:03.670 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts.
19:04:03.670 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts.
19:04:03.670 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts.
19:04:03.671 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts.
19:04:03.671 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AudioMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AudioMgr.ts.
19:04:03.672 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts.
19:04:03.672 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NodePoolMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NodePoolMgr.ts.
19:04:03.672 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts.
19:04:03.673 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/StorageMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/StorageMgr.ts.
19:04:03.673 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts.
19:04:03.674 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts.
19:04:03.674 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/EventCenter.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/EventCenter.ts.
19:04:03.674 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts.
19:04:03.675 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts.
19:04:03.675 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Utils.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Utils.ts.
19:04:03.675 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/proto/ProtoHelper.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/proto/ProtoHelper.ts.
19:04:03.676 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/LocalConfig.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/LocalConfig.ts.
19:04:03.676 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/AnimalFrameAnimConf.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/AnimalFrameAnimConf.ts.
19:04:03.677 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts.
19:04:03.677 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Constant.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Constant.ts.
19:04:03.677 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/DataType.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/DataType.ts.
19:04:03.678 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/ECode.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/ECode.ts.
19:04:03.678 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:03.678 debug: Detected change: file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts. Last mtime: Thu Jul 31 2025 17:53:45 GMT+0800 (中国标准时间)@39e2e7e8-12e8-4bd3-b593-a301ca5b3dc7, Current mtime: Fri Aug 08 2025 18:34:31 GMT+0800 (中国标准时间)@39e2e7e8-12e8-4bd3-b593-a301ca5b3dc7
19:04:03.678 debug: Inspect file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts
19:04:03.704 debug: transform url: 'file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts' costs: 25.70 ms
19:04:03.705 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/interface.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/interface.ts.
19:04:03.706 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts.
19:04:03.706 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/JsbEvent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/JsbEvent.ts.
19:04:03.707 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts.
19:04:03.707 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NotEvent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NotEvent.ts.
19:04:03.708 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts.
19:04:03.708 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/LoadProgressHelper.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/LoadProgressHelper.ts.
19:04:03.708 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts.
19:04:03.709 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts.
19:04:03.709 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts.
19:04:03.710 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts.
19:04:03.710 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts.
19:04:03.710 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts.
19:04:03.711 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts.
19:04:03.711 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts.
19:04:03.712 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:03.712 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts.
19:04:03.712 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts.
19:04:03.713 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts.
19:04:03.714 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseCondition.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseCondition.ts.
19:04:03.714 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseDecorator.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseDecorator.ts.
19:04:03.714 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts.
19:04:03.715 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts.
19:04:03.715 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts.
19:04:03.716 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts.
19:04:03.716 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/BuffObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/BuffObj.ts.
19:04:03.716 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts.
19:04:03.717 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/RandomObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/RandomObj.ts.
19:04:03.717 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts.
19:04:03.718 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts.
19:04:03.718 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalStateObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalStateObj.ts.
19:04:03.719 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/EncounterObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/EncounterObj.ts.
19:04:03.719 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/FoodObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/FoodObj.ts.
19:04:03.719 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts.
19:04:03.720 debug: Detected change: file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts. Last mtime: Mon Aug 04 2025 20:13:32 GMT+0800 (中国标准时间)@ce653eb4-22b7-427a-8bfe-bf9a030a7d28, Current mtime: Fri Aug 08 2025 18:38:08 GMT+0800 (中国标准时间)@ce653eb4-22b7-427a-8bfe-bf9a030a7d28
19:04:04.017 debug: Inspect file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts
19:04:04.017 debug: transform url: 'file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts' costs: 48.30 ms
19:04:04.017 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts.
19:04:04.017 debug: Inspect file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts
19:04:04.017 debug: transform url: 'file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts' costs: 19.80 ms
19:04:04.017 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/RoleObj.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/RoleObj.ts.
19:04:04.017 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts.
19:04:04.017 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts.
19:04:04.017 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts.
19:04:04.017 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts.
19:04:04.017 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts.
19:04:04.017 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts.
19:04:04.017 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/LoadingMaskCmpt.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/LoadingMaskCmpt.ts.
19:04:04.017 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts.
19:04:04.017 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts.
19:04:04.017 debug: Detected change: file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts. Last mtime: Mon Aug 04 2025 20:13:37 GMT+0800 (中国标准时间)@fb0cd3e7-4241-4db0-9e3f-6da38cffbef3, Current mtime: Fri Aug 08 2025 18:28:28 GMT+0800 (中国标准时间)@fb0cd3e7-4241-4db0-9e3f-6da38cffbef3
19:04:04.017 debug: Inspect file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts
19:04:04.017 debug: transform url: 'file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts' costs: 49.70 ms
19:04:04.017 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts.
19:04:04.017 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts.
19:04:04.018 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts.
19:04:04.018 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts.
19:04:04.018 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts.
19:04:04.018 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/WindWaitNotCtrl.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/WindWaitNotCtrl.ts.
19:04:04.018 debug: Resolve file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts from cce:/internal/x/prerequisite-imports as file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts.
19:04:04.018 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.018 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Thu Aug 07 2025 23:20:49 GMT+0800 (中国标准时间), Current mtime: Fri Aug 08 2025 19:03:49 GMT+0800 (中国标准时间)
19:04:04.018 debug: Inspect cce:/internal/code-quality/cr.mjs
19:04:04.018 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 38.30 ms
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
19:04:04.018 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
19:04:04.018 debug: Resolve ./builtin-pipeline-pass from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
19:04:04.018 debug: Resolve ./builtin-pipeline from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
19:04:04.018 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
19:04:04.018 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
19:04:04.018 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
19:04:04.018 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
19:04:04.018 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/App.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.018 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/App.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/App.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/App.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve ./script/common/LocalConfig from file:///D:/Projects/cute-animals-client/client/assets/app/App.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/LocalConfig.ts.
19:04:04.018 debug: Resolve ../scene/ca from file:///D:/Projects/cute-animals-client/client/assets/app/App.ts as file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts.
19:04:04.018 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/LocalConfig.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.018 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as cce:/internal/x/cc.
19:04:04.018 debug: Resolve ./base/BasePnlCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts.
19:04:04.018 debug: Resolve ./base/BaseWindCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts.
19:04:04.018 debug: Resolve ./base/BaseNoticeCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts.
19:04:04.018 debug: Resolve ./base/BaseWdtCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWdtCtrl.ts.
19:04:04.021 debug: Resolve ./base/BaseModel from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseModel.ts.
19:04:04.021 debug: Resolve ./manage/WindCtrlMgr from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts.
19:04:04.021 debug: Resolve ./manage/ViewCtrlMgr from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts.
19:04:04.021 debug: Resolve ./manage/ModelMgr from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts.
19:04:04.021 debug: Resolve ./event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:04.021 debug: Resolve ./layer/ViewLayerCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts.
19:04:04.021 debug: Resolve ./layer/WindLayerCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts.
19:04:04.021 debug: Resolve ./layer/NoticeLayerCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts.
19:04:04.021 debug: Resolve ./manage/NoticeCtrlMgr from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts.
19:04:04.021 debug: Resolve ./component/ButtonEx from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts.
19:04:04.021 debug: Resolve ./component/ScrollViewEx from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts.
19:04:04.021 debug: Resolve ./component/LabelWaitDot from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelWaitDot.ts.
19:04:04.021 debug: Resolve ./component/LabelRollNumber from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelRollNumber.ts.
19:04:04.021 debug: Resolve ./component/LabelTimer from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelTimer.ts.
19:04:04.021 debug: Resolve ./component/MultiColor from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiColor.ts.
19:04:04.021 debug: Resolve ./component/MultiFrame from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiFrame.ts.
19:04:04.021 debug: Resolve ./component/LocaleLabel from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts.
19:04:04.021 debug: Resolve ./component/LocaleRichText from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts.
19:04:04.021 debug: Resolve ./component/LocaleSprite from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts.
19:04:04.021 debug: Resolve ./component/ScrollViewPlus from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts.
19:04:04.021 debug: Resolve ./component/LocaleFont from file:///D:/Projects/cute-animals-client/client/assets/app/core/CCMvc.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts.
19:04:04.021 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.021 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts as cce:/internal/x/cc.
19:04:04.021 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts as cce:/internal/x/cc.
19:04:04.021 debug: Resolve ./BaseViewCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts.
19:04:04.021 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:04.021 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts as cce:/internal/x/cc.
19:04:04.021 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve ./BaseMvcCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve ./BaseViewCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts.
19:04:04.022 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve ./BaseViewCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts.
19:04:04.022 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWdtCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWdtCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve ./BaseViewCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWdtCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseViewCtrl.ts.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseModel.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve ../base/BaseWindCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseWindCtrl.ts.
19:04:04.022 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:04.022 debug: Resolve ../utils/ResLoader from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/WindCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve ../base/BasePnlCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts.
19:04:04.022 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:04.022 debug: Resolve ../utils/ResLoader from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ViewCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts.
19:04:04.022 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/ModelMgr.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve ../base/BaseLayerCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts.
19:04:04.022 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/ViewLayerCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:04.022 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve ./BaseMvcCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseMvcCtrl.ts.
19:04:04.022 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve ../base/BaseLayerCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts.
19:04:04.022 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/WindLayerCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:04.022 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts as cce:/internal/x/cc.
19:04:04.022 debug: Resolve ../base/BaseLayerCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLayerCtrl.ts.
19:04:04.022 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/layer/NoticeLayerCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:04.024 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve ../base/BaseNoticeCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseNoticeCtrl.ts.
19:04:04.024 debug: Resolve ../utils/ResLoader from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NoticeCtrlMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts.
19:04:04.024 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc/env from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts as external dependency cc/env.
19:04:04.024 debug: Resolve ../base/BasePnlCtrl from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BasePnlCtrl.ts.
19:04:04.024 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ButtonEx.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelWaitDot.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelWaitDot.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelWaitDot.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelRollNumber.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelRollNumber.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelRollNumber.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelTimer.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelTimer.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LabelTimer.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiColor.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiColor.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiColor.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiFrame.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiFrame.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/MultiFrame.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve ../base/BaseLocale from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts.
19:04:04.024 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve ../base/BaseLocale from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts.
19:04:04.024 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:04.024 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve ../base/BaseLocale from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts.
19:04:04.024 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve ../base/BaseLocale from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts.
19:04:04.024 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleFont.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendAnimation.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendAnimation.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendAnimation.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendArray.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendButton.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendButton.ts as cce:/internal/x/cc.
19:04:04.024 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendButton.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendCC.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendCC.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendCC.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendComponent.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendComponent.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendComponent.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve ../base/BaseLocale from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendEditBox.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts.
19:04:04.025 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve ../component/LocaleLabel from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleLabel.ts.
19:04:04.025 debug: Resolve ../component/LocaleRichText from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendLabel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleRichText.ts.
19:04:04.025 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve ../base/BaseLocale from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/base/BaseLocale.ts.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendNode.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts as cce:/internal/x/cc.
19:04:04.025 debug: Resolve ../component/ScrollViewEx from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewEx.ts.
19:04:04.025 debug: Resolve ../component/ScrollViewPlus from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendScrollView.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/ScrollViewPlus.ts.
19:04:04.025 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.025 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve ../component/LocaleSprite from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendSprite.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/component/LocaleSprite.ts.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendToggleContainer.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendToggleContainer.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendToggleContainer.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendVec.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendVec.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/extend/ExtendVec.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:04.028 debug: Resolve ../utils/ResLoader from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AssetsMgr.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/ResLoader.ts.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/AudioMgr.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NodePoolMgr.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NodePoolMgr.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/NodePoolMgr.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/manage/StorageMgr.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/EventCenter.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/EventCenter.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/EventCenter.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve ../event/CoreEventType from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Logger.ts as file:///D:/Projects/cute-animals-client/client/assets/app/core/event/CoreEventType.ts.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Utils.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Utils.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/core/utils/Utils.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/proto/ProtoHelper.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/AnimalFrameAnimConf.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/AnimalFrameAnimConf.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/AnimalFrameAnimConf.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Constant.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/DataType.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/ECode.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/interface.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/JsbEvent.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NotEvent.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as cce:/internal/x/cc.
19:04:04.028 debug: Resolve ../../model/game/GameModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts.
19:04:04.029 debug: Resolve ../LocalConfig from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/LocalConfig.ts.
19:04:04.029 debug: Resolve db://assets/scene/ca from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts.
19:04:04.029 debug: Resolve ../../model/common/UserModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts.
19:04:04.029 debug: Resolve ../../model/common/NetworkModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts.
19:04:04.029 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.029 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts as cce:/internal/x/cc.
19:04:04.029 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:04.029 debug: Resolve ../../common/event/EventType from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts.
19:04:04.029 debug: Resolve ../battle/FSPModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts.
19:04:04.029 debug: Resolve ./AnimalObj from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts.
19:04:04.029 debug: Resolve ./MapNodeObj from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts.
19:04:04.029 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.029 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts as cce:/internal/x/cc.
19:04:04.029 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts as cce:/internal/x/cc.
19:04:04.029 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts as cce:/internal/x/cc.
19:04:04.029 debug: Resolve ./FSPController from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts.
19:04:04.029 debug: Resolve ../../common/constant/Constant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Constant.ts.
19:04:04.029 debug: Resolve ../game/GameModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts.
19:04:04.029 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.029 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts as cce:/internal/x/cc.
19:04:04.029 debug: Resolve ../common/RandomObj from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/RandomObj.ts.
19:04:04.029 debug: Resolve ../game/GameModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts.
19:04:04.029 debug: Resolve ./FSPFighter from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPController.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts.
19:04:04.029 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/RandomObj.ts as cce:/internal/x/cc.
19:04:04.029 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.029 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts as cce:/internal/x/cc.
19:04:04.029 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts as cce:/internal/x/cc.
19:04:04.029 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts as cce:/internal/x/cc.
19:04:04.029 debug: Resolve ./BehaviorTree from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts.
19:04:04.029 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:04.029 debug: Resolve ../../common/event/EventType from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/FSPFighter.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts.
19:04:04.029 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.029 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts as cce:/internal/x/cc.
19:04:04.029 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts as cce:/internal/x/cc.
19:04:04.029 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts as cce:/internal/x/cc.
19:04:04.029 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:04.029 debug: Resolve ./_BevTreeFactory from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BehaviorTree.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts.
19:04:04.029 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts as cce:/internal/x/cc.
19:04:04.029 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.029 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as cce:/internal/x/cc.
19:04:04.031 debug: Resolve ./BTAttack from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts.
19:04:04.031 debug: Resolve ./BTRoundBegin from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts.
19:04:04.031 debug: Resolve ./BTRoundEnd from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts.
19:04:04.031 debug: Resolve ./_Parallel from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts.
19:04:04.031 debug: Resolve ./_Priority from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts.
19:04:04.031 debug: Resolve ./_Sequence from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BevTreeFactory.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts.
19:04:04.031 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.031 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts as cce:/internal/x/cc.
19:04:04.031 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:04.031 debug: Resolve ./_BaseAction from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts.
19:04:04.031 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTAttack.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:04.031 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.031 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts as cce:/internal/x/cc.
19:04:04.031 debug: Resolve ./_BaseBTNode from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts.
19:04:04.031 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:04.031 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.031 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts as cce:/internal/x/cc.
19:04:04.031 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:04.031 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.031 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts as cce:/internal/x/cc.
19:04:04.031 debug: Resolve ./_BaseAction from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts.
19:04:04.032 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundBegin.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:04.032 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve ./_BaseAction from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseAction.ts.
19:04:04.032 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/BTRoundEnd.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:04.032 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve ./_BaseComposite from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts.
19:04:04.032 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Parallel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:04.032 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve ./_BaseBTNode from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts.
19:04:04.032 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:04.032 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve ./_BaseComposite from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts.
19:04:04.032 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Priority.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:04.032 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve ./_BaseComposite from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseComposite.ts.
19:04:04.032 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_Sequence.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:04.032 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:04.032 debug: Resolve ./AnimalStateObj from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalObj.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalStateObj.ts.
19:04:04.032 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalStateObj.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/AnimalStateObj.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/MapNodeObj.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:04.032 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/UserModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:04.032 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve ../../common/event/EventType from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts.
19:04:04.032 debug: Resolve ../../common/event/NetEvent from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts.
19:04:04.032 debug: Resolve ../../common/helper/ViewHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/NetworkModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts.
19:04:04.032 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve ../event/NetEvent from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts.
19:04:04.032 debug: Resolve ../event/NotEvent from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NotEvent.ts.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/LoadProgressHelper.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseCondition.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.032 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseCondition.ts as cce:/internal/x/cc.
19:04:04.032 debug: Resolve ./_BaseBTNode from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseCondition.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts.
19:04:04.032 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseCondition.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:04.032 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseDecorator.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.033 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseDecorator.ts as cce:/internal/x/cc.
19:04:04.033 debug: Resolve ./_BaseBTNode from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseDecorator.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseBTNode.ts.
19:04:04.033 debug: Resolve ./_BTConstant from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BaseDecorator.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/battle/_BTConstant.ts.
19:04:04.033 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/common/BuffObj.ts as cce:/internal/x/cc.
19:04:04.033 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/EncounterObj.ts as cce:/internal/x/cc.
19:04:04.034 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/FoodObj.ts as cce:/internal/x/cc.
19:04:04.034 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/RoleObj.ts as cce:/internal/x/cc.
19:04:04.034 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.034 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as cce:/internal/x/cc.
19:04:04.034 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as cce:/internal/x/cc.
19:04:04.034 debug: Resolve db://assets/scene/ca from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts.
19:04:04.034 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:04.034 debug: Resolve ../../common/event/NetEvent from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts.
19:04:04.034 debug: Resolve ../../common/helper/GameHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts.
19:04:04.034 debug: Resolve ../../common/constant/ECode from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/ECode.ts.
19:04:04.034 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve ../../common/helper/GameHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts.
19:04:04.035 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve ../cmpt/FrameAnimationCmpt from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts.
19:04:04.035 debug: Resolve ../../common/config/AnimalFrameAnimConf from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/AnimalFrameAnimConf.ts.
19:04:04.035 debug: Resolve ../../model/game/GameModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts.
19:04:04.035 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:04.035 debug: Resolve ../../common/event/EventType from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts.
19:04:04.035 debug: Resolve ./AttrBarCmpt from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts.
19:04:04.035 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AttrBarCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve ./AnimalCmpt from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimalCmpt.ts.
19:04:04.035 debug: Resolve ../../common/event/EventType from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/EventType.ts.
19:04:04.035 debug: Resolve ./AnimPlayCmpt from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/AnimPlayCmpt.ts.
19:04:04.035 debug: Resolve ./LoadingMaskCmpt from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/LoadingMaskCmpt.ts.
19:04:04.035 debug: Resolve ./RoleCmpt from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts.
19:04:04.035 debug: Resolve ../../model/game/RoleObj from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/GameWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/RoleObj.ts.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/LoadingMaskCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/LoadingMaskCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/LoadingMaskCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve ../cmpt/FrameAnimationCmpt from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/view/cmpt/FrameAnimationCmpt.ts.
19:04:04.035 debug: Resolve ../../model/game/GameModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts.
19:04:04.035 debug: Resolve ../../common/config/RoleFrameAnimConf from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/RoleCmpt.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/RoleFrameAnimConf.ts.
19:04:04.035 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve ../../common/helper/GameHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/GameHelper.ts.
19:04:04.035 debug: Resolve ../../common/helper/ViewHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts.
19:04:04.035 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as cce:/internal/x/cc.
19:04:04.035 debug: Resolve ../../common/helper/ViewHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts.
19:04:04.035 debug: Resolve db://assets/scene/ca from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/scene/ca.ts.
19:04:04.035 debug: Resolve ../../common/constant/Enums from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/Enums.ts.
19:04:04.035 debug: Resolve ../../common/constant/ECode from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/constant/ECode.ts.
19:04:04.036 debug: Resolve ../../common/helper/LoadProgressHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/login/LoginWindCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/LoadProgressHelper.ts.
19:04:04.036 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.036 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts as cce:/internal/x/cc.
19:04:04.036 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts as cce:/internal/x/cc.
19:04:04.036 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts as cce:/internal/x/cc.
19:04:04.036 debug: Resolve ../../model/game/GameModel from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/EventNotCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/model/game/GameModel.ts.
19:04:04.036 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.036 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts as cce:/internal/x/cc.
19:04:04.036 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts as cce:/internal/x/cc.
19:04:04.036 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts as cce:/internal/x/cc.
19:04:04.036 debug: Resolve ../../common/event/NotEvent from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/MessageBoxNotCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NotEvent.ts.
19:04:04.036 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.036 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:04.036 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:04.036 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:04.036 debug: Resolve ../../common/event/NetEvent from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/event/NetEvent.ts.
19:04:04.036 debug: Resolve ../../common/helper/ViewHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/NetWaitNotCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts.
19:04:04.036 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts as cce:/internal/code-quality/cr.mjs.
19:04:04.037 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:04.037 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:04.037 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:04.037 debug: Resolve ../../common/helper/ViewHelper from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/PnlWaitNotCtrl.ts as file:///D:/Projects/cute-animals-client/client/assets/app/script/common/helper/ViewHelper.ts.
19:04:04.037 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/WindWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:04.037 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/WindWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:04.037 debug: Resolve cc from file:///D:/Projects/cute-animals-client/client/assets/app/script/view/notice/WindWaitNotCtrl.ts as cce:/internal/x/cc.
19:04:04.037 debug: Target(preview) ends with cost 440.0568000000003ms.
