import { AnimalCamp } from "../../common/constant/Enums"
import { IFrameAnimation } from "../../common/constant/interface"
import EventType from "../../common/event/EventType"
import FSPModel from "../battle/FSPModel"
import AnimalObj from "./AnimalObj"
import FoodObj from "./FoodObj"
import MapNodeObj from "./MapNodeObj"

/**
 * 游戏模块
 */
@mc.addmodel('game')
export default class GameModel extends mc.BaseModel {

    private runing: boolean = false //是否运行中

    private maps: MapNodeObj[][] = [] //地图数据
    private mapPaths: number[] = [] //已经走的路径

    private day: number = 0 // 天数
    private hp: number[] = [] //血量
    private winCount: number = 0 //胜利次数

    private battleAnimals: AnimalObj[] = [] //当前战斗区域的动物列表
    private prepareAnimals: AnimalObj[] = [] //当前备战区域的动物列表
    private foods: FoodObj[] = [] //当前的食物列表

    private enemyAnimals: AnimalObj[] = [] //敌方战斗区域的动物列表

    private frameAnimations: IFrameAnimation[] = [] //动画组件 在这里统一每帧调用
    private fspModel: FSPModel = null

    public onCreate() {
    }

    // 初始化基础数据
    public initBaseData(data: any) {
        console.log('initBaseData', data)
        this.day = data.day
        this.hp = data.hp || [100, 100]
        this.winCount = data.winCount
    }

    public init(data: any) {
        console.log('init', data)
        this.maps = data.mapData.maps.map(m => m.nodes.map(n => new MapNodeObj().init(n)))
        const gameData = data.gameData, player = gameData.player
        // Shop: this.Shop.ToPb(),
        // OtherPlayer: otherPlayer,
        // MapPaths: array.Clone(this.MapPaths),
    }

    public enter() {
        this.runing = true
    }

    public leave() {
        this.runing = false
        this.frameAnimations = []
    }

    public isRuning() { return this.runing }
    public getMaps() { return this.maps }
    public getDay() { return this.day }
    public getBattleAnimals() { return this.battleAnimals }
    public getPrepareAnimals() { return this.prepareAnimals }
    public getFoods() { return this.foods }

    public addAnimal(id: number) {
        if (this.battleAnimals.length < 3) {
            const animal = new AnimalObj().init(id, AnimalCamp.FRIENDLY, this.battleAnimals.length)
            this.battleAnimals.push(animal)
            this.emit(EventType.UPDATE_MY_BATTLE_AREA)
        }
    }

    // 获取敌方动物
    public getEnemyAnimals() {
        if (this.enemyAnimals.length === 0) {
            this.enemyAnimals = [
                new AnimalObj().init(11001, AnimalCamp.ENEMY, 0),
                new AnimalObj().init(11001, AnimalCamp.ENEMY, 1),
                new AnimalObj().init(11001, AnimalCamp.ENEMY, 2),
            ]
        }
        return this.enemyAnimals
    }

    public addFrameAnimation(cmpt: IFrameAnimation) {
        if (!this.frameAnimations.has('uuid', cmpt.uuid)) {
            this.frameAnimations.push(cmpt)
        }
    }

    public removeFrameAnimation(uuid: string) {
        this.frameAnimations.remove('uuid', uuid)
    }

    public update(dt: number) {
        if (!this.runing) {
            return
        } else if (this.fspModel) {
            this.fspModel.update(dt)
        } else {
            this.updateAnimationFrame(dt * 1000)
        }
    }

    // 刷新动画帧 毫秒
    public updateAnimationFrame(dt: number) {
        for (let i = this.frameAnimations.length - 1; i >= 0; i--) {
            const cmpt = this.frameAnimations[i]
            if (cmpt.isValid) {
                cmpt.updateFrame(dt)
            } else {
                this.frameAnimations.splice(i, 1)
            }
        }
    }

    //////////////////////////////////////////////////////////[ 战斗相关 ]///////////////////////////////////////////////////////////////////

    public getFspModel() { return this.fspModel }

    // 战斗开始
    public battleBegin() {
        const fighters = []
        fighters.pushArr(this.battleAnimals.map(m => m.toFighter()))
        fighters.pushArr(this.enemyAnimals.map(m => m.toFighter()))
        this.fspModel = new FSPModel().init({
            randSeed: ut.random(10000, 99999),
            fighters: fighters,
        })
    }

    // 战斗结束 本地
    public battleEndByLocal() {
        this.fspModel.stop()
        this.fspModel = null
    }

    // 根据阵营删除动物 来至战斗
    public removeAnimalByBattle(camp: number, uid: string) {
        if (camp === 1) {
            this.battleAnimals.remove('uid', uid)
        } else {
            this.enemyAnimals.remove('uid', uid)
        }
    }

}