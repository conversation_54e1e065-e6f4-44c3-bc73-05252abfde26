import { _decorator, log, EventTouch } from "cc";
const { ccclass } = _decorator;

@ccclass
export default class TestPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    //@end

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        log('TestPnlCtrl onCreate')
    }

    public onEnter(data: any) {
        log('TestPnlCtrl onEnter')
    }

    public onRemove() {
        log('TestPnlCtrl onRemove')
    }

    public onClean() {
        log('TestPnlCtrl onClean')
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://button_be
    onClickButton(event: EventTouch, data: string) {
        log('onClickButton', data)
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

}
