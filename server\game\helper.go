package game

import (
	"casrv/server/common/pb"
	ut "casrv/utils"
	"casrv/utils/array"
	"slices"
)

// 拷贝属性
func CloneAttrs(attrs [][]int32) [][]int32 {
	ret := [][]int32{}
	for i, l := 0, len(attrs); i < l; i++ {
		ret = append(ret, array.Clone(attrs[i]))
	}
	return ret
}

// 拷贝属性到pb
func CloneAttrsToPb(attrs [][]int32) []*pb.Int32ArrayInfo {
	arr := []*pb.Int32ArrayInfo{}
	for _, attr := range attrs {
		attrInfo := &pb.Int32ArrayInfo{}
		for _, val := range attr {
			attrInfo.Arr = append(attrInfo.Arr, val)
		}
		arr = append(arr, attrInfo)
	}
	return arr
}

// 随机3个物品
func RandomItemsTo3(conf []string) []*Item {
	return array.Map(conf, func(m string, _ int) *Item {
		arr := ut.StringToInt32s(m, ",")
		return NewItem(arr[0], int8(arr[1]))
	})
}

// 生成指定层数的地图数据
func GenerateMapData(layers int32) [][]*MapNode {
	maps := [][]*MapNode{}
	// 生成每一层
	for layer := range layers {
		currentLayer := []*MapNode{}
		if layer == 0 { //第一层固定3个节点
			for range 3 {
				node := &MapNode{
					Type:     getRandomNodeType(layer),
					Children: []int32{},
				}
				currentLayer = append(currentLayer, node)
			}
		} else {
			// 其他层根据上一层生成
			prevLayer := maps[layer-1]
			nodeCount := ut.RandomInt32(2, 5) // 每层2-5个节点
			for range nodeCount {
				node := &MapNode{
					Type:     getRandomNodeType(layer),
					Children: []int32{},
				}
				currentLayer = append(currentLayer, node)
			}
			// 为上一层的节点分配子节点（避免路径交叉）
			assignChildrenByStrictRegions(prevLayer, currentLayer)
		}
		maps = append(maps, currentLayer)
	}
	return maps
}

// 根据层数获取随机节点类型
func getRandomNodeType(currentLayer int32) int32 {
	// 每第4层遇到玩家
	if currentLayer%3 == 0 {
		return MAP_NODE_TYPE_PLAYER
	}
	// 其他层随机分配类型
	nodeTypes := []int32{
		MAP_NODE_TYPE_BATTLE,
		MAP_NODE_TYPE_BATTLE, // 战斗节点权重更高
		MAP_NODE_TYPE_BATTLE,
		MAP_NODE_TYPE_SHOP,
		MAP_NODE_TYPE_EVENT,
		MAP_NODE_TYPE_TREASURE,
	}
	return nodeTypes[ut.RandomInt32(0, int32(len(nodeTypes))-1)]
}

// 严格的区域分割算法，确保不会有路径交叉且每个子节点都有父节点
func assignChildrenByStrictRegions(parentLayer []*MapNode, childLayer []*MapNode) {
	parentCount := len(parentLayer)
	childCount := len(childLayer)
	if parentCount == 0 || childCount == 0 {
		return
	}
	// 第一步：确保每个子节点都有至少一个父节点
	childHasParent := make([]bool, childCount)
	// 为每个父节点分配严格的子节点区域
	for i, parent := range parentLayer {
		// 计算当前父节点的严格区域
		startIndex, endIndex := calculateStrictRegion(i, parentCount, childCount)
		// 确保区域内至少有一个子节点被连接
		if startIndex <= endIndex {
			// 随机选择区域内的一个子节点作为必连节点
			guaranteedChild := ut.RandomInt32(int32(startIndex), int32(endIndex))
			parent.Children = append(parent.Children, guaranteedChild)
			childHasParent[guaranteedChild] = true
		}
	}
	// 第二步：检查是否有子节点没有父节点，如果有则分配给最近的父节点
	for childIndex, hasParent := range childHasParent {
		if !hasParent {
			// 找到最适合的父节点（区域包含此子节点的父节点）
			bestParent := findBestParentForChild(childIndex, parentCount, childCount)
			if bestParent >= 0 && bestParent < len(parentLayer) {
				parentLayer[bestParent].Children = append(parentLayer[bestParent].Children, int32(childIndex))
				childHasParent[childIndex] = true
			}
		}
	}
	// 第三步：允许多个父节点连接到同一个子节点（在不交叉的前提下）
	for i, parent := range parentLayer {
		// 为当前父节点添加额外的子节点连接
		addAdditionalConnections(parent, i, parentLayer, childCount)
	}
}

// 计算父节点的严格区域，确保不会交叉
func calculateStrictRegion(parentIndex, parentCount, childCount int) (int, int) {
	if parentCount == 1 {
		// 如果只有一个父节点，可以连接所有子节点
		return 0, childCount - 1
	}
	// 将子节点平均分配给父节点，不允许重叠
	baseSize := childCount / parentCount
	remainder := childCount % parentCount
	// 计算当前父节点的起始位置
	startIndex := parentIndex * baseSize
	if parentIndex < remainder {
		startIndex += parentIndex
	} else {
		startIndex += remainder
	}
	// 计算当前父节点的区域大小
	regionSize := baseSize
	if parentIndex < remainder {
		regionSize++
	}
	endIndex := startIndex + regionSize - 1
	// 确保索引在有效范围内
	if startIndex >= childCount {
		startIndex = childCount - 1
	}
	if endIndex >= childCount {
		endIndex = childCount - 1
	}
	if startIndex > endIndex {
		endIndex = startIndex
	}
	return startIndex, endIndex
}

// 为孤立的子节点找到最适合的父节点
func findBestParentForChild(childIndex, parentCount, childCount int) int {
	// 找到哪个父节点的区域包含这个子节点
	for parentIndex := range parentCount {
		startIndex, endIndex := calculateStrictRegion(parentIndex, parentCount, childCount)
		if childIndex >= startIndex && childIndex <= endIndex {
			return parentIndex
		}
	}
	// 如果没有找到合适的区域（理论上不应该发生），返回最近的父节点
	bestParent := 0
	minDistance := childCount
	for parentIndex := range parentCount {
		startIndex, endIndex := calculateStrictRegion(parentIndex, parentCount, childCount)
		centerPos := (startIndex + endIndex) / 2
		distance := ut.Abs(childIndex - centerPos)
		if distance < minDistance {
			minDistance = distance
			bestParent = parentIndex
		}
	}
	return bestParent
}

// 为父节点添加额外的子节点连接，允许多个父节点连接同一个子节点
func addAdditionalConnections(parent *MapNode, parentIndex int, parentLayer []*MapNode, childCount int) {
	// 计算当前父节点可以连接的子节点范围（无交叉约束）
	allowedChildren := calculateAllowedChildren(parentIndex, parentLayer, childCount)
	// 过滤掉已经连接的子节点
	availableChildren := make([]int32, 0)
	for _, childIndex := range allowedChildren {
		if !slices.Contains(parent.Children, childIndex) {
			availableChildren = append(availableChildren, childIndex)
		}
	}
	// 随机添加1-2个额外连接
	if count := int32(len(availableChildren)); count > 0 {
		maxAdditional := ut.MinInt32(2, count)
		additionalCount := ut.RandomInt32(0, maxAdditional)
		if additionalCount > 0 {
			// 随机打乱可用子节点
			for i := len(availableChildren) - 1; i > 0; i-- {
				j := ut.RandomInt32(0, int32(i))
				availableChildren[i], availableChildren[j] = availableChildren[j], availableChildren[i]
			}
			// 添加额外连接
			for i := range additionalCount {
				parent.Children = append(parent.Children, availableChildren[i])
			}
		}
	}
}

// 计算父节点可以连接的所有子节点（不违反无交叉原则）
func calculateAllowedChildren(parentIndex int, parentLayer []*MapNode, childCount int) []int32 {
	parentCount := len(parentLayer)

	// 扩展区域：检查与相邻父节点的连接是否会造成交叉
	minAllowed := 0
	maxAllowed := childCount - 1

	// 检查左边的父节点
	for leftParent := range parentIndex {
		if len(parentLayer[leftParent].Children) > 0 {
			// 找到左边父节点连接的最右边的子节点
			maxChildOfLeft := int32(-1)
			for _, child := range parentLayer[leftParent].Children {
				if child > maxChildOfLeft {
					maxChildOfLeft = child
				}
			}
			// 当前父节点不能连接到比这个更左的子节点
			if int(maxChildOfLeft) >= minAllowed {
				minAllowed = int(maxChildOfLeft)
			}
		}
	}

	// 检查右边的父节点
	for rightParent := parentIndex + 1; rightParent < parentCount; rightParent++ {
		if len(parentLayer[rightParent].Children) > 0 {
			// 找到右边父节点连接的最左边的子节点
			minChildOfRight := int32(childCount)
			for _, child := range parentLayer[rightParent].Children {
				if child < minChildOfRight {
					minChildOfRight = child
				}
			}
			// 当前父节点不能连接到比这个更右的子节点
			if int(minChildOfRight) <= maxAllowed {
				maxAllowed = int(minChildOfRight)
			}
		}
	}

	// 生成允许的子节点列表
	allowedChildren := make([]int32, 0)
	for i := minAllowed; i <= maxAllowed; i++ {
		allowedChildren = append(allowedChildren, int32(i))
	}

	return allowedChildren
}
