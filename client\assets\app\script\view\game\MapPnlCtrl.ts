import { _decorator, log, ScrollView, Label, Node, EventTouch, v2 } from "cc";
import GameModel from "../../model/game/GameModel";
import MapNodeObj from "../../model/game/MapNodeObj";
const { ccclass } = _decorator;

@ccclass
export default class MapPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    private layersSv_: ScrollView = null // path://layers_sv
    //@end

    private game: GameModel = null

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        this.setParam({ isMask: false, isAct: false })
        this.game = this.getModel('game')
    }

    public onEnter() {
        const maps = this.game.getMaps()
        let lastLayerNode: Node = null, tempVec = v2()
        this.layersSv_.Items(maps, (it, layer) => {
            it.Data = layer
            it.Items(layer, (node, data) => {
                node.Child('val', Label).string = data.type + ''
            })
            // 绘制上一个的线
            if (lastLayerNode) {
                const lastLayerNodes: MapNodeObj[] = lastLayerNode.Data
                lastLayerNodes.forEach((m, i) => {
                    const preNode = lastLayerNode.children[i], prePos = preNode.position.toVec2()
                    const lineNode = preNode.Child('lines')
                    lineNode.Items(m.children, (line, id) => {
                        const curNode = it.children[id], curPos = ut.convertToNodeAR(curNode, lineNode, tempVec)
                        line.Height = curPos.subtract(prePos).length()
                        line.angle = ut.getAngle(curPos, prePos)
                    })
                })
            }
            lastLayerNode = it
        })
    }

    public onRemove() {
    }

    public onClean() {
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://layers_sv/view/content/layer/item_be
    onClickItem(event: EventTouch, data: string) {
        log('onClickItem', data)
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

}
