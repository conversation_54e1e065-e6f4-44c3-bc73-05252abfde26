import { easing, instantiate, js, Node, Prefab, tween, v3, view, Widget } from "cc"
import BasePnlCtrl from "../base/BasePnlCtrl"
import CoreEventType from "../event/CoreEventType"
import { loader } from "../utils/ResLoader"

export default class ViewCtrlMgr {

    public node: Node = null

    private caches: Map<string, BasePnlCtrl> = new Map<string, BasePnlCtrl>()
    private opened: BasePnlCtrl[] = []
    private masks: Node[] = [] // 遮罩列表
    private pnlOpenIndex: number = 0 // 打开顺序id
    private pnlIndexConf: any = {} // ui的层级关系配置

    private loadQueues: LoadPnlInfo[] = [] // 当前加载队列
    private loadId: number = 0

    public getOpened() {
        return this.opened
    }

    public setPnlIndexConf(conf: any) {
        this.pnlIndexConf = conf
    }

    // 添加到加载队列
    private addLoadQueue(name: string) {
        return this.loadQueues.add({ id: ++this.loadId, name: name, url: '' })
    }

    private hasLoadQueue(name: string) {
        return this.loadQueues.has('name', name)
    }

    private async __load(name: string, info: LoadPnlInfo) {
        // console.time('load ' + name)
        let pfb: Prefab = null, url: string = '', mod: string = ''
        let [wind, pnl] = name.split('/')
        if (!pnl) {// 没有输入模块的情况下
            // 先默认从当前模块找
            const head = ut.initialUpperCase(wind).replace('Pnl', '')
            mod = mc.currWindName
            info.url = url = `view/${mod}/${head}Pnl`
            loader.error = false
            pfb = await loader.loadRes(url, Prefab)
            loader.error = true
            if (!pfb) { //如果没有就从common里面找
                mod = 'common'
                info.url = url = `view/${mod}/${head}Pnl`
                pfb = await loader.loadRes(url, Prefab)
            }
        } else {
            const head = ut.initialUpperCase(pnl).replace('Pnl', '')
            mod = wind
            info.url = url = `view/${mod}/${head}Pnl`
            pfb = await loader.loadRes(url, Prefab)
        }
        // console.timeEnd('load ' + name)
        if (!this.loadQueues.remove('id', info.id)) {
            pfb = null
        }
        info.id = 0 //这里表示已经加载完成了 用于后续弹出是否重试检测是的时候
        return Promise.resolve({ mod, url, pfb })
    }

    // 加载一个Pnl
    public async __loadPnl(name: string, info: LoadPnlInfo): Promise<BasePnlCtrl> {
        const { mod, url, pfb } = await this.__load(name, info)
        if (!pfb) {
            return Promise.resolve(null)
        }
        const it = mc.instantiate(pfb, this.node)
        const className = it.name + 'Ctrl'
        if (!js.getClassByName(className)) {
            logger.error('loadPnl error! not found class ' + className)
            return Promise.resolve(null)
        }
        let pnl = it.getComponent(className)
        if (!pnl) {
            pnl = it.addComponent(className)
        }
        if (!pnl || !(pnl instanceof BasePnlCtrl)) {
            logger.error('loadPnl error! not found class ' + className)
            return Promise.resolve(null)
        }
        pnl.key = name
        pnl.mod = mod
        pnl.url = url
        // 这里检查一下 是否还没有加载属性
        if (!pnl._isLoadProperty) {
            logger.error('load pnl error! not load property. at=' + className)
            pnl.loadProperty()
        }
        it.active = false
        await pnl.__create()
        this.caches.set(url, pnl)
        return Promise.resolve(pnl)
    }

    // 获取缓存的Pnl
    private __getForCache(name: string) {
        let [wind, key] = name.split('/'), ui: BasePnlCtrl = null
        if (!key) {
            const head = ut.initialUpperCase(wind).replace('Pnl', '')
            ui = this.caches.get(`view/${mc.currWindName}/${head}Pnl`)
            if (!ui) {
                ui = this.caches.get(`view/common/${head}Pnl`)
            }
        } else {
            const head = ut.initialUpperCase(key).replace('Pnl', '')
            ui = this.caches.get(`view/${wind}/${head}Pnl`)
        }
        return ui
    }

    // 预加载
    public async preloadPnl(name: string) {
        let pnl = this.__getForCache(name)
        if (!pnl && !this.hasLoadQueue(name)) {
            pnl = await this.__loadPnl(name, this.addLoadQueue(name))
        }
        return pnl
    }

    // 获取一个遮罩
    private getMask(ui: BasePnlCtrl) {
        let it = this.masks.pop()
        if (!it) {
            const pfb = assetsMgr.getPrefab('PNL_MASK')
            if (pfb) {
                it = instantiate(pfb)
            }
        }
        if (it) {
            it.parent = this.node
            it.active = true
            it.opacity = 150
            it.zIndex = ui.node.zIndex - 1
        }
        ui.mask = it
    }

    private putMask(ui: BasePnlCtrl) {
        if (ui && ui.mask) {
            ui.mask.parent = null
            this.masks.push(ui.mask)
            ui.mask = null
        }
    }

    // 播放显示的动作
    private async playShowAction(ui: BasePnlCtrl) {
        const widget = ui.Component(Widget)
        if (widget && widget.enabled) {
            widget.updateAlignment()
            widget.enabled = false
        }
        // ui.node.stopAllActions()
        ui.node.scale = v3(0.4)
        tween(ui.node).to(0.25, { scale: v3(1) }, { easing: easing.backOut }).start()
        if (ui.mask) {
            // ui.mask.stopAllActions()
            ui.mask.opacity = 0
            tween(ui.mask).to(0.3, { opacity: 120 }, { easing: easing.sineOut }).start()
        }
        await ut.wait(0.25)
        if (ui.isValid) {
            ui.onPlayActionComplete()
            eventCenter.emit(CoreEventType.PNL_ENTER_PLAY_DONE, ui)
        }
    }

    // 适应大小
    private adaptRootSize(ui: BasePnlCtrl) {
        if (!ui) {
            return
        }
        const root = ui.Child('root') || ui.Child('root_n')
        if (!root) {
            return
        }
        const wsize = view.getVisibleSize()
        const dsize = view.getDesignResolutionSize()
        const rsize = root.transform.contentSize
        // 算出宽度比例
        let scale = (rsize.width / dsize.width * wsize.width) / rsize.width
        // 如果高度超过了
        const height = wsize.height - ui.adaptHeight
        if (rsize.height * scale > height) {
            scale = height / rsize.height
        }
        root.scale = v3(Math.min(1.2, scale))
    }

    private getNextOpenIndex() {
        return ++this.pnlOpenIndex
    }

    private pushPnl(ui: BasePnlCtrl) {
        ui.__open_index = this.getNextOpenIndex()
        this.opened.remove('url', ui.url)
        this.opened.push(ui)
        this.updatePnlIndex()
    }

    private popPnl(ui: BasePnlCtrl) {
        this.opened.remove('url', ui.url)
        this.updatePnlIndex()
    }

    // 刷新ui层级关系
    private updatePnlIndex() {
        // 排个序根据打开顺序
        const list = this.opened.sort((a, b) => a.__open_index - b.__open_index).map((m, i) => {
            const index = i + 1
            return {
                ui: m,
                key: m.key,
                initIndex: index,
                sortIndex: index * 1000
            }
        })
        list.forEach(m => {
            const conf = this.pnlIndexConf[m.key]
            if (!conf) {
                return
            }
            const lt: string[] = conf.lt?.slice()
            // 找出大于的 调整自己的位置 在大于的上面
            if (conf.gt) {
                const arr = list.filter(pnl => conf.gt.has(pnl.key)).sort((a, b) => b.sortIndex - a.sortIndex), temp = arr[0]
                if (temp && m.sortIndex < temp.sortIndex) {
                    m.sortIndex = temp.sortIndex + m.initIndex
                }
                // 这里如果小于的也在 当前大于的大于里面 就删除掉
                lt && arr.forEach(m => {
                    const gt = this.pnlIndexConf[m.key]?.gt
                    gt && lt.delete(s => gt.has(s))
                })
            }
            // 找出小于的 调整小于的位置 在自己的上面
            lt && list.filter(pnl => lt.has(pnl.key)).forEach(temp => {
                if (temp.sortIndex < m.sortIndex) {
                    temp.sortIndex = m.sortIndex + temp.initIndex
                }
            })
        })
        list.sort((a, b) => a.sortIndex - b.sortIndex).forEach((m, i) => m.ui.setIndex(i * 10))
        this.opened.sort((a, b) => a.node.zIndex - b.node.zIndex)
    }

    // 显示一个UI
    public async show(pnl: string | BasePnlCtrl, ...params: any): Promise<BasePnlCtrl> {
        mc.lockTouch('__show_pnl__')
        let ui: BasePnlCtrl = null
        if (typeof (pnl) === 'string') {
            ui = this.__getForCache(pnl)
            if (!ui && !this.hasLoadQueue(pnl)) {
                const data = this.addLoadQueue(pnl)
                data.params = params
                eventCenter.emit(CoreEventType.LOAD_BEGIN_PNL, data)
                ui = await this.__loadPnl(pnl, data)
                eventCenter.emit(CoreEventType.LOAD_END_PNL, data)
            }
        } else if (pnl.isValid && pnl._state !== 'clean') {
            ui = pnl
        } else {
            return this.show(pnl.key, ...params)
        }
        if (!ui || !ui.isValid) {
            mc.unlockTouch('__show_pnl__')
            return null
        } else if (!ui.getActive()) {
            this.pushPnl(ui)
            ui.setActive(true)
            ui.isMask && this.getMask(ui)
            ui.__enter(...params)
            this.adaptRootSize(ui)
            ui.setOpacity(255)
            // 发送进入事件
            eventCenter.emit(CoreEventType.PNL_ENTER, ui)
            if (ui.isAct) {
                this.playShowAction(ui).then(() => mc.unlockTouch('__show_pnl__'))
            } else {
                ui.node.scale = v3(1)
                ui.onPlayActionComplete()
                eventCenter.emit(CoreEventType.PNL_ENTER_PLAY_DONE, ui)
                mc.unlockTouch('__show_pnl__')
            }
        } else {
            mc.unlockTouch('__show_pnl__')
        }
        return ui
    }

    // 隐藏一个Pnl
    public hide(val: BasePnlCtrl | string) {
        const ui: BasePnlCtrl = val instanceof BasePnlCtrl ? val : this.__getForCache(val)
        if (!ui || !ui.isValid) {
            return
        }
        this.popPnl(ui)
        this.putMask(ui)
        if (ui.getActive()) {
            ui.__remove()
            ui.setActive(false)
            eventCenter.emit(CoreEventType.PNL_LEAVE, ui)
        }
    }

    // 隐藏所有Pnl
    public hideAll(val?: string, ignores?: string) {
        if (!val) {
            const arr = ignores ? ignores.split('|') : []
            for (let i = this.opened.length - 1; i >= 0; i--) {
                const m = this.opened[i]
                // 这里关闭所有的时候 忽略掉不清理的UI
                if (m.isClean && arr.indexOf(m.key) === -1) {
                    this.opened.splice(i, 1)
                    this.putMask(m)
                    if (m.getActive()) {
                        m.__remove()
                        m.setActive(false)
                        eventCenter.emit(CoreEventType.PNL_LEAVE, m)
                    }
                }
            }
            this.updatePnlIndex()
        } else {
            val.split('|').forEach(m => this.hide(m))
        }
    }

    // 清理一个Pnl
    public clean(val: BasePnlCtrl | string, force?: boolean) {
        const ui: BasePnlCtrl = val instanceof BasePnlCtrl ? val : this.__getForCache(val)
        if (!ui || (!ui.isClean && !force)) {
            return
        }
        this.hide(ui)
        ui.__clean()
        ui.node.destroy()
        this.caches.delete(ui.url)
        loader.releaseRes(ui.url, Prefab)
    }

    // 清理所有Pnl
    public cleanAll(val?: string, force?: boolean) {
        if (!val) {
            this.caches.forEach(m => this.clean(m, force))
            this.cleanLoadQueue()
        } else {
            val.split('|').forEach(m => {
                this.clean(m, force)
                this.giveupLoadByName(m)
            })
        }
    }

    // 清理pnl根据模块
    public cleanByMod(mod: string) {
        this.caches.forEach(m => m.mod === mod && this.clean(m))
    }

    // 清理所有未打开的Pnl
    public cleanAllUnused() {
        this.caches.forEach(m => !m.getActive() && this.clean(m))
        this.cleanLoadQueue()
    }

    // 清理加载队列
    public cleanLoadQueue(isUnlock?: boolean) {
        while (this.loadQueues.length > 0) {
            loader.giveupLoad(this.loadQueues.pop().url)
        }
        if (isUnlock) {
            mc.unlockTouch('__show_pnl__')
        }
    }

    // 放弃加载
    private giveupLoadByName(name: string) {
        const data = this.loadQueues.remove('name', name)
        data && loader.giveupLoad(data.url)
    }

    // 放弃当前加载
    public giveupLoadById(id: number) {
        const data = this.loadQueues.remove('id', id)
        data && loader.giveupLoad(data.url)
    }
}