System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, log, _class, _crd, ccclass, TestPnlCtrl;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      log = _cc.log;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "fc334MEzJNMJot2RLpqq1TV", "TestPnlCtrl", undefined);

      __checkObsolete__(['_decorator', 'log', 'EventTouch']);

      ({
        ccclass
      } = _decorator);

      _export("default", TestPnlCtrl = ccclass(_class = class TestPnlCtrl extends mc.BasePnlCtrl {
        //@autocode property begin
        //@end
        listenEventMaps() {
          return [];
        }

        async onCreate() {
          this.setParam({
            isMask: false
          });
        }

        onEnter(data) {
          log('TestPnlCtrl onEnter');
        }

        onRemove() {
          log('TestPnlCtrl onRemove');
        }

        onClean() {
          log('TestPnlCtrl onClean');
        } // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        // path://button_be


        onClickButton(event, data) {
          log('onClickButton', data);
        } //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------


      }) || _class);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4c4c64c6b982b7d11aef9f2cc0d8f0cf5f06f143.js.map