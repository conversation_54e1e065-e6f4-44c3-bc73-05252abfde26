System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, error, _crd, ROLE_FRAME_ANIM_CONF;

  // 获取角色的帧动画配置
  function getRoleFrameAnimConf(id) {
    var conf = ROLE_FRAME_ANIM_CONF[id];

    if (!conf) {
      error('getRoleFrameAnimConf error. id: ' + id);
      return null;
    } else if (!conf.url) {
      conf.url = "role/" + id + "/role_" + id + "_";
    }

    return conf;
  } // 角色动画帧配置


  _export("getRoleFrameAnimConf", getRoleFrameAnimConf);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      error = _cc.error;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6b5aaNmxGZG5oPM19FXmsD7", "RoleFrameAnimConf", undefined);

      __checkObsolete__(['error']);

      ROLE_FRAME_ANIM_CONF = {
        110001: {
          anims: [{
            name: 'idle',
            interval: 160,
            loop: true,
            frameIndexs: ['02', '03', '04', '05']
          }, {
            name: 'attack',
            interval: 140,
            loop: false,
            frameIndexs: ['12', '13', '14', '15', '21', '22', '23', '01']
          }]
        }
      };

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6d269b8b9ca74003b0b8879fb7f16c8bcaa33e92.js.map