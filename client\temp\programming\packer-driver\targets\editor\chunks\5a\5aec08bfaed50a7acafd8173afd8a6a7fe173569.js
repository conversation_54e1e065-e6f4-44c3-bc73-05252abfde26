System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, gHelper, viewHelper, _class, _crd, ccclass, LobbyWindCtrl;

  function _reportPossibleCrUseOfgHelper(extras) {
    _reporterNs.report("gHelper", "../../common/helper/GameHelper", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUserModel(extras) {
    _reporterNs.report("UserModel", "../../model/common/UserModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfviewHelper(extras) {
    _reporterNs.report("viewHelper", "../../common/helper/ViewHelper", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
    }, function (_unresolved_2) {
      gHelper = _unresolved_2.gHelper;
    }, function (_unresolved_3) {
      viewHelper = _unresolved_3.viewHelper;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "fb0cdPnQkFNsJ4/baOM/77z", "LobbyWindCtrl", undefined);

      __checkObsolete__(['_decorator', 'EventTouch', 'Label', 'log', 'Node']);

      ({
        ccclass
      } = _decorator);

      _export("default", LobbyWindCtrl = ccclass(_class = class LobbyWindCtrl extends mc.BaseWindCtrl {
        constructor(...args) {
          super(...args);
          //@autocode property begin
          this.enterGameNode_ = null;
          // path://enter_game_be_n
          //@end
          this.user = null;
        }

        listenEventMaps() {
          return [];
        }

        async onCreate() {
          this.user = this.getModel('user');
        }

        onEnter(data) {
          this.enterGameNode_.Child('val', Label).string = (_crd && gHelper === void 0 ? (_reportPossibleCrUseOfgHelper({
            error: Error()
          }), gHelper) : gHelper).game.getDay() ? '继续冒险' : '开始新的冒险';
        }

        onClean() {} // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        // path://enter_game_be_n


        onClickEnterGame(event, data) {
          this.enterGame();
        } //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------


        async enterGame() {
          const {
            err,
            data
          } = await (_crd && gHelper === void 0 ? (_reportPossibleCrUseOfgHelper({
            error: Error()
          }), gHelper) : gHelper).net.request('game/HD_Entry', {
            nickname: this.user.getNickname(),
            roleId: this.user.getRoleId()
          });

          if (err) {
            return (_crd && viewHelper === void 0 ? (_reportPossibleCrUseOfviewHelper({
              error: Error()
            }), viewHelper) : viewHelper).showAlert(err);
          }

          (_crd && gHelper === void 0 ? (_reportPossibleCrUseOfgHelper({
            error: Error()
          }), gHelper) : gHelper).game.init(data);
          (_crd && viewHelper === void 0 ? (_reportPossibleCrUseOfviewHelper({
            error: Error()
          }), viewHelper) : viewHelper).gotoWind('game');
        }

      }) || _class);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=5aec08bfaed50a7acafd8173afd8a6a7fe173569.js.map