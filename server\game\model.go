package game

import (
	"casrv/server/common/pb"
	rds "casrv/server/common/redis"
	ut "casrv/utils"
	"casrv/utils/array"
	"encoding/json"

	"github.com/huyangv/vmqant/log"
)

// 一个地图节点
type MapNode struct {
	Children []int32 `json:"children"` //子节点ID
	Type     int32   `json:"type"`     //类型
}

// 地图数据
type MapData struct {
	Maps [][]*MapNode `json:"maps"` //地图数据
}

func (this *MapData) ToPb() *pb.MapData {
	return &pb.MapData{Maps: array.Map(this.Maps, func(layer []*MapNode, _ int) *pb.MapLayer {
		return &pb.MapLayer{Nodes: array.Map(layer, func(m *MapNode, _ int) *pb.MapNode {
			return &pb.MapNode{Type: m.Type, Children: array.Clone(m.Children)}
		})}
	})}
}

// 获取地图数据
func GetMapData(uid string) *MapData {
	mapData := &MapData{}
	jsonStr, err := rds.RdsHGet(rds.RDS_GAME_DATA_KEY+uid, "map")
	if err == nil && jsonStr != "" {
		// 将JSON字符串反序列化为MapData对象
		if err := json.Unmarshal([]byte(jsonStr), mapData); err != nil {
			log.Error("GetMapData unmarshal error: %v", err)
		} else {
			return mapData
		}
	}
	// 随机地图数据
	mapData.Maps = GenerateMapData(10)
	// 保存
	SaveMapData(uid, mapData)
	return mapData
}

// 保存游戏数据到redis
func SaveMapData(uid string, data *MapData) error {
	// 将MapData序列化为JSON字符串
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		log.Error("SaveMapData marshal error: %v", err)
		return err
	}
	// 保存到redis hash中
	return rds.RdsHSet(rds.RDS_GAME_DATA_KEY+uid, "map", string(jsonBytes))
}

type Item struct {
	Id    int32     `json:"id"`
	Lv    int8      `json:"lv"`
	Attrs [][]int32 `json:"attrs"` //属性 {type, id, value} type: 0.生命 1.攻击 2.治疗
}

func (this *Item) ToPb() *pb.Item {
	return &pb.Item{Id: this.Id, Lv: int32(this.Lv), Attrs: CloneAttrsToPb(this.Attrs)}
}
func NewItem(id int32, lv int8) *Item { return &Item{Id: id, Lv: lv} }

// 商店信息
type ShopInfo struct {
	Id         int32   `json:"id"`         //商店id
	Items      []*Item `json:"items"`      //物品列表
	SelectItem *Item   `json:"selectItem"` //选择的物品
}

func (this *ShopInfo) ToPb() *pb.ShopInfo {
	var selectItem *Item = nil
	if this.SelectItem != nil {
		selectItem = this.SelectItem
	}
	return &pb.ShopInfo{Id: this.Id, Items: array.Map(this.Items, func(m *Item, _ int) *pb.Item { return m.ToPb() }), SelectItem: selectItem.ToPb()}
}

func (this *ShopInfo) SetSelectItem(item *Item) {
	this.SelectItem = item
	this.Items = []*Item{}
}

// 其他玩家的信息
type PlayerInfo struct {
	Animals  []*Item `json:"animals"` //动物列表
	Bags     []*Item `json:"bags"`    //背包
	UID      string  `json:"uid"`
	Nickname string  `json:"nickname"`
	RoleId   int32   `json:"roleId"`   //角色id
	Day      int32   `json:"day"`      //天数
	HP       []int32 `json:"hp"`       //生命
	WinCount int32   `json:"winCount"` //胜利次数
	Gold     int32   `json:"gold"`     //金币
	Earnings int32   `json:"earnings"` //收益
}

func (this *PlayerInfo) ToPb() *pb.PlayerInfo {
	return &pb.PlayerInfo{
		Animals:  array.Map(this.Animals, func(m *Item, _ int) *pb.Item { return m.ToPb() }),
		Bags:     array.Map(this.Bags, func(m *Item, _ int) *pb.Item { return m.ToPb() }),
		Uid:      this.UID,
		Nickname: this.Nickname,
		RoleId:   this.RoleId,
		Day:      this.Day,
		Hp:       array.Clone(this.HP),
		WinCount: this.WinCount,
		Gold:     this.Gold,
		Earnings: this.Earnings,
	}
}

func (this *PlayerInfo) ToBasePb() *pb.PlayerInfo {
	return &pb.PlayerInfo{
		Day:      this.Day,
		Hp:       array.Clone(this.HP),
		WinCount: this.WinCount,
	}
}

// 游戏数据
type GameData struct {
	Player      *PlayerInfo `json:"player"`      //自己的信息
	OtherPlayer *PlayerInfo `json:"otherPlayer"` //其他玩家信息
	Shop        *ShopInfo   `json:"shopInfo"`    //商店信息
	CreateTime  int64       `json:"createTime"`  //创建时间
	MapPaths    []int32     `json:"mapPaths"`    //走过的路径
}

func (this *GameData) ToPb() *pb.GameData {
	var otherPlayer *pb.PlayerInfo = nil
	if this.OtherPlayer != nil {
		otherPlayer = this.OtherPlayer.ToPb()
	}
	return &pb.GameData{
		Player:      this.Player.ToPb(),
		Shop:        this.Shop.ToPb(),
		OtherPlayer: otherPlayer,
		MapPaths:    array.Clone(this.MapPaths),
	}
}

// 获取游戏数据
func GetGameData(uid string) *GameData {
	jsonStr, err := rds.RdsHGet(rds.RDS_GAME_DATA_KEY+uid, "data")
	if err != nil || jsonStr == "" {
		return nil
	}
	// 将JSON字符串反序列化为GameData对象
	var gameData GameData
	if err := json.Unmarshal([]byte(jsonStr), &gameData); err != nil {
		log.Error("GetGameData unmarshal error: %v", err)
		return nil
	}
	return &gameData
}

// 保存游戏数据到redis
func SaveGameData(uid string, data *GameData) error {
	// 将GameData序列化为JSON字符串
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		log.Error("SaveGameData marshal error: %v", err)
		return err
	}
	// 保存到redis hash中
	return rds.RdsHSet(rds.RDS_GAME_DATA_KEY+uid, "data", string(jsonBytes))
}

// 创建游戏信息
func CreateGame(uid string, nickname string, roleId int32) *GameData {
	data := &GameData{CreateTime: ut.Now(), MapPaths: []int32{}}
	// 自己的数据
	data.Player = &PlayerInfo{
		UID:      uid,
		Nickname: nickname,
		RoleId:   roleId,
		Animals:  []*Item{},
		Bags:     []*Item{},
		Day:      1, //从第一天开始
		HP:       []int32{100, 100},
		WinCount: 0,
		Gold:     INIT_GOLD,     //金币
		Earnings: INIT_EARNINGS, //收益
	}
	// 随机三个东西 变异幼年动物 金币和收益 祝福
	data.Shop = &ShopInfo{
		Id:         BEGIN_GAME_SHOP_ID,
		Items:      []*Item{NewItem(SPECIAL_ITEM_RAND_ANIMAL, 0), NewItem(SPECIAL_ITEM_GOLD_EARNINGS, 0), NewItem(SPECIAL_ITEM_RAND_REMAINS, 0)},
		SelectItem: nil,
	}
	// 保存到redis
	SaveGameData(uid, data)
	return data
}

// 获取下一天的数据
func GetNextDayInfo(data *GameData) *GameData {
	// 添加遭遇

	// 增加天数和金币
	player := data.Player
	player.Day += 1
	player.Gold += player.Earnings
	return data
}
