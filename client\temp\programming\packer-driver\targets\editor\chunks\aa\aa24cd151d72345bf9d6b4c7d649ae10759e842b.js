System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, error, _crd, ANIMAL_FRAME_ANIM_CONF;

  // 获取动物的帧动画配置
  function getAnimalFrameAnimConf(id) {
    const conf = ANIMAL_FRAME_ANIM_CONF[id];

    if (!conf) {
      error('getAnimalFrameAnimConf error. id: ' + id);
      return null;
    } else if (!conf.url) {
      conf.url = `animal/${id}/animal_${id}_`;
    }

    return conf;
  } // 动物动画帧配置


  _export("getAnimalFrameAnimConf", getAnimalFrameAnimConf);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      error = _cc.error;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "85352MrBfNDhr+fIXTPFb7l", "AnimalFrameAnimConf", undefined);

      __checkObsolete__(['error']);

      ANIMAL_FRAME_ANIM_CONF = {
        211001: {
          //狮子
          anims: [{
            name: 'idle',
            interval: 100,
            loop: false,
            frameIndexs: ['01']
          }, {
            name: 'attack',
            interval: 140,
            loop: false,
            frameIndexs: ['01', '07', '08', '09', '10']
          }, {
            name: 'hit',
            interval: 140,
            loop: false,
            frameIndexs: ['01', '12', '12']
          }, {
            name: 'die',
            interval: 160,
            loop: false,
            frameIndexs: ['01', '12', '13', '14']
          }]
        }
      };

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=aa24cd151d72345bf9d6b4c7d649ae10759e842b.js.map