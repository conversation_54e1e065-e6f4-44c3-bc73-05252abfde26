2025-8-8 19:03:55-debug: start **** info
2025-8-8 19:03:55-log: Cannot access game frame or container.
2025-8-8 19:03:56-debug: asset-db:require-engine-code (1270ms)
2025-8-8 19:03:56-log: meshopt wasm decoder initialized
2025-8-8 19:03:56-log: [bullet]:bullet wasm lib loaded.
2025-8-8 19:03:56-log: [box2d]:box2d wasm lib loaded.
2025-8-8 19:03:56-log: Cocos Creator v3.8.6
2025-8-8 19:03:56-log: Using legacy pipeline
2025-8-8 19:03:56-log: Forward render pipeline initialized.
2025-8-8 19:03:56-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:31.77MB, end 79.88MB, increase: 48.12MB
2025-8-8 19:03:56-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.00MB, end 84.14MB, increase: 3.15MB
2025-8-8 19:03:59-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.18MB, end 270.05MB, increase: 185.87MB
2025-8-8 19:03:59-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:79.91MB, end 271.45MB, increase: 191.54MB
2025-8-8 19:03:59-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.85MB, end 271.41MB, increase: 190.57MB
2025-8-8 19:03:59-debug: asset-db:worker-init: initPlugin (2341ms)
2025-8-8 19:03:59-debug: [Assets Memory track]: asset-db:worker-init start:31.76MB, end 170.95MB, increase: 139.19MB
2025-8-8 19:03:59-debug: Run asset db hook programming:beforePreStart ...
2025-8-8 19:03:59-debug: Run asset db hook programming:beforePreStart success!
2025-8-8 19:03:59-debug: Run asset db hook engine-extends:beforePreStart ...
2025-8-8 19:03:59-debug: Run asset db hook engine-extends:beforePreStart success!
2025-8-8 19:04:01-debug: asset-db:worker-init (5688ms)
2025-8-8 19:04:01-debug: asset-db-hook-programming-beforePreStart (1836ms)
2025-8-8 19:04:01-debug: asset-db-hook-engine-extends-beforePreStart (1836ms)
2025-8-8 19:04:01-debug: Preimport db internal success
2025-8-8 19:04:02-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\model\game
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:04:02-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\common\constant\Enums.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:04:02-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\model\game\GameModel.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:04:02-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\model\game\MapNodeObj.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:04:02-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\lobby\LobbyWindCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:04:02-debug: Preimport db assets success
2025-8-8 19:04:02-debug: Run asset db hook programming:afterPreStart ...
2025-8-8 19:04:02-debug: starting packer-driver...
2025-8-8 19:04:04-debug: initialize scripting environment...
2025-8-8 19:04:04-debug: [[Executor]] prepare before lock
2025-8-8 19:04:04-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-8 19:04:04-debug: [[Executor]] prepare after unlock
2025-8-8 19:04:04-debug: Run asset db hook engine-extends:afterPreStart ...
2025-8-8 19:04:04-debug: Run asset db hook programming:afterPreStart success!
2025-8-8 19:04:04-debug: Run asset db hook engine-extends:afterPreStart success!
2025-8-8 19:04:04-debug: [Assets Memory track]: asset-db:worker-init: preStart start:170.96MB, end 181.61MB, increase: 10.64MB
2025-8-8 19:04:04-debug: Start up the 'internal' database...
2025-8-8 19:04:05-debug: asset-db-hook-programming-afterPreStart (2415ms)
2025-8-8 19:04:05-debug: asset-db:worker-effect-data-processing (1265ms)
2025-8-8 19:04:05-debug: asset-db-hook-engine-extends-afterPreStart (1265ms)
2025-8-8 19:04:05-debug: [Assets Memory track]: asset-db:worker-startup-database[internal] start:171.06MB, end 189.70MB, increase: 18.64MB
2025-8-8 19:04:05-debug: Start up the 'assets' database...
2025-8-8 19:04:08-debug: asset-db:worker-startup-database[internal] (8735ms)
2025-8-8 19:04:08-debug: [Assets Memory track]: asset-db:worker-startup-database[assets] start:176.56MB, end 191.58MB, increase: 15.02MB
2025-8-8 19:04:08-debug: lazy register asset handler *
2025-8-8 19:04:08-debug: lazy register asset handler directory
2025-8-8 19:04:08-debug: lazy register asset handler json
2025-8-8 19:04:08-debug: [Assets Memory track]: asset-db:worker-init: startup start:181.62MB, end 191.59MB, increase: 9.97MB
2025-8-8 19:04:08-debug: lazy register asset handler dragonbones
2025-8-8 19:04:08-debug: lazy register asset handler text
2025-8-8 19:04:08-debug: lazy register asset handler dragonbones-atlas
2025-8-8 19:04:08-debug: lazy register asset handler terrain
2025-8-8 19:04:08-debug: lazy register asset handler javascript
2025-8-8 19:04:08-debug: lazy register asset handler scene
2025-8-8 19:04:08-debug: lazy register asset handler prefab
2025-8-8 19:04:08-debug: lazy register asset handler tiled-map
2025-8-8 19:04:08-debug: lazy register asset handler sprite-frame
2025-8-8 19:04:08-debug: lazy register asset handler buffer
2025-8-8 19:04:08-debug: lazy register asset handler image
2025-8-8 19:04:08-debug: lazy register asset handler sign-image
2025-8-8 19:04:08-debug: lazy register asset handler alpha-image
2025-8-8 19:04:08-debug: lazy register asset handler typescript
2025-8-8 19:04:08-debug: lazy register asset handler texture-cube
2025-8-8 19:04:08-debug: lazy register asset handler render-texture
2025-8-8 19:04:08-debug: lazy register asset handler erp-texture-cube
2025-8-8 19:04:08-debug: lazy register asset handler texture-cube-face
2025-8-8 19:04:08-debug: lazy register asset handler spine-data
2025-8-8 19:04:08-debug: lazy register asset handler rt-sprite-frame
2025-8-8 19:04:08-debug: lazy register asset handler texture
2025-8-8 19:04:08-debug: lazy register asset handler gltf-mesh
2025-8-8 19:04:08-debug: lazy register asset handler gltf-animation
2025-8-8 19:04:08-debug: lazy register asset handler gltf
2025-8-8 19:04:08-debug: lazy register asset handler gltf-scene
2025-8-8 19:04:08-debug: lazy register asset handler gltf-embeded-image
2025-8-8 19:04:08-debug: lazy register asset handler fbx
2025-8-8 19:04:08-debug: lazy register asset handler gltf-skeleton
2025-8-8 19:04:08-debug: lazy register asset handler physics-material
2025-8-8 19:04:08-debug: lazy register asset handler material
2025-8-8 19:04:08-debug: lazy register asset handler gltf-material
2025-8-8 19:04:08-debug: lazy register asset handler effect-header
2025-8-8 19:04:08-debug: lazy register asset handler audio-clip
2025-8-8 19:04:08-debug: lazy register asset handler animation-clip
2025-8-8 19:04:08-debug: lazy register asset handler animation-graph
2025-8-8 19:04:08-debug: lazy register asset handler effect
2025-8-8 19:04:08-debug: lazy register asset handler animation-mask
2025-8-8 19:04:08-debug: lazy register asset handler animation-graph-variant
2025-8-8 19:04:08-debug: lazy register asset handler ttf-font
2025-8-8 19:04:08-debug: lazy register asset handler bitmap-font
2025-8-8 19:04:08-debug: lazy register asset handler sprite-atlas
2025-8-8 19:04:08-debug: lazy register asset handler auto-atlas
2025-8-8 19:04:08-debug: lazy register asset handler label-atlas
2025-8-8 19:04:08-debug: lazy register asset handler particle
2025-8-8 19:04:08-debug: lazy register asset handler render-pipeline
2025-8-8 19:04:08-debug: lazy register asset handler instantiation-material
2025-8-8 19:04:08-debug: lazy register asset handler render-stage
2025-8-8 19:04:08-debug: lazy register asset handler instantiation-mesh
2025-8-8 19:04:08-debug: lazy register asset handler instantiation-skeleton
2025-8-8 19:04:08-debug: lazy register asset handler video-clip
2025-8-8 19:04:08-debug: lazy register asset handler instantiation-animation
2025-8-8 19:04:08-debug: lazy register asset handler render-flow
2025-8-8 19:04:08-debug: asset-db:worker-startup-database[assets] (6334ms)
2025-8-8 19:04:08-debug: asset-db:start-database (8970ms)
2025-8-8 19:04:08-debug: fix the bug of updateDefaultUserData
2025-8-8 19:04:08-debug: asset-db:ready (18020ms)
2025-8-8 19:04:08-debug: init worker message success
2025-8-8 19:04:08-debug: programming:execute-script (9ms)
2025-8-8 19:04:08-debug: [Build Memory track]: builder:worker-init start:190.85MB, end 204.54MB, increase: 13.69MB
2025-8-8 19:04:08-debug: builder:worker-init (583ms)
2025-8-8 19:04:52-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\GameWind.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:04:52-debug: asset-db:reimport-assetc7a3e8ba-e61a-4454-9329-cef01e6896f6 (16ms)
2025-8-8 19:05:01-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\GameWind.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:05:01-debug: asset-db:reimport-assetc7a3e8ba-e61a-4454-9329-cef01e6896f6 (13ms)
2025-8-8 19:05:29-debug: refresh db internal success
2025-8-8 19:05:29-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\GameWindCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:05:29-debug: refresh db assets success
2025-8-8 19:05:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:05:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:05:29-debug: asset-db:refresh-all-database (209ms)
2025-8-8 19:05:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:05:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:05:34-debug: programming:execute-script (1ms)
2025-8-8 19:05:35-debug: start remove asset D:\Projects\cute-animals-client\client\assets\app\script\view\game\LoadingMaskCmpt.ts...
2025-8-8 19:05:36-debug: refresh db internal success
2025-8-8 19:05:36-debug: %cDestroy%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\LoadingMaskCmpt.ts
background: #ffb8b8; color: #000;
color: #000;
2025-8-8 19:05:36-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:05:36-log: 资源数据库已锁定，资源操作(bound _refreshAsset)将会延迟响应，请稍侯
2025-8-8 19:05:36-debug: refresh db assets success
2025-8-8 19:05:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:05:36-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\app\script\view\game\LoadingMaskCmpt.ts...
2025-8-8 19:05:36-debug: remove asset D:\Projects\cute-animals-client\client\assets\app\script\view\game\LoadingMaskCmpt.ts success
2025-8-8 19:05:36-debug: refresh asset D:\Projects\cute-animals-client\client\assets\app\script\view\game success
2025-8-8 19:05:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:05:36-debug: asset-db:refresh-all-database (222ms)
2025-8-8 19:05:36-debug: asset-db:worker-effect-data-processing (11ms)
2025-8-8 19:05:36-debug: asset-db-hook-engine-extends-afterRefresh (12ms)
2025-8-8 19:06:18-debug: refresh db internal success
2025-8-8 19:06:18-debug: refresh db assets success
2025-8-8 19:06:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:06:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:06:18-debug: asset-db:refresh-all-database (180ms)
2025-8-8 19:06:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:06:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:06:52-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\view\game\MapPnl.prefab...
2025-8-8 19:06:52-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\MapPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:06:52-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\view\game success
2025-8-8 19:06:52-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:06:56-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\GameWind.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:06:56-debug: asset-db:reimport-assetc7a3e8ba-e61a-4454-9329-cef01e6896f6 (14ms)
2025-8-8 19:07:02-debug: refresh db internal success
2025-8-8 19:07:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:07:02-debug: refresh db assets success
2025-8-8 19:07:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:07:02-debug: asset-db:refresh-all-database (182ms)
2025-8-8 19:07:05-debug: refresh db internal success
2025-8-8 19:07:05-debug: refresh db assets success
2025-8-8 19:07:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:07:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:07:05-debug: asset-db:refresh-all-database (208ms)
2025-8-8 19:07:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:07:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:07:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\MapPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:07:13-debug: asset-db:reimport-asset9033f3b5-d829-434f-a077-9a690c5917a4 (10ms)
2025-8-8 19:07:27-debug: refresh db internal success
2025-8-8 19:07:27-debug: refresh db assets success
2025-8-8 19:07:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:07:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:07:27-debug: asset-db:refresh-all-database (172ms)
2025-8-8 19:07:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:08:03-debug: refresh db internal success
2025-8-8 19:08:03-debug: refresh db assets success
2025-8-8 19:08:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:08:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:08:03-debug: asset-db:refresh-all-database (165ms)
2025-8-8 19:08:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:08:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:08:06-debug: refresh db internal success
2025-8-8 19:08:06-debug: refresh db assets success
2025-8-8 19:08:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:08:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:08:06-debug: asset-db:refresh-all-database (160ms)
2025-8-8 19:08:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:08:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:08:13-debug: refresh db internal success
2025-8-8 19:08:13-debug: refresh db assets success
2025-8-8 19:08:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:08:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:08:13-debug: asset-db:refresh-all-database (159ms)
2025-8-8 19:08:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:08:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:08:14-debug: refresh db internal success
2025-8-8 19:08:14-debug: refresh db assets success
2025-8-8 19:08:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:08:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:08:14-debug: asset-db:refresh-all-database (164ms)
2025-8-8 19:08:16-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\MapPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:08:16-debug: asset-db:reimport-asset9033f3b5-d829-434f-a077-9a690c5917a4 (11ms)
2025-8-8 19:08:17-debug: refresh db internal success
2025-8-8 19:08:18-debug: refresh db assets success
2025-8-8 19:08:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:08:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:08:18-debug: asset-db:refresh-all-database (161ms)
2025-8-8 19:08:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:08:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:08:22-debug: refresh db internal success
2025-8-8 19:08:22-debug: refresh db assets success
2025-8-8 19:08:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:08:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:08:22-debug: asset-db:refresh-all-database (163ms)
2025-8-8 19:08:22-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\MapPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:08:22-debug: asset-db:reimport-asset9033f3b5-d829-434f-a077-9a690c5917a4 (19ms)
2025-8-8 19:10:07-debug: refresh db internal success
2025-8-8 19:10:07-debug: refresh db assets success
2025-8-8 19:10:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:10:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:10:07-debug: asset-db:refresh-all-database (166ms)
2025-8-8 19:10:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:10:07-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 19:10:08-debug: refresh db internal success
2025-8-8 19:10:08-debug: refresh db assets success
2025-8-8 19:10:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:10:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:10:08-debug: asset-db:refresh-all-database (162ms)
2025-8-8 19:10:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:10:08-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 19:14:09-debug: refresh db internal success
2025-8-8 19:14:09-debug: refresh db assets success
2025-8-8 19:14:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:14:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:14:09-debug: asset-db:refresh-all-database (176ms)
2025-8-8 19:14:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:14:09-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 19:14:14-debug: refresh db internal success
2025-8-8 19:14:14-debug: refresh db assets success
2025-8-8 19:14:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:14:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:14:14-debug: asset-db:refresh-all-database (160ms)
2025-8-8 19:14:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:14:14-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 19:14:17-debug: refresh db internal success
2025-8-8 19:14:18-debug: refresh db assets success
2025-8-8 19:14:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:14:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:14:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:14:18-debug: asset-db:refresh-all-database (157ms)
2025-8-8 19:14:18-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 19:16:08-debug: refresh db internal success
2025-8-8 19:16:08-debug: refresh db assets success
2025-8-8 19:16:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:16:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:16:08-debug: asset-db:refresh-all-database (161ms)
2025-8-8 19:16:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:16:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:16:11-debug: refresh db internal success
2025-8-8 19:16:11-debug: refresh db assets success
2025-8-8 19:16:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:16:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:16:11-debug: asset-db:refresh-all-database (160ms)
2025-8-8 19:16:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:16:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:16:53-debug: refresh db internal success
2025-8-8 19:16:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:16:54-debug: refresh db assets success
2025-8-8 19:16:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:16:54-debug: asset-db:refresh-all-database (160ms)
2025-8-8 19:16:54-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:16:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:16:55-debug: refresh db internal success
2025-8-8 19:16:55-debug: refresh db assets success
2025-8-8 19:16:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:16:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:16:55-debug: asset-db:refresh-all-database (196ms)
2025-8-8 19:16:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:16:55-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 19:19:01-debug: refresh db internal success
2025-8-8 19:19:01-debug: refresh db assets success
2025-8-8 19:19:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:19:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:19:01-debug: asset-db:refresh-all-database (168ms)
2025-8-8 19:19:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:19:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:20:35-debug: refresh db internal success
2025-8-8 19:20:35-debug: refresh db assets success
2025-8-8 19:20:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:20:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:20:35-debug: asset-db:refresh-all-database (164ms)
2025-8-8 19:20:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:20:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:20:40-debug: refresh db internal success
2025-8-8 19:20:40-debug: refresh db assets success
2025-8-8 19:20:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:20:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:20:40-debug: asset-db:refresh-all-database (164ms)
2025-8-8 19:20:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:20:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:20:44-debug: refresh db internal success
2025-8-8 19:20:44-debug: refresh db assets success
2025-8-8 19:20:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:20:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:20:44-debug: asset-db:refresh-all-database (162ms)
2025-8-8 19:20:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:20:47-debug: refresh db internal success
2025-8-8 19:20:47-debug: refresh db assets success
2025-8-8 19:20:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:20:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:20:47-debug: asset-db:refresh-all-database (158ms)
2025-8-8 19:20:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:20:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:20:50-debug: refresh db internal success
2025-8-8 19:20:50-debug: refresh db assets success
2025-8-8 19:20:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:20:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:20:50-debug: asset-db:refresh-all-database (165ms)
2025-8-8 19:20:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:20:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:20:56-debug: refresh db internal success
2025-8-8 19:20:56-debug: refresh db assets success
2025-8-8 19:20:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:20:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:20:56-debug: asset-db:refresh-all-database (156ms)
2025-8-8 19:20:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:20:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:20:59-debug: refresh db internal success
2025-8-8 19:20:59-debug: refresh db assets success
2025-8-8 19:20:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:20:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:20:59-debug: asset-db:refresh-all-database (163ms)
2025-8-8 19:20:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:20:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:21:17-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\MapPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:21:17-debug: asset-db:reimport-asset9033f3b5-d829-434f-a077-9a690c5917a4 (12ms)
2025-8-8 19:21:23-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\MapPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:21:23-debug: asset-db:reimport-asset9033f3b5-d829-434f-a077-9a690c5917a4 (11ms)
2025-8-8 19:21:55-debug: refresh db internal success
2025-8-8 19:21:55-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:21:55-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:21:55-debug: refresh db assets success
2025-8-8 19:21:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:21:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:21:55-debug: asset-db:refresh-all-database (201ms)
2025-8-8 19:21:55-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-8 19:21:55-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 19:23:54-debug: refresh db internal success
2025-8-8 19:23:54-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:23:54-debug: refresh db assets success
2025-8-8 19:23:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:23:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:23:54-debug: asset-db:refresh-all-database (176ms)
2025-8-8 19:23:54-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:23:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:23:57-debug: refresh db internal success
2025-8-8 19:23:57-debug: refresh db assets success
2025-8-8 19:23:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:23:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:23:57-debug: asset-db:refresh-all-database (159ms)
2025-8-8 19:23:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:23:57-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 19:25:04-debug: refresh db internal success
2025-8-8 19:25:04-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:25:04-debug: refresh db assets success
2025-8-8 19:25:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:25:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:25:04-debug: asset-db:refresh-all-database (178ms)
2025-8-8 19:25:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:25:33-debug: refresh db internal success
2025-8-8 19:25:33-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:25:33-debug: refresh db assets success
2025-8-8 19:25:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:25:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:25:33-debug: asset-db:refresh-all-database (164ms)
2025-8-8 19:37:28-debug: refresh db internal success
2025-8-8 19:37:28-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\GameWindCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:37:28-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:37:28-debug: refresh db assets success
2025-8-8 19:37:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:37:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:37:28-debug: asset-db:refresh-all-database (205ms)
2025-8-8 19:37:51-debug: refresh db internal success
2025-8-8 19:37:51-debug: refresh db assets success
2025-8-8 19:37:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:37:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:37:51-debug: asset-db:refresh-all-database (185ms)
2025-8-8 19:37:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:37:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:37:56-debug: Query all assets info in project
2025-8-8 19:37:56-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 19:37:56-debug: Skip compress image, progress: 0%
2025-8-8 19:37:56-debug: Init all bundles start..., progress: 0%
2025-8-8 19:37:56-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:37:56-debug: Num of bundles: 3..., progress: 0%
2025-8-8 19:37:56-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 19:37:56-debug: Init bundle root assets start..., progress: 0%
2025-8-8 19:37:56-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 19:37:56-debug:   Number of all scenes: 1
2025-8-8 19:37:56-debug:   Number of all scripts: 122
2025-8-8 19:37:56-debug: Init bundle root assets success..., progress: 0%
2025-8-8 19:37:56-debug:   Number of other assets: 1548
2025-8-8 19:37:56-debug: // ---- build task 查询 Asset Bundle ---- (18ms)
2025-8-8 19:37:56-log: run build task 查询 Asset Bundle success in 18 ms√, progress: 5%
2025-8-8 19:37:56-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 19:37:56-debug: [Build Memory track]: 查询 Asset Bundle start:202.98MB, end 203.80MB, increase: 841.16KB
2025-8-8 19:37:56-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:37:56-debug: // ---- build task 查询 Asset Bundle ---- (5ms)
2025-8-8 19:37:56-log: run build task 查询 Asset Bundle success in 5 ms√, progress: 10%
2025-8-8 19:37:56-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:37:56-debug: [Build Memory track]: 查询 Asset Bundle start:203.83MB, end 204.15MB, increase: 330.59KB
2025-8-8 19:37:56-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 19:37:56-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-8 19:37:56-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-8-8 19:37:56-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 19:37:56-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:204.18MB, end 204.21MB, increase: 30.91KB
2025-8-8 19:37:56-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 19:37:56-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-8 19:37:56-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-8 19:37:56-debug: [Build Memory track]: 填充脚本数据到 settings.json start:204.24MB, end 204.26MB, increase: 26.77KB
2025-8-8 19:37:56-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 19:37:56-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:37:56-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-8 19:37:56-log: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 15%
2025-8-8 19:37:56-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:204.29MB, end 204.43MB, increase: 142.44KB
2025-8-8 19:40:02-debug: refresh db internal success
2025-8-8 19:40:02-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\model\login\LoginModel.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:40:02-debug: refresh db assets success
2025-8-8 19:40:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:40:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:40:02-debug: asset-db:refresh-all-database (185ms)
2025-8-8 19:40:03-debug: Query all assets info in project
2025-8-8 19:40:03-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 19:40:03-debug: Skip compress image, progress: 0%
2025-8-8 19:40:03-debug: Init all bundles start..., progress: 0%
2025-8-8 19:40:03-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 19:40:03-debug: Num of bundles: 3..., progress: 0%
2025-8-8 19:40:03-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:40:03-debug: Init bundle root assets start..., progress: 0%
2025-8-8 19:40:03-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 19:40:03-debug:   Number of all scripts: 122
2025-8-8 19:40:03-debug:   Number of other assets: 1548
2025-8-8 19:40:03-debug:   Number of all scenes: 1
2025-8-8 19:40:03-debug: Init bundle root assets success..., progress: 0%
2025-8-8 19:40:03-debug: // ---- build task 查询 Asset Bundle ---- (24ms)
2025-8-8 19:40:03-log: run build task 查询 Asset Bundle success in 24 ms√, progress: 5%
2025-8-8 19:40:03-debug: [Build Memory track]: 查询 Asset Bundle start:207.95MB, end 208.02MB, increase: 65.56KB
2025-8-8 19:40:03-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 19:40:03-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:40:03-debug: // ---- build task 查询 Asset Bundle ---- (9ms)
2025-8-8 19:40:03-log: run build task 查询 Asset Bundle success in 9 ms√, progress: 10%
2025-8-8 19:40:03-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 19:40:03-debug: [Build Memory track]: 查询 Asset Bundle start:208.05MB, end 208.34MB, increase: 297.56KB
2025-8-8 19:40:03-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:40:03-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.38MB, end 208.40MB, increase: 17.82KB
2025-8-8 19:40:03-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 19:40:03-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-8 19:40:03-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 19:40:03-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 19:40:03-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 19:40:03-debug: [Build Memory track]: 填充脚本数据到 settings.json start:208.43MB, end 208.46MB, increase: 32.07KB
2025-8-8 19:40:03-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 19:40:03-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:40:03-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (7ms)
2025-8-8 19:40:03-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.49MB, end 208.64MB, increase: 156.71KB
2025-8-8 19:40:03-log: run build task 整理部分构建选项内数据到 settings.json success in 7 ms√, progress: 15%
2025-8-8 19:40:29-debug: refresh db internal success
2025-8-8 19:40:29-debug: refresh db assets success
2025-8-8 19:40:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:40:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:40:29-debug: asset-db:refresh-all-database (179ms)
2025-8-8 19:40:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:40:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:41:01-debug: refresh db internal success
2025-8-8 19:41:01-debug: refresh db assets success
2025-8-8 19:41:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:41:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:41:01-debug: asset-db:refresh-all-database (173ms)
2025-8-8 19:41:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:41:14-debug: refresh db internal success
2025-8-8 19:41:14-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\GameWindCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:41:14-debug: refresh db assets success
2025-8-8 19:41:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:41:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:41:14-debug: asset-db:refresh-all-database (200ms)
2025-8-8 19:41:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:41:14-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 19:41:16-debug: Query all assets info in project
2025-8-8 19:41:16-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 19:41:16-debug: Skip compress image, progress: 0%
2025-8-8 19:41:16-debug: Init all bundles start..., progress: 0%
2025-8-8 19:41:16-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:41:16-debug: Init bundle root assets start..., progress: 0%
2025-8-8 19:41:16-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 19:41:16-debug: Num of bundles: 3..., progress: 0%
2025-8-8 19:41:16-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 19:41:16-debug:   Number of all scripts: 122
2025-8-8 19:41:16-debug:   Number of other assets: 1548
2025-8-8 19:41:16-debug: Init bundle root assets success..., progress: 0%
2025-8-8 19:41:16-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-8-8 19:41:16-log: run build task 查询 Asset Bundle success in 17 ms√, progress: 5%
2025-8-8 19:41:16-debug: [Build Memory track]: 查询 Asset Bundle start:215.83MB, end 213.70MB, increase: -2175.66KB
2025-8-8 19:41:16-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:41:16-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 19:41:16-debug:   Number of all scenes: 1
2025-8-8 19:41:16-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-8 19:41:16-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-8 19:41:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:41:16-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 19:41:16-debug: [Build Memory track]: 查询 Asset Bundle start:213.73MB, end 214.02MB, increase: 296.34KB
2025-8-8 19:41:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-8 19:41:16-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-8-8 19:41:16-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 19:41:16-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 19:41:16-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.35MB, end 214.08MB, increase: -272.46KB
2025-8-8 19:41:16-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-8 19:41:16-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-8 19:41:16-debug: [Build Memory track]: 填充脚本数据到 settings.json start:214.12MB, end 214.14MB, increase: 26.10KB
2025-8-8 19:41:16-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 19:41:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:41:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (10ms)
2025-8-8 19:41:16-log: run build task 整理部分构建选项内数据到 settings.json success in 10 ms√, progress: 15%
2025-8-8 19:41:16-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.17MB, end 214.30MB, increase: 128.54KB
2025-8-8 19:41:50-debug: refresh db internal success
2025-8-8 19:41:50-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\common\config\RoleFrameAnimConf.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:41:50-debug: refresh db assets success
2025-8-8 19:41:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:41:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:41:50-debug: asset-db:refresh-all-database (193ms)
2025-8-8 19:41:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:41:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:41:51-debug: Query all assets info in project
2025-8-8 19:41:51-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 19:41:51-debug: Skip compress image, progress: 0%
2025-8-8 19:41:51-debug: Init all bundles start..., progress: 0%
2025-8-8 19:41:51-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:41:51-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 19:41:51-debug: Num of bundles: 3..., progress: 0%
2025-8-8 19:41:51-debug: Init bundle root assets start..., progress: 0%
2025-8-8 19:41:51-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 19:41:51-debug:   Number of other assets: 1548
2025-8-8 19:41:51-debug: Init bundle root assets success..., progress: 0%
2025-8-8 19:41:51-debug:   Number of all scenes: 1
2025-8-8 19:41:51-debug:   Number of all scripts: 122
2025-8-8 19:41:51-debug: // ---- build task 查询 Asset Bundle ---- (13ms)
2025-8-8 19:41:51-log: run build task 查询 Asset Bundle success in 13 ms√, progress: 5%
2025-8-8 19:41:51-debug: [Build Memory track]: 查询 Asset Bundle start:217.76MB, end 217.72MB, increase: -41.02KB
2025-8-8 19:41:51-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 19:41:51-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:41:51-debug: // ---- build task 查询 Asset Bundle ---- (5ms)
2025-8-8 19:41:51-log: run build task 查询 Asset Bundle success in 5 ms√, progress: 10%
2025-8-8 19:41:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:41:51-debug: [Build Memory track]: 查询 Asset Bundle start:217.75MB, end 218.04MB, increase: 297.52KB
2025-8-8 19:41:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 19:41:51-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-8 19:41:51-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.07MB, end 218.10MB, increase: 27.48KB
2025-8-8 19:41:51-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 19:41:51-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 19:41:51-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 19:41:51-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 19:41:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 19:41:51-debug: [Build Memory track]: 填充脚本数据到 settings.json start:218.13MB, end 218.15MB, increase: 26.05KB
2025-8-8 19:41:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:41:51-log: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 15%
2025-8-8 19:41:51-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.18MB, end 218.31MB, increase: 127.33KB
2025-8-8 19:41:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-8 19:41:59-debug: refresh db internal success
2025-8-8 19:41:59-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\common\config\AnimalFrameAnimConf.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:41:59-debug: refresh db assets success
2025-8-8 19:41:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:41:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:41:59-debug: asset-db:refresh-all-database (185ms)
2025-8-8 19:41:59-debug: asset-db-hook-engine-extends-afterRefresh (6ms)
2025-8-8 19:41:59-debug: asset-db:worker-effect-data-processing (6ms)
2025-8-8 19:42:00-debug: Query all assets info in project
2025-8-8 19:42:00-debug: Skip compress image, progress: 0%
2025-8-8 19:42:00-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 19:42:00-debug: Init all bundles start..., progress: 0%
2025-8-8 19:42:00-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:42:00-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 19:42:00-debug: Num of bundles: 3..., progress: 0%
2025-8-8 19:42:00-debug: Init bundle root assets start..., progress: 0%
2025-8-8 19:42:00-debug:   Number of all scenes: 1
2025-8-8 19:42:00-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 19:42:00-debug: Init bundle root assets success..., progress: 0%
2025-8-8 19:42:00-debug:   Number of all scripts: 122
2025-8-8 19:42:00-debug:   Number of other assets: 1548
2025-8-8 19:42:00-log: run build task 查询 Asset Bundle success in 17 ms√, progress: 5%
2025-8-8 19:42:00-debug: [Build Memory track]: 查询 Asset Bundle start:203.46MB, end 199.81MB, increase: -3737.76KB
2025-8-8 19:42:00-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-8-8 19:42:00-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:42:00-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 19:42:00-debug: // ---- build task 查询 Asset Bundle ---- (6ms)
2025-8-8 19:42:00-log: run build task 查询 Asset Bundle success in 6 ms√, progress: 10%
2025-8-8 19:42:00-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 19:42:00-debug: [Build Memory track]: 查询 Asset Bundle start:199.85MB, end 200.16MB, increase: 318.36KB
2025-8-8 19:42:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:42:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 19:42:00-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 19:42:00-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 19:42:00-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:200.19MB, end 200.22MB, increase: 26.63KB
2025-8-8 19:42:00-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 19:42:00-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-8 19:42:00-debug: [Build Memory track]: 填充脚本数据到 settings.json start:200.25MB, end 200.27MB, increase: 27.63KB
2025-8-8 19:42:00-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-8 19:42:00-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 19:42:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:42:00-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (10ms)
2025-8-8 19:42:00-log: run build task 整理部分构建选项内数据到 settings.json success in 10 ms√, progress: 15%
2025-8-8 19:42:00-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:200.31MB, end 200.44MB, increase: 132.57KB
2025-8-8 19:42:13-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\1001 -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001...
2025-8-8 19:42:13-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001...
2025-8-8 19:42:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_01.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_02.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_05.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_04.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_03.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_01.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_05.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_02.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_03.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_04.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_01.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_05.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_02.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_03.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_04.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_12.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_14.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_13.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_15.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_21.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_14.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_13.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_12.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_15.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_21.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_13.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_12.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_14.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_15.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_21.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_22.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_23.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_22.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_23.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_22.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_23.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role success
2025-8-8 19:42:13-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role...
2025-8-8 19:42:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:13-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image success
2025-8-8 19:42:13-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\1001 -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001 success
2025-8-8 19:42:18-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_22.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_22.png...
2025-8-8 19:42:18-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_22.png...
2025-8-8 19:42:18-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_22.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:18-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_22.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:18-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_22.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:18-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001 success
2025-8-8 19:42:18-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001...
2025-8-8 19:42:18-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:18-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role success
2025-8-8 19:42:18-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_22.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_22.png success
2025-8-8 19:42:21-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_01.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_01.png...
2025-8-8 19:42:21-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_01.png...
2025-8-8 19:42:21-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_01.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:21-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_01.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:21-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_01.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:21-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001 success
2025-8-8 19:42:21-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001...
2025-8-8 19:42:21-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:21-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role success
2025-8-8 19:42:21-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_01.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_01.png success
2025-8-8 19:42:25-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_23.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_23.png...
2025-8-8 19:42:25-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_23.png...
2025-8-8 19:42:25-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_23.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:25-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_23.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:25-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_23.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:25-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001 success
2025-8-8 19:42:25-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001...
2025-8-8 19:42:25-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:25-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role success
2025-8-8 19:42:25-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_23.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_23.png success
2025-8-8 19:42:28-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_21.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_21.png...
2025-8-8 19:42:28-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_21.png...
2025-8-8 19:42:28-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_21.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:28-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_21.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:28-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_21.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:28-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001 success
2025-8-8 19:42:28-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001...
2025-8-8 19:42:28-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:28-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_21.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_21.png success
2025-8-8 19:42:28-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role success
2025-8-8 19:42:32-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_15.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_15.png...
2025-8-8 19:42:32-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_15.png...
2025-8-8 19:42:32-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_15.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:32-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_15.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:32-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_15.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:32-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001 success
2025-8-8 19:42:32-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001...
2025-8-8 19:42:32-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:32-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role success
2025-8-8 19:42:32-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_15.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_15.png success
2025-8-8 19:42:35-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_14.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_14.png...
2025-8-8 19:42:35-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_14.png...
2025-8-8 19:42:35-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_14.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:35-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_14.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:35-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_14.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:35-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001 success
2025-8-8 19:42:35-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001...
2025-8-8 19:42:35-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:35-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role success
2025-8-8 19:42:35-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_14.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_14.png success
2025-8-8 19:42:38-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_13.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_13.png...
2025-8-8 19:42:38-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_13.png...
2025-8-8 19:42:38-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_13.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:38-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_13.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:38-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_13.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:38-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001 success
2025-8-8 19:42:38-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001...
2025-8-8 19:42:38-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:38-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role success
2025-8-8 19:42:38-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_13.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_13.png success
2025-8-8 19:42:41-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_12.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_12.png...
2025-8-8 19:42:41-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_12.png...
2025-8-8 19:42:41-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_12.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:41-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_12.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:41-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_12.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:41-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001 success
2025-8-8 19:42:41-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001...
2025-8-8 19:42:41-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:41-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role success
2025-8-8 19:42:41-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_12.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_12.png success
2025-8-8 19:42:43-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_05.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_05.png...
2025-8-8 19:42:43-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_05.png...
2025-8-8 19:42:43-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_05.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:43-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_05.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:43-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_05.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:43-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001 success
2025-8-8 19:42:43-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001...
2025-8-8 19:42:43-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:43-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_05.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_05.png success
2025-8-8 19:42:43-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role success
2025-8-8 19:42:47-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_04.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_04.png...
2025-8-8 19:42:47-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_04.png...
2025-8-8 19:42:47-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_04.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:47-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_04.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:47-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_04.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:47-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001 success
2025-8-8 19:42:47-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001...
2025-8-8 19:42:47-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:47-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role success
2025-8-8 19:42:47-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_04.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_04.png success
2025-8-8 19:42:50-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_03.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_03.png...
2025-8-8 19:42:50-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_03.png...
2025-8-8 19:42:50-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_03.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:50-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_03.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:50-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_03.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:50-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001 success
2025-8-8 19:42:50-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001...
2025-8-8 19:42:50-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:50-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_03.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_03.png success
2025-8-8 19:42:50-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role success
2025-8-8 19:42:52-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_02.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_02.png...
2025-8-8 19:42:52-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_02.png...
2025-8-8 19:42:52-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_02.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:52-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_02.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:52-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_02.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:52-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001 success
2025-8-8 19:42:52-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001...
2025-8-8 19:42:52-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:42:52-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role success
2025-8-8 19:42:52-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_1001_02.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\role\110001\role_110001_02.png success
2025-8-8 19:43:00-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\11001 -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:00-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\0.pac
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_02.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_01.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_03.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_04.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_02.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_01.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_03.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_02.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_01.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_03.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_04.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_04.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_07.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_05.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_08.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_09.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_10.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_07.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_05.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_08.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_10.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_09.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_07.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_05.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_08.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_09.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_10.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_12.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_14.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_16.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_13.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_17.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_12.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_12.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_13.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_14.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_16.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_17.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_14.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_13.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_16.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_17.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_18.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_19.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_20.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_18.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_19.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_20.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_18.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_19.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_20.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:00-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal...
2025-8-8 19:43:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:00-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image success
2025-8-8 19:43:00-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\11001 -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:05-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_01.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_01.png...
2025-8-8 19:43:05-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_01.png...
2025-8-8 19:43:05-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_01.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:05-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_01.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:05-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_01.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:05-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:05-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:05-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:05-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:05-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_01.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_01.png success
2025-8-8 19:43:09-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_02.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_02.png...
2025-8-8 19:43:09-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_02.png...
2025-8-8 19:43:09-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_02.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:09-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_02.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:09-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_02.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:09-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:09-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:09-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:09-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:09-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_02.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_02.png success
2025-8-8 19:43:16-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001.png...
2025-8-8 19:43:16-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001.png...
2025-8-8 19:43:16-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:16-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:16-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:16-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:16-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:16-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:16-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:16-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001.png success
2025-8-8 19:43:19-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_20.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_20.png...
2025-8-8 19:43:19-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_20.png...
2025-8-8 19:43:19-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_20.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:19-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_20.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:19-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_20.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:19-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:19-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:19-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:19-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:19-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_20.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_20.png success
2025-8-8 19:43:23-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_19.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_19.png...
2025-8-8 19:43:23-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_19.png...
2025-8-8 19:43:23-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_19.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:23-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_19.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:23-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_19.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:23-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:23-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:23-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:23-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:23-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_19.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_19.png success
2025-8-8 19:43:26-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_18.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_18.png...
2025-8-8 19:43:26-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_18.png...
2025-8-8 19:43:26-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_18.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:26-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_18.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:26-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_18.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:26-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:26-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:26-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:26-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:26-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_18.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_18.png success
2025-8-8 19:43:29-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_17.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_17.png...
2025-8-8 19:43:29-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_17.png...
2025-8-8 19:43:29-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_17.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:29-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_17.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:29-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_17.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:29-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:29-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:29-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:29-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:29-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_17.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_17.png success
2025-8-8 19:43:32-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_16.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_16.png...
2025-8-8 19:43:32-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_16.png...
2025-8-8 19:43:32-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_16.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:32-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_16.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:32-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_16.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:32-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:32-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:32-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:32-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:32-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_16.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_16.png success
2025-8-8 19:43:35-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_14.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_14.png...
2025-8-8 19:43:35-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_14.png...
2025-8-8 19:43:35-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_14.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:35-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_14.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:35-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_14.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:35-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:35-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:35-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:35-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:35-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_14.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_14.png success
2025-8-8 19:43:38-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_13.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_13.png...
2025-8-8 19:43:38-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_13.png...
2025-8-8 19:43:38-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_13.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:38-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_13.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:38-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_13.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:38-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:38-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:38-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:38-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:38-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_13.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_13.png success
2025-8-8 19:43:41-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_12.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_12.png...
2025-8-8 19:43:41-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_12.png...
2025-8-8 19:43:41-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_12.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:41-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_12.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:41-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_12.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:41-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:41-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:41-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:41-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:41-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_12.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_12.png success
2025-8-8 19:43:44-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_10.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_10.png...
2025-8-8 19:43:44-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_10.png...
2025-8-8 19:43:44-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_10.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:44-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_10.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:44-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_10.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:44-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:44-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:44-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:44-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:44-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_10.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_10.png success
2025-8-8 19:43:46-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_09.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_09.png...
2025-8-8 19:43:46-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_09.png...
2025-8-8 19:43:46-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_09.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:46-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_09.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:46-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_09.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:46-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:46-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:46-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:46-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:46-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_09.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_09.png success
2025-8-8 19:43:49-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_08.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_08.png...
2025-8-8 19:43:49-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_08.png...
2025-8-8 19:43:49-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_08.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:49-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_08.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:49-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_08.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:49-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:49-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:49-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:49-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:49-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_08.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_08.png success
2025-8-8 19:43:52-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_07.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_07.png...
2025-8-8 19:43:52-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_07.png...
2025-8-8 19:43:52-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_07.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:52-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_07.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:52-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_07.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:52-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:52-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:52-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:52-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:52-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_07.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_07.png success
2025-8-8 19:43:55-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_05.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_05.png...
2025-8-8 19:43:55-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_05.png...
2025-8-8 19:43:55-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_05.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:55-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_05.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:55-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_05.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:55-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:55-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:55-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:55-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:55-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_05.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_05.png success
2025-8-8 19:43:58-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_04.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_04.png...
2025-8-8 19:43:58-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_04.png...
2025-8-8 19:43:58-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_04.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:58-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_04.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:58-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_04.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:58-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:43:58-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:43:58-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:43:58-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:43:58-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_04.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_04.png success
2025-8-8 19:44:01-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_03.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_03.png...
2025-8-8 19:44:01-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_03.png...
2025-8-8 19:44:01-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_03.png
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:44:01-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_03.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:44:01-debug: %cReImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_03.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:44:01-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001 success
2025-8-8 19:44:01-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001...
2025-8-8 19:44:01-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:44:01-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal success
2025-8-8 19:44:01-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_11001_03.png -> D:\Projects\cute-animals-client\client\assets\resources\tmp\image\animal\211001\animal_211001_03.png success
2025-8-8 19:44:02-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\tmp\prefab\role\ROLE_120001.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:44:02-debug: asset-db:reimport-assetaaf74af6-ac53-4337-8e92-02c707f9baa9 (12ms)
2025-8-8 19:44:04-debug: Query all assets info in project
2025-8-8 19:44:04-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 19:44:04-debug: Skip compress image, progress: 0%
2025-8-8 19:44:04-debug: Init all bundles start..., progress: 0%
2025-8-8 19:44:04-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:44:04-debug: Num of bundles: 3..., progress: 0%
2025-8-8 19:44:04-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 19:44:04-debug: Init bundle root assets start..., progress: 0%
2025-8-8 19:44:04-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 19:44:04-debug:   Number of all scenes: 1
2025-8-8 19:44:04-debug:   Number of all scripts: 122
2025-8-8 19:44:04-debug:   Number of other assets: 1548
2025-8-8 19:44:04-debug: Init bundle root assets success..., progress: 0%
2025-8-8 19:44:04-debug: // ---- build task 查询 Asset Bundle ---- (15ms)
2025-8-8 19:44:04-debug: [Build Memory track]: 查询 Asset Bundle start:210.27MB, end 208.25MB, increase: -2060.88KB
2025-8-8 19:44:04-log: run build task 查询 Asset Bundle success in 15 ms√, progress: 5%
2025-8-8 19:44:04-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:44:04-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 19:44:04-debug: // ---- build task 查询 Asset Bundle ---- (8ms)
2025-8-8 19:44:04-log: run build task 查询 Asset Bundle success in 8 ms√, progress: 10%
2025-8-8 19:44:04-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 19:44:04-debug: [Build Memory track]: 查询 Asset Bundle start:208.28MB, end 208.57MB, increase: 295.20KB
2025-8-8 19:44:04-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:44:04-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 12%
2025-8-8 19:44:04-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.60MB, end 208.63MB, increase: 27.46KB
2025-8-8 19:44:04-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-8 19:44:04-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 19:44:04-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 19:44:04-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 19:44:04-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 19:44:04-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:44:04-debug: [Build Memory track]: 填充脚本数据到 settings.json start:208.66MB, end 208.68MB, increase: 26.72KB
2025-8-8 19:44:04-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 19:44:04-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (8ms)
2025-8-8 19:44:04-log: run build task 整理部分构建选项内数据到 settings.json success in 8 ms√, progress: 15%
2025-8-8 19:44:04-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.71MB, end 208.84MB, increase: 132.80KB
2025-8-8 19:46:51-debug: refresh db internal success
2025-8-8 19:46:51-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\@api\cc.d.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:46:51-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\extend\ExtendNode.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:46:52-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:46:52-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\notice\MessageBoxNotCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:46:52-debug: refresh db assets success
2025-8-8 19:46:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:46:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:46:52-debug: asset-db:refresh-all-database (210ms)
2025-8-8 19:46:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:46:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:46:53-debug: Query all assets info in project
2025-8-8 19:46:53-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 19:46:53-debug: Skip compress image, progress: 0%
2025-8-8 19:46:53-debug: Init all bundles start..., progress: 0%
2025-8-8 19:46:53-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:46:53-debug: Init bundle root assets start..., progress: 0%
2025-8-8 19:46:53-debug: Num of bundles: 3..., progress: 0%
2025-8-8 19:46:53-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 19:46:53-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 19:46:53-debug:   Number of all scripts: 122
2025-8-8 19:46:53-debug:   Number of other assets: 1548
2025-8-8 19:46:53-debug: Init bundle root assets success..., progress: 0%
2025-8-8 19:46:53-debug:   Number of all scenes: 1
2025-8-8 19:46:53-debug: // ---- build task 查询 Asset Bundle ---- (15ms)
2025-8-8 19:46:53-log: run build task 查询 Asset Bundle success in 15 ms√, progress: 5%
2025-8-8 19:46:53-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:46:53-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 19:46:53-debug: [Build Memory track]: 查询 Asset Bundle start:213.04MB, end 212.24MB, increase: -815.36KB
2025-8-8 19:46:53-debug: // ---- build task 查询 Asset Bundle ---- (6ms)
2025-8-8 19:46:53-log: run build task 查询 Asset Bundle success in 6 ms√, progress: 10%
2025-8-8 19:46:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:46:53-debug: [Build Memory track]: 查询 Asset Bundle start:212.27MB, end 212.56MB, increase: 293.56KB
2025-8-8 19:46:53-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 19:46:53-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 19:46:53-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.59MB, end 212.61MB, increase: 26.70KB
2025-8-8 19:46:53-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 19:46:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 19:46:53-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 19:46:53-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 19:46:53-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 19:46:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:46:53-debug: [Build Memory track]: 填充脚本数据到 settings.json start:212.64MB, end 212.67MB, increase: 27.36KB
2025-8-8 19:46:53-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 19:46:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (8ms)
2025-8-8 19:46:53-log: run build task 整理部分构建选项内数据到 settings.json success in 8 ms√, progress: 15%
2025-8-8 19:46:53-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.70MB, end 212.84MB, increase: 144.47KB
2025-8-8 19:49:37-debug: refresh db internal success
2025-8-8 19:49:37-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\@api\cc.d.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:49:37-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\component\ScrollViewEx.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:49:37-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\component\ScrollViewPlus.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:49:37-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\extend\ExtendNode.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:49:37-debug: refresh db assets success
2025-8-8 19:49:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:49:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:49:37-debug: asset-db:refresh-all-database (197ms)
2025-8-8 19:49:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:49:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 19:49:39-debug: Query all assets info in project
2025-8-8 19:49:39-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 19:49:39-debug: Skip compress image, progress: 0%
2025-8-8 19:49:39-debug: Init all bundles start..., progress: 0%
2025-8-8 19:49:39-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:49:39-debug: Num of bundles: 3..., progress: 0%
2025-8-8 19:49:39-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 19:49:39-debug: Init bundle root assets start..., progress: 0%
2025-8-8 19:49:39-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 19:49:39-debug:   Number of all scripts: 122
2025-8-8 19:49:39-debug: Init bundle root assets success..., progress: 0%
2025-8-8 19:49:39-debug:   Number of all scenes: 1
2025-8-8 19:49:39-debug:   Number of other assets: 1548
2025-8-8 19:49:39-debug: // ---- build task 查询 Asset Bundle ---- (14ms)
2025-8-8 19:49:39-log: run build task 查询 Asset Bundle success in 14 ms√, progress: 5%
2025-8-8 19:49:39-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:49:39-debug: [Build Memory track]: 查询 Asset Bundle start:216.69MB, end 215.44MB, increase: -1287.08KB
2025-8-8 19:49:39-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 19:49:39-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-8 19:49:39-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-8 19:49:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:49:39-debug: [Build Memory track]: 查询 Asset Bundle start:215.47MB, end 215.75MB, increase: 294.08KB
2025-8-8 19:49:39-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 19:49:39-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-8 19:49:39-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.78MB, end 215.80MB, increase: 17.84KB
2025-8-8 19:49:39-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 19:49:39-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 19:49:39-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 19:49:39-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 19:49:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:49:39-debug: [Build Memory track]: 填充脚本数据到 settings.json start:215.83MB, end 215.86MB, increase: 26.06KB
2025-8-8 19:49:39-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 19:49:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-8 19:49:39-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-8-8 19:49:39-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.88MB, end 216.01MB, increase: 132.70KB
2025-8-8 19:51:26-debug: Query all assets info in project
2025-8-8 19:51:26-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 19:51:26-debug: Skip compress image, progress: 0%
2025-8-8 19:51:26-debug: Init all bundles start..., progress: 0%
2025-8-8 19:51:26-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:51:26-debug: Num of bundles: 3..., progress: 0%
2025-8-8 19:51:26-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 19:51:26-debug: Init bundle root assets start..., progress: 0%
2025-8-8 19:51:26-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 19:51:26-debug:   Number of other assets: 1548
2025-8-8 19:51:26-debug: Init bundle root assets success..., progress: 0%
2025-8-8 19:51:26-debug:   Number of all scripts: 122
2025-8-8 19:51:26-debug:   Number of all scenes: 1
2025-8-8 19:51:26-debug: // ---- build task 查询 Asset Bundle ---- (15ms)
2025-8-8 19:51:26-log: run build task 查询 Asset Bundle success in 15 ms√, progress: 5%
2025-8-8 19:51:26-debug: [Build Memory track]: 查询 Asset Bundle start:217.52MB, end 215.37MB, increase: -2203.44KB
2025-8-8 19:51:26-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 19:51:26-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:51:26-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-8 19:51:26-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-8-8 19:51:26-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 19:51:26-debug: [Build Memory track]: 查询 Asset Bundle start:215.40MB, end 214.81MB, increase: -603.38KB
2025-8-8 19:51:26-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:51:26-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 19:51:26-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 19:51:26-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 19:51:26-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.84MB, end 214.87MB, increase: 27.30KB
2025-8-8 19:51:26-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 19:51:26-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 19:51:26-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 19:51:26-debug: [Build Memory track]: 填充脚本数据到 settings.json start:214.89MB, end 214.92MB, increase: 26.15KB
2025-8-8 19:51:26-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 19:51:26-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:51:26-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-8 19:51:26-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-8-8 19:51:26-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.95MB, end 215.09MB, increase: 141.80KB
2025-8-8 19:51:28-debug: refresh db internal success
2025-8-8 19:51:28-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\@api\cc.d.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:51:28-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\component\ScrollViewEx.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:51:28-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\component\ScrollViewPlus.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:51:28-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\extend\ExtendNode.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 19:51:28-debug: refresh db assets success
2025-8-8 19:51:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:51:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:51:28-debug: asset-db:refresh-all-database (318ms)
2025-8-8 19:51:28-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-8 19:51:28-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-8 19:51:30-debug: Query all assets info in project
2025-8-8 19:51:30-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 19:51:30-debug: Skip compress image, progress: 0%
2025-8-8 19:51:30-debug: Init all bundles start..., progress: 0%
2025-8-8 19:51:30-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:51:30-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 19:51:30-debug: Init bundle root assets start..., progress: 0%
2025-8-8 19:51:30-debug: Num of bundles: 3..., progress: 0%
2025-8-8 19:51:30-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 19:51:30-debug:   Number of all scenes: 1
2025-8-8 19:51:30-debug:   Number of all scripts: 122
2025-8-8 19:51:30-debug:   Number of other assets: 1548
2025-8-8 19:51:30-debug: Init bundle root assets success..., progress: 0%
2025-8-8 19:51:30-debug: // ---- build task 查询 Asset Bundle ---- (11ms)
2025-8-8 19:51:30-log: run build task 查询 Asset Bundle success in 11 ms√, progress: 5%
2025-8-8 19:51:30-debug: [Build Memory track]: 查询 Asset Bundle start:200.27MB, end 202.79MB, increase: 2.53MB
2025-8-8 19:51:30-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 19:51:30-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 19:51:30-debug: // ---- build task 查询 Asset Bundle ---- (8ms)
2025-8-8 19:51:30-log: run build task 查询 Asset Bundle success in 8 ms√, progress: 10%
2025-8-8 19:51:30-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 19:51:30-debug: [Build Memory track]: 查询 Asset Bundle start:202.82MB, end 200.00MB, increase: -2894.06KB
2025-8-8 19:51:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:51:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 19:51:30-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 19:51:30-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 19:51:30-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:200.02MB, end 200.05MB, increase: 26.71KB
2025-8-8 19:51:30-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 19:51:30-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 19:51:30-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 19:51:30-debug: [Build Memory track]: 填充脚本数据到 settings.json start:200.08MB, end 200.11MB, increase: 27.51KB
2025-8-8 19:51:30-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 19:51:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 19:51:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (9ms)
2025-8-8 19:51:30-log: run build task 整理部分构建选项内数据到 settings.json success in 9 ms√, progress: 15%
2025-8-8 19:51:30-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:200.14MB, end 200.27MB, increase: 139.51KB
2025-8-8 19:55:14-debug: refresh db internal success
2025-8-8 19:55:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 19:55:14-debug: refresh db assets success
2025-8-8 19:55:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 19:55:14-debug: asset-db:refresh-all-database (190ms)
2025-8-8 19:55:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 19:55:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:01:33-debug: refresh db internal success
2025-8-8 20:01:33-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:01:33-debug: refresh db assets success
2025-8-8 20:01:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:01:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:01:33-debug: asset-db:refresh-all-database (183ms)
2025-8-8 20:01:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:01:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:01:34-debug: Query all assets info in project
2025-8-8 20:01:34-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:01:34-debug: Skip compress image, progress: 0%
2025-8-8 20:01:34-debug: Init all bundles start..., progress: 0%
2025-8-8 20:01:34-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:01:34-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:01:34-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:01:34-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:01:34-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:01:34-debug:   Number of all scripts: 122
2025-8-8 20:01:34-debug:   Number of other assets: 1548
2025-8-8 20:01:34-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:01:34-debug:   Number of all scenes: 1
2025-8-8 20:01:34-debug: // ---- build task 查询 Asset Bundle ---- (14ms)
2025-8-8 20:01:34-debug: [Build Memory track]: 查询 Asset Bundle start:207.79MB, end 205.60MB, increase: -2238.92KB
2025-8-8 20:01:34-log: run build task 查询 Asset Bundle success in 14 ms√, progress: 5%
2025-8-8 20:01:34-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:01:34-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:01:34-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-8 20:01:34-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-8-8 20:01:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:01:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:01:34-debug: [Build Memory track]: 查询 Asset Bundle start:205.63MB, end 205.97MB, increase: 343.93KB
2025-8-8 20:01:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 20:01:34-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 20:01:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.00MB, end 206.02MB, increase: 27.20KB
2025-8-8 20:01:34-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:01:34-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:01:34-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-8 20:01:34-debug: [Build Memory track]: 填充脚本数据到 settings.json start:206.05MB, end 206.08MB, increase: 26.86KB
2025-8-8 20:01:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:01:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:01:34-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-8 20:01:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-8-8 20:01:34-log: run build task 整理部分构建选项内数据到 settings.json success in 5 ms√, progress: 15%
2025-8-8 20:01:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.11MB, end 206.23MB, increase: 127.75KB
2025-8-8 20:03:45-debug: refresh db internal success
2025-8-8 20:03:46-debug: refresh db assets success
2025-8-8 20:03:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:03:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:03:46-debug: asset-db:refresh-all-database (178ms)
2025-8-8 20:03:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:03:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:04:00-debug: refresh db internal success
2025-8-8 20:04:00-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:04:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:04:00-debug: refresh db assets success
2025-8-8 20:04:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:04:00-debug: asset-db:refresh-all-database (179ms)
2025-8-8 20:04:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:04:00-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:04:01-debug: Query all assets info in project
2025-8-8 20:04:01-debug: Skip compress image, progress: 0%
2025-8-8 20:04:01-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:04:01-debug: Init all bundles start..., progress: 0%
2025-8-8 20:04:01-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:04:01-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:04:01-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:04:01-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:04:01-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:04:01-debug:   Number of all scripts: 122
2025-8-8 20:04:01-debug:   Number of other assets: 1548
2025-8-8 20:04:01-debug:   Number of all scenes: 1
2025-8-8 20:04:01-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:04:01-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-8-8 20:04:01-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:04:01-debug: [Build Memory track]: 查询 Asset Bundle start:213.75MB, end 211.56MB, increase: -2243.90KB
2025-8-8 20:04:01-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:04:01-debug: [Build Memory track]: 查询 Asset Bundle start:211.56MB, end 211.84MB, increase: 289.82KB
2025-8-8 20:04:01-debug: // ---- build task 查询 Asset Bundle ---- (10ms)
2025-8-8 20:04:01-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:04:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:04:01-log: run build task 整理部分构建选项内数据到 settings.json success in 27 ms√, progress: 12%
2025-8-8 20:04:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (27ms)
2025-8-8 20:04:01-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.87MB, end 211.89MB, increase: 25.46KB
2025-8-8 20:04:01-log: run build task 查询 Asset Bundle success in 17 ms√, progress: 5%
2025-8-8 20:04:01-log: run build task 查询 Asset Bundle success in 10 ms√, progress: 10%
2025-8-8 20:04:01-log: run build task 填充脚本数据到 settings.json success in 4 ms√, progress: 13%
2025-8-8 20:04:01-debug: [Build Memory track]: 填充脚本数据到 settings.json start:211.92MB, end 211.95MB, increase: 23.33KB
2025-8-8 20:04:01-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:04:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:04:01-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:04:01-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:04:01-debug: // ---- build task 填充脚本数据到 settings.json ---- (4ms)
2025-8-8 20:04:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (7ms)
2025-8-8 20:04:01-log: run build task 整理部分构建选项内数据到 settings.json success in 7 ms√, progress: 15%
2025-8-8 20:04:01-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.97MB, end 212.11MB, increase: 138.45KB
2025-8-8 20:04:58-debug: refresh db internal success
2025-8-8 20:04:58-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:04:58-debug: refresh db assets success
2025-8-8 20:04:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:04:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:04:58-debug: asset-db:refresh-all-database (179ms)
2025-8-8 20:04:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:04:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:04:59-debug: Query all assets info in project
2025-8-8 20:04:59-debug: Skip compress image, progress: 0%
2025-8-8 20:04:59-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:04:59-debug: Init all bundles start..., progress: 0%
2025-8-8 20:04:59-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:04:59-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:04:59-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:04:59-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:04:59-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:04:59-debug:   Number of all scenes: 1
2025-8-8 20:04:59-debug:   Number of other assets: 1548
2025-8-8 20:04:59-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:04:59-debug:   Number of all scripts: 122
2025-8-8 20:04:59-debug: // ---- build task 查询 Asset Bundle ---- (14ms)
2025-8-8 20:04:59-log: run build task 查询 Asset Bundle success in 14 ms√, progress: 5%
2025-8-8 20:04:59-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:04:59-debug: [Build Memory track]: 查询 Asset Bundle start:215.54MB, end 215.56MB, increase: 18.93KB
2025-8-8 20:04:59-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:04:59-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-8 20:04:59-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-8 20:04:59-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:04:59-debug: [Build Memory track]: 查询 Asset Bundle start:215.59MB, end 215.87MB, increase: 294.03KB
2025-8-8 20:04:59-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:04:59-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-8 20:04:59-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-8-8 20:04:59-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.90MB, end 215.93MB, increase: 29.00KB
2025-8-8 20:04:59-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:04:59-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:04:59-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 20:04:59-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 20:04:59-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:04:59-debug: [Build Memory track]: 填充脚本数据到 settings.json start:215.96MB, end 215.99MB, increase: 26.48KB
2025-8-8 20:04:59-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:04:59-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (8ms)
2025-8-8 20:04:59-log: run build task 整理部分构建选项内数据到 settings.json success in 8 ms√, progress: 15%
2025-8-8 20:04:59-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:216.02MB, end 216.15MB, increase: 142.31KB
2025-8-8 20:05:24-debug: refresh db internal success
2025-8-8 20:05:24-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:05:24-debug: refresh db assets success
2025-8-8 20:05:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:05:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:05:24-debug: asset-db:refresh-all-database (205ms)
2025-8-8 20:05:25-debug: Query all assets info in project
2025-8-8 20:05:25-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:05:25-debug: Skip compress image, progress: 0%
2025-8-8 20:05:25-debug: Init all bundles start..., progress: 0%
2025-8-8 20:05:25-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:05:25-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:05:25-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:05:25-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:05:25-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:05:25-debug:   Number of all scenes: 1
2025-8-8 20:05:25-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:05:25-debug:   Number of other assets: 1548
2025-8-8 20:05:25-debug:   Number of all scripts: 122
2025-8-8 20:05:25-debug: // ---- build task 查询 Asset Bundle ---- (12ms)
2025-8-8 20:05:25-debug: [Build Memory track]: 查询 Asset Bundle start:218.34MB, end 218.39MB, increase: 46.74KB
2025-8-8 20:05:25-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:05:25-log: run build task 查询 Asset Bundle success in 12 ms√, progress: 5%
2025-8-8 20:05:25-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:05:25-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-8 20:05:25-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-8 20:05:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:05:25-debug: [Build Memory track]: 查询 Asset Bundle start:218.42MB, end 218.70MB, increase: 294.21KB
2025-8-8 20:05:25-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:05:25-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-8 20:05:25-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.73MB, end 218.75MB, increase: 16.94KB
2025-8-8 20:05:25-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:05:25-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:05:25-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 20:05:25-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 20:05:25-debug: [Build Memory track]: 填充脚本数据到 settings.json start:218.78MB, end 218.80MB, increase: 26.08KB
2025-8-8 20:05:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:05:25-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:05:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-8 20:05:25-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-8-8 20:05:25-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.83MB, end 218.97MB, increase: 136.64KB
2025-8-8 20:06:58-debug: refresh db internal success
2025-8-8 20:06:58-debug: refresh db assets success
2025-8-8 20:06:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:06:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:06:58-debug: asset-db:refresh-all-database (173ms)
2025-8-8 20:07:02-debug: refresh db internal success
2025-8-8 20:07:02-debug: refresh db assets success
2025-8-8 20:07:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:07:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:07:02-debug: asset-db:refresh-all-database (202ms)
2025-8-8 20:07:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:07:02-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:07:05-debug: refresh db internal success
2025-8-8 20:07:05-debug: refresh db assets success
2025-8-8 20:07:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:07:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:07:05-debug: asset-db:refresh-all-database (171ms)
2025-8-8 20:07:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:07:05-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:07:07-debug: refresh db internal success
2025-8-8 20:07:07-debug: refresh db assets success
2025-8-8 20:07:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:07:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:07:07-debug: asset-db:refresh-all-database (178ms)
2025-8-8 20:07:10-debug: refresh db internal success
2025-8-8 20:07:10-debug: refresh db assets success
2025-8-8 20:07:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:07:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:07:10-debug: asset-db:refresh-all-database (168ms)
2025-8-8 20:07:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:07:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:07:14-debug: refresh db internal success
2025-8-8 20:07:14-debug: refresh db assets success
2025-8-8 20:07:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:07:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:07:14-debug: asset-db:refresh-all-database (163ms)
2025-8-8 20:07:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:07:17-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\MapPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:07:17-debug: asset-db:reimport-asset9033f3b5-d829-434f-a077-9a690c5917a4 (12ms)
2025-8-8 20:07:47-debug: refresh db internal success
2025-8-8 20:07:48-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:07:48-debug: refresh db assets success
2025-8-8 20:07:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:07:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:07:48-debug: asset-db:refresh-all-database (181ms)
2025-8-8 20:07:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:07:48-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:07:49-debug: Query all assets info in project
2025-8-8 20:07:49-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:07:49-debug: Skip compress image, progress: 0%
2025-8-8 20:07:49-debug: Init all bundles start..., progress: 0%
2025-8-8 20:07:49-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:07:49-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:07:49-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:07:49-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:07:49-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:07:49-debug:   Number of all scripts: 122
2025-8-8 20:07:49-debug:   Number of other assets: 1548
2025-8-8 20:07:49-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-8-8 20:07:49-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:07:49-log: run build task 查询 Asset Bundle success in 16 ms√, progress: 5%
2025-8-8 20:07:49-debug: [Build Memory track]: 查询 Asset Bundle start:208.97MB, end 206.80MB, increase: -2223.19KB
2025-8-8 20:07:49-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:07:49-debug:   Number of all scenes: 1
2025-8-8 20:07:49-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:07:49-debug: // ---- build task 查询 Asset Bundle ---- (6ms)
2025-8-8 20:07:49-log: run build task 查询 Asset Bundle success in 6 ms√, progress: 10%
2025-8-8 20:07:49-debug: [Build Memory track]: 查询 Asset Bundle start:206.83MB, end 207.12MB, increase: 296.18KB
2025-8-8 20:07:49-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:07:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:07:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 20:07:49-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 20:07:49-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.15MB, end 207.17MB, increase: 25.79KB
2025-8-8 20:07:49-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:07:49-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:07:49-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-8 20:07:49-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-8 20:07:49-debug: [Build Memory track]: 填充脚本数据到 settings.json start:207.20MB, end 207.23MB, increase: 25.16KB
2025-8-8 20:07:49-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:07:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:07:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-8 20:07:49-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-8-8 20:07:49-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.26MB, end 207.39MB, increase: 133.25KB
2025-8-8 20:09:09-debug: refresh db internal success
2025-8-8 20:09:09-debug: refresh db assets success
2025-8-8 20:09:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:09:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:09:09-debug: asset-db:refresh-all-database (190ms)
2025-8-8 20:09:09-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-8 20:09:09-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:10:24-debug: refresh db internal success
2025-8-8 20:10:25-debug: refresh db assets success
2025-8-8 20:10:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:10:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:10:25-debug: asset-db:refresh-all-database (204ms)
2025-8-8 20:10:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:10:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:13:35-debug: Query all assets info in project
2025-8-8 20:13:35-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:13:35-debug: Skip compress image, progress: 0%
2025-8-8 20:13:35-debug: Init all bundles start..., progress: 0%
2025-8-8 20:13:35-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:13:35-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:13:35-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:13:35-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:13:35-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:13:35-debug:   Number of all scenes: 1
2025-8-8 20:13:35-debug:   Number of all scripts: 122
2025-8-8 20:13:35-debug:   Number of other assets: 1548
2025-8-8 20:13:35-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:13:35-debug: // ---- build task 查询 Asset Bundle ---- (14ms)
2025-8-8 20:13:35-log: run build task 查询 Asset Bundle success in 14 ms√, progress: 5%
2025-8-8 20:13:35-debug: [Build Memory track]: 查询 Asset Bundle start:213.18MB, end 212.02MB, increase: -1191.09KB
2025-8-8 20:13:35-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:13:35-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:13:35-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-8 20:13:35-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-8-8 20:13:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:13:35-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:13:35-debug: [Build Memory track]: 查询 Asset Bundle start:212.05MB, end 212.33MB, increase: 292.63KB
2025-8-8 20:13:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 20:13:35-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 20:13:35-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.36MB, end 212.39MB, increase: 26.98KB
2025-8-8 20:13:35-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:13:35-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:13:35-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 20:13:35-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 20:13:35-debug: [Build Memory track]: 填充脚本数据到 settings.json start:212.42MB, end 212.44MB, increase: 26.39KB
2025-8-8 20:13:35-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:13:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:13:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-8-8 20:13:35-log: run build task 整理部分构建选项内数据到 settings.json success in 5 ms√, progress: 15%
2025-8-8 20:13:35-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.47MB, end 212.60MB, increase: 127.06KB
2025-8-8 20:14:33-debug: refresh db internal success
2025-8-8 20:14:33-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\manage\ViewCtrlMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:14:33-debug: refresh db assets success
2025-8-8 20:14:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:14:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:14:33-debug: asset-db:refresh-all-database (195ms)
2025-8-8 20:14:33-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-8 20:14:33-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:14:34-debug: Query all assets info in project
2025-8-8 20:14:34-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:14:34-debug: Skip compress image, progress: 0%
2025-8-8 20:14:34-debug: Init all bundles start..., progress: 0%
2025-8-8 20:14:34-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:14:34-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:14:34-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:14:34-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:14:34-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:14:34-debug:   Number of all scenes: 1
2025-8-8 20:14:34-debug:   Number of all scripts: 122
2025-8-8 20:14:34-debug:   Number of other assets: 1548
2025-8-8 20:14:34-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:14:34-debug: [Build Memory track]: 查询 Asset Bundle start:216.40MB, end 216.38MB, increase: -23.66KB
2025-8-8 20:14:34-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-8-8 20:14:34-log: run build task 查询 Asset Bundle success in 17 ms√, progress: 5%
2025-8-8 20:14:34-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:14:34-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:14:34-debug: // ---- build task 查询 Asset Bundle ---- (6ms)
2025-8-8 20:14:34-log: run build task 查询 Asset Bundle success in 6 ms√, progress: 10%
2025-8-8 20:14:34-debug: [Build Memory track]: 查询 Asset Bundle start:216.41MB, end 216.71MB, increase: 311.73KB
2025-8-8 20:14:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:14:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:14:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 20:14:34-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 20:14:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:216.74MB, end 216.77MB, increase: 27.09KB
2025-8-8 20:14:34-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:14:34-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-8 20:14:34-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:14:34-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-8 20:14:34-debug: [Build Memory track]: 填充脚本数据到 settings.json start:216.80MB, end 216.82MB, increase: 24.08KB
2025-8-8 20:14:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:14:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:14:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (9ms)
2025-8-8 20:14:34-log: run build task 整理部分构建选项内数据到 settings.json success in 9 ms√, progress: 15%
2025-8-8 20:14:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:216.85MB, end 216.98MB, increase: 132.97KB
2025-8-8 20:15:41-debug: refresh db internal success
2025-8-8 20:15:41-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:15:41-debug: refresh db assets success
2025-8-8 20:15:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:15:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:15:41-debug: asset-db:refresh-all-database (193ms)
2025-8-8 20:15:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:15:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:15:42-debug: Query all assets info in project
2025-8-8 20:15:42-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:15:42-debug: Skip compress image, progress: 0%
2025-8-8 20:15:42-debug: Init all bundles start..., progress: 0%
2025-8-8 20:15:42-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:15:42-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:15:42-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:15:42-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:15:42-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:15:42-debug:   Number of all scenes: 1
2025-8-8 20:15:42-debug:   Number of all scripts: 122
2025-8-8 20:15:42-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:15:42-debug:   Number of other assets: 1548
2025-8-8 20:15:42-debug: // ---- build task 查询 Asset Bundle ---- (15ms)
2025-8-8 20:15:42-log: run build task 查询 Asset Bundle success in 15 ms√, progress: 5%
2025-8-8 20:15:42-debug: [Build Memory track]: 查询 Asset Bundle start:219.15MB, end 219.17MB, increase: 13.18KB
2025-8-8 20:15:42-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:15:42-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:15:42-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-8-8 20:15:42-debug: [Build Memory track]: 查询 Asset Bundle start:219.20MB, end 219.48MB, increase: 293.34KB
2025-8-8 20:15:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:15:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:15:42-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-8-8 20:15:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-8-8 20:15:42-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 12%
2025-8-8 20:15:42-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.51MB, end 219.54MB, increase: 26.93KB
2025-8-8 20:15:42-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:15:42-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:15:42-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-8 20:15:42-debug: [Build Memory track]: 填充脚本数据到 settings.json start:219.57MB, end 219.58MB, increase: 16.87KB
2025-8-8 20:15:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:15:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:15:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-8 20:15:42-log: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 15%
2025-8-8 20:15:42-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.61MB, end 219.75MB, increase: 138.96KB
2025-8-8 20:16:03-debug: refresh db internal success
2025-8-8 20:16:03-debug: refresh db assets success
2025-8-8 20:16:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:16:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:16:03-debug: asset-db:refresh-all-database (176ms)
2025-8-8 20:16:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:16:03-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:16:06-debug: refresh db internal success
2025-8-8 20:16:06-debug: refresh db assets success
2025-8-8 20:16:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:16:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:16:06-debug: asset-db:refresh-all-database (177ms)
2025-8-8 20:16:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:16:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:16:34-debug: refresh db internal success
2025-8-8 20:16:34-debug: refresh db assets success
2025-8-8 20:16:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:16:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:16:34-debug: asset-db:refresh-all-database (174ms)
2025-8-8 20:16:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:16:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:16:40-debug: refresh db internal success
2025-8-8 20:16:40-debug: refresh db assets success
2025-8-8 20:16:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:16:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:16:40-debug: asset-db:refresh-all-database (209ms)
2025-8-8 20:16:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:16:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:17:43-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\MapPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:17:43-debug: asset-db:reimport-asset9033f3b5-d829-434f-a077-9a690c5917a4 (15ms)
2025-8-8 20:17:46-debug: Query all assets info in project
2025-8-8 20:17:46-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:17:46-debug: Skip compress image, progress: 0%
2025-8-8 20:17:46-debug: Init all bundles start..., progress: 0%
2025-8-8 20:17:46-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:17:46-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:17:46-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:17:46-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:17:46-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:17:46-debug:   Number of all scenes: 1
2025-8-8 20:17:46-debug:   Number of all scripts: 122
2025-8-8 20:17:46-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:17:46-debug:   Number of other assets: 1548
2025-8-8 20:17:46-log: run build task 查询 Asset Bundle success in 18 ms√, progress: 5%
2025-8-8 20:17:46-debug: // ---- build task 查询 Asset Bundle ---- (18ms)
2025-8-8 20:17:46-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:17:46-debug: [Build Memory track]: 查询 Asset Bundle start:203.68MB, end 202.22MB, increase: -1487.94KB
2025-8-8 20:17:46-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:17:46-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-8 20:17:46-debug: [Build Memory track]: 查询 Asset Bundle start:202.25MB, end 201.63MB, increase: -641.18KB
2025-8-8 20:17:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:17:46-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-8 20:17:46-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:17:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 20:17:46-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 20:17:46-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:201.66MB, end 201.68MB, increase: 26.89KB
2025-8-8 20:17:46-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:17:46-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:17:46-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 20:17:46-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 20:17:46-debug: [Build Memory track]: 填充脚本数据到 settings.json start:201.71MB, end 201.74MB, increase: 26.52KB
2025-8-8 20:17:46-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:17:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:17:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-8 20:17:46-log: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 15%
2025-8-8 20:17:46-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:201.77MB, end 201.90MB, increase: 133.50KB
2025-8-8 20:18:38-debug: refresh db internal success
2025-8-8 20:18:38-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:18:38-debug: refresh db assets success
2025-8-8 20:18:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:18:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:18:38-debug: asset-db:refresh-all-database (225ms)
2025-8-8 20:18:38-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-8 20:18:38-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:18:39-debug: Query all assets info in project
2025-8-8 20:18:39-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:18:39-debug: Skip compress image, progress: 0%
2025-8-8 20:18:39-debug: Init all bundles start..., progress: 0%
2025-8-8 20:18:39-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:18:39-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:18:39-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:18:39-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:18:39-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:18:39-debug:   Number of other assets: 1548
2025-8-8 20:18:39-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:18:39-debug: // ---- build task 查询 Asset Bundle ---- (15ms)
2025-8-8 20:18:39-log: run build task 查询 Asset Bundle success in 15 ms√, progress: 5%
2025-8-8 20:18:39-debug: [Build Memory track]: 查询 Asset Bundle start:207.51MB, end 205.31MB, increase: -2247.41KB
2025-8-8 20:18:39-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:18:39-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:18:39-debug:   Number of all scenes: 1
2025-8-8 20:18:39-debug:   Number of all scripts: 122
2025-8-8 20:18:39-debug: // ---- build task 查询 Asset Bundle ---- (5ms)
2025-8-8 20:18:39-debug: [Build Memory track]: 查询 Asset Bundle start:205.34MB, end 205.63MB, increase: 296.06KB
2025-8-8 20:18:39-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:18:39-log: run build task 查询 Asset Bundle success in 5 ms√, progress: 10%
2025-8-8 20:18:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:18:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-8 20:18:39-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-8-8 20:18:39-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.66MB, end 205.69MB, increase: 27.02KB
2025-8-8 20:18:39-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:18:39-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:18:39-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-8 20:18:39-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-8 20:18:39-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:18:39-debug: [Build Memory track]: 填充脚本数据到 settings.json start:205.71MB, end 205.74MB, increase: 25.99KB
2025-8-8 20:18:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:18:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-8-8 20:18:39-log: run build task 整理部分构建选项内数据到 settings.json success in 5 ms√, progress: 15%
2025-8-8 20:18:39-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.77MB, end 205.91MB, increase: 140.97KB
2025-8-8 20:19:07-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\MapPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:19:07-debug: asset-db:reimport-asset9033f3b5-d829-434f-a077-9a690c5917a4 (15ms)
2025-8-8 20:19:09-debug: Query all assets info in project
2025-8-8 20:19:09-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:19:09-debug: Skip compress image, progress: 0%
2025-8-8 20:19:09-debug: Init all bundles start..., progress: 0%
2025-8-8 20:19:09-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:19:09-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:19:09-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:19:09-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:19:09-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:19:09-debug:   Number of all scripts: 122
2025-8-8 20:19:09-debug:   Number of other assets: 1548
2025-8-8 20:19:09-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:19:09-debug:   Number of all scenes: 1
2025-8-8 20:19:09-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-8-8 20:19:09-log: run build task 查询 Asset Bundle success in 26 ms√, progress: 5%
2025-8-8 20:19:09-debug: [Build Memory track]: 查询 Asset Bundle start:205.61MB, end 205.62MB, increase: 13.69KB
2025-8-8 20:19:09-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:19:09-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:19:09-debug: // ---- build task 查询 Asset Bundle ---- (7ms)
2025-8-8 20:19:09-log: run build task 查询 Asset Bundle success in 7 ms√, progress: 10%
2025-8-8 20:19:09-debug: [Build Memory track]: 查询 Asset Bundle start:205.65MB, end 205.94MB, increase: 292.84KB
2025-8-8 20:19:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:19:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:19:09-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 20:19:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.97MB, end 205.99MB, increase: 25.75KB
2025-8-8 20:19:09-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:19:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 20:19:09-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:19:09-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-8 20:19:09-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-8 20:19:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:19:09-debug: [Build Memory track]: 填充脚本数据到 settings.json start:206.03MB, end 206.06MB, increase: 28.27KB
2025-8-8 20:19:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:19:09-log: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 15%
2025-8-8 20:19:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.09MB, end 206.21MB, increase: 128.22KB
2025-8-8 20:19:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-8 20:20:03-debug: refresh db internal success
2025-8-8 20:20:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:20:03-debug: refresh db assets success
2025-8-8 20:20:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:20:03-debug: asset-db:refresh-all-database (193ms)
2025-8-8 20:20:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:20:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:21:20-debug: refresh db internal success
2025-8-8 20:21:20-debug: refresh db assets success
2025-8-8 20:21:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:21:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:21:20-debug: asset-db:refresh-all-database (189ms)
2025-8-8 20:21:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:21:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:21:31-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\MapPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:21:31-debug: asset-db:reimport-asset9033f3b5-d829-434f-a077-9a690c5917a4 (13ms)
2025-8-8 20:21:39-debug: refresh db internal success
2025-8-8 20:21:39-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:21:39-debug: refresh db assets success
2025-8-8 20:21:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:21:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:21:39-debug: asset-db:refresh-all-database (238ms)
2025-8-8 20:21:39-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-8 20:21:39-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:21:40-debug: Query all assets info in project
2025-8-8 20:21:40-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:21:40-debug: Skip compress image, progress: 0%
2025-8-8 20:21:40-debug: Init all bundles start..., progress: 0%
2025-8-8 20:21:40-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:21:40-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:21:40-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:21:40-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:21:40-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:21:40-debug:   Number of all scripts: 122
2025-8-8 20:21:40-debug:   Number of other assets: 1548
2025-8-8 20:21:40-debug:   Number of all scenes: 1
2025-8-8 20:21:40-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:21:40-debug: // ---- build task 查询 Asset Bundle ---- (24ms)
2025-8-8 20:21:40-log: run build task 查询 Asset Bundle success in 24 ms√, progress: 5%
2025-8-8 20:21:40-debug: [Build Memory track]: 查询 Asset Bundle start:216.13MB, end 215.57MB, increase: -576.08KB
2025-8-8 20:21:40-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:21:40-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:21:40-debug: // ---- build task 查询 Asset Bundle ---- (9ms)
2025-8-8 20:21:40-log: run build task 查询 Asset Bundle success in 9 ms√, progress: 10%
2025-8-8 20:21:40-debug: [Build Memory track]: 查询 Asset Bundle start:215.60MB, end 215.88MB, increase: 292.87KB
2025-8-8 20:21:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:21:40-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:21:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-8 20:21:40-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 12%
2025-8-8 20:21:40-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:21:40-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.91MB, end 215.94MB, increase: 27.02KB
2025-8-8 20:21:40-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:21:40-debug: // ---- build task 填充脚本数据到 settings.json ---- (3ms)
2025-8-8 20:21:40-log: run build task 填充脚本数据到 settings.json success in 3 ms√, progress: 13%
2025-8-8 20:21:40-debug: [Build Memory track]: 填充脚本数据到 settings.json start:215.97MB, end 216.00MB, increase: 26.46KB
2025-8-8 20:21:40-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:21:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:21:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (12ms)
2025-8-8 20:21:40-log: run build task 整理部分构建选项内数据到 settings.json success in 12 ms√, progress: 15%
2025-8-8 20:21:40-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:216.02MB, end 216.15MB, increase: 127.59KB
2025-8-8 20:21:46-debug: refresh db internal success
2025-8-8 20:21:46-debug: refresh db assets success
2025-8-8 20:21:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:21:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:21:46-debug: asset-db:refresh-all-database (221ms)
2025-8-8 20:21:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:21:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:21:55-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\MapPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:21:55-debug: asset-db:reimport-asset9033f3b5-d829-434f-a077-9a690c5917a4 (18ms)
2025-8-8 20:22:40-debug: refresh db internal success
2025-8-8 20:22:40-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:22:40-debug: refresh db assets success
2025-8-8 20:22:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:22:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:22:40-debug: asset-db:refresh-all-database (217ms)
2025-8-8 20:22:40-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-8 20:22:40-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:22:41-debug: Query all assets info in project
2025-8-8 20:22:41-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:22:41-debug: Skip compress image, progress: 0%
2025-8-8 20:22:41-debug: Init all bundles start..., progress: 0%
2025-8-8 20:22:41-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:22:41-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:22:41-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:22:41-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:22:41-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:22:41-debug:   Number of all scenes: 1
2025-8-8 20:22:41-debug:   Number of all scripts: 122
2025-8-8 20:22:41-debug:   Number of other assets: 1548
2025-8-8 20:22:41-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:22:41-debug: [Build Memory track]: 查询 Asset Bundle start:203.13MB, end 203.21MB, increase: 79.90KB
2025-8-8 20:22:41-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-8-8 20:22:41-log: run build task 查询 Asset Bundle success in 16 ms√, progress: 5%
2025-8-8 20:22:41-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:22:41-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:22:41-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-8 20:22:41-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-8 20:22:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:22:41-debug: [Build Memory track]: 查询 Asset Bundle start:203.23MB, end 203.52MB, increase: 295.07KB
2025-8-8 20:22:41-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:22:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 20:22:41-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 20:22:41-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:22:41-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:203.55MB, end 203.58MB, increase: 25.64KB
2025-8-8 20:22:41-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:22:41-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 20:22:41-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 20:22:41-debug: [Build Memory track]: 填充脚本数据到 settings.json start:203.61MB, end 203.63MB, increase: 26.89KB
2025-8-8 20:22:41-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:22:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:22:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (14ms)
2025-8-8 20:22:41-log: run build task 整理部分构建选项内数据到 settings.json success in 14 ms√, progress: 15%
2025-8-8 20:22:41-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:203.66MB, end 203.79MB, increase: 131.98KB
2025-8-8 20:23:36-debug: refresh db internal success
2025-8-8 20:23:36-debug: refresh db assets success
2025-8-8 20:23:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:23:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:23:36-debug: asset-db:refresh-all-database (180ms)
2025-8-8 20:23:36-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-8 20:23:36-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:24:33-debug: refresh db internal success
2025-8-8 20:24:33-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\MapPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:24:33-debug: refresh db assets success
2025-8-8 20:24:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:24:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:24:33-debug: asset-db:refresh-all-database (186ms)
2025-8-8 20:24:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:24:33-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:24:34-debug: Query all assets info in project
2025-8-8 20:24:34-debug: Skip compress image, progress: 0%
2025-8-8 20:24:34-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:24:34-debug: Init all bundles start..., progress: 0%
2025-8-8 20:24:34-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:24:34-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:24:34-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:24:34-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:24:34-debug:   Number of other assets: 1548
2025-8-8 20:24:34-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:24:34-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:24:34-debug:   Number of all scripts: 122
2025-8-8 20:24:34-debug:   Number of all scenes: 1
2025-8-8 20:24:34-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-8-8 20:24:34-log: run build task 查询 Asset Bundle success in 16 ms√, progress: 5%
2025-8-8 20:24:34-debug: [Build Memory track]: 查询 Asset Bundle start:208.73MB, end 208.77MB, increase: 35.81KB
2025-8-8 20:24:34-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:24:34-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:24:34-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-8 20:24:34-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-8 20:24:34-debug: [Build Memory track]: 查询 Asset Bundle start:208.81MB, end 209.10MB, increase: 294.70KB
2025-8-8 20:24:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:24:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:24:34-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-8 20:24:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.13MB, end 209.15MB, increase: 17.20KB
2025-8-8 20:24:34-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:24:34-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:24:34-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-8 20:24:34-debug: [Build Memory track]: 填充脚本数据到 settings.json start:209.17MB, end 209.19MB, increase: 16.48KB
2025-8-8 20:24:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:24:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:24:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-8 20:24:34-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-8-8 20:24:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.22MB, end 209.34MB, increase: 126.44KB
2025-8-8 20:25:01-debug: refresh db internal success
2025-8-8 20:25:01-debug: refresh db assets success
2025-8-8 20:25:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:25:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:25:01-debug: asset-db:refresh-all-database (192ms)
2025-8-8 20:25:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:25:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:25:52-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\MapPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:25:52-debug: asset-db:reimport-asset9033f3b5-d829-434f-a077-9a690c5917a4 (13ms)
2025-8-8 20:27:30-debug: refresh db internal success
2025-8-8 20:27:30-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\component\ScrollViewEx.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:27:30-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\extend\ExtendScrollView.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:27:30-debug: refresh db assets success
2025-8-8 20:27:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:27:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:27:30-debug: asset-db:refresh-all-database (207ms)
2025-8-8 20:27:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:27:32-debug: Query all assets info in project
2025-8-8 20:27:32-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:27:32-debug: Skip compress image, progress: 0%
2025-8-8 20:27:32-debug: Init all bundles start..., progress: 0%
2025-8-8 20:27:32-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:27:32-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:27:32-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:27:32-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:27:32-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:27:32-debug:   Number of all scenes: 1
2025-8-8 20:27:32-debug:   Number of other assets: 1548
2025-8-8 20:27:32-debug:   Number of all scripts: 122
2025-8-8 20:27:32-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:27:32-debug: // ---- build task 查询 Asset Bundle ---- (9ms)
2025-8-8 20:27:32-log: run build task 查询 Asset Bundle success in 9 ms√, progress: 5%
2025-8-8 20:27:32-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:27:32-debug: [Build Memory track]: 查询 Asset Bundle start:214.99MB, end 214.99MB, increase: 4.58KB
2025-8-8 20:27:32-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:27:32-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-8 20:27:32-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-8-8 20:27:32-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:27:32-debug: [Build Memory track]: 查询 Asset Bundle start:215.02MB, end 215.31MB, increase: 293.67KB
2025-8-8 20:27:32-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:27:32-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-8 20:27:32-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.34MB, end 215.35MB, increase: 16.79KB
2025-8-8 20:27:32-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:27:32-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:27:32-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-8 20:27:32-debug: [Build Memory track]: 填充脚本数据到 settings.json start:215.38MB, end 215.40MB, increase: 16.65KB
2025-8-8 20:27:32-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:27:32-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:27:32-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 20:27:32-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-8-8 20:27:32-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.43MB, end 215.56MB, increase: 132.43KB
2025-8-8 20:28:04-debug: refresh db internal success
2025-8-8 20:28:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:28:04-debug: refresh db assets success
2025-8-8 20:28:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:28:04-debug: asset-db:refresh-all-database (237ms)
2025-8-8 20:28:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:28:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:30:03-debug: refresh db internal success
2025-8-8 20:30:03-debug: refresh db assets success
2025-8-8 20:30:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:30:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:30:03-debug: asset-db:refresh-all-database (201ms)
2025-8-8 20:30:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:30:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:30:32-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\view\game\Test.prefab...
2025-8-8 20:30:32-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\Test.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:30:32-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\view\game success
2025-8-8 20:30:32-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:30:35-debug: refresh db internal success
2025-8-8 20:30:35-debug: refresh db assets success
2025-8-8 20:30:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:30:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:30:35-debug: asset-db:refresh-all-database (206ms)
2025-8-8 20:30:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:30:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:30:38-debug: refresh db internal success
2025-8-8 20:30:38-debug: refresh db assets success
2025-8-8 20:30:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:30:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:30:38-debug: asset-db:refresh-all-database (194ms)
2025-8-8 20:30:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:30:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:31:05-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\Test.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:31:05-debug: asset-db:reimport-asset940e65eb-8fbb-46b4-9bf3-b998d881a171 (11ms)
2025-8-8 20:31:13-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\view\game\Test.prefab -> D:\Projects\cute-animals-client\client\assets\resources\view\game\TestPn.prefab...
2025-8-8 20:31:13-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\view\game\TestPn.prefab...
2025-8-8 20:31:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\TestPn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:31:13-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\view\game success
2025-8-8 20:31:13-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\view\game...
2025-8-8 20:31:13-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:31:13-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\view\game\Test.prefab -> D:\Projects\cute-animals-client\client\assets\resources\view\game\TestPn.prefab success
2025-8-8 20:31:13-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\view success
2025-8-8 20:31:16-debug: start move asset from D:\Projects\cute-animals-client\client\assets\resources\view\game\TestPn.prefab -> D:\Projects\cute-animals-client\client\assets\resources\view\game\TestPnl.prefab...
2025-8-8 20:31:16-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\view\game\TestPnl.prefab...
2025-8-8 20:31:16-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\TestPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:31:16-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\view\game success
2025-8-8 20:31:16-debug: start refresh asset from D:\Projects\cute-animals-client\client\assets\resources\view\game...
2025-8-8 20:31:16-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:31:16-debug: refresh asset D:\Projects\cute-animals-client\client\assets\resources\view success
2025-8-8 20:31:16-debug: move asset from D:\Projects\cute-animals-client\client\assets\resources\view\game\TestPn.prefab -> D:\Projects\cute-animals-client\client\assets\resources\view\game\TestPnl.prefab success
2025-8-8 20:31:17-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\TestPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:31:17-debug: asset-db:reimport-asset940e65eb-8fbb-46b4-9bf3-b998d881a171 (12ms)
2025-8-8 20:31:20-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\view\game\TestPnl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:31:20-debug: asset-db:reimport-asset940e65eb-8fbb-46b4-9bf3-b998d881a171 (11ms)
2025-8-8 20:31:33-debug: refresh db internal success
2025-8-8 20:31:33-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\TestPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:31:33-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:31:33-debug: refresh db assets success
2025-8-8 20:31:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:31:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:31:33-debug: asset-db:refresh-all-database (235ms)
2025-8-8 20:31:33-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-8 20:31:33-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:31:34-debug: Query all assets info in project
2025-8-8 20:31:34-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:31:34-debug: Skip compress image, progress: 0%
2025-8-8 20:31:34-debug: Init all bundles start..., progress: 0%
2025-8-8 20:31:34-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:31:34-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:31:34-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:31:34-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:31:34-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:31:34-debug:   Number of all scenes: 1
2025-8-8 20:31:34-debug:   Number of all scripts: 123
2025-8-8 20:31:34-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:31:34-debug:   Number of other assets: 1549
2025-8-8 20:31:34-debug: // ---- build task 查询 Asset Bundle ---- (18ms)
2025-8-8 20:31:34-log: run build task 查询 Asset Bundle success in 18 ms√, progress: 5%
2025-8-8 20:31:34-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:31:34-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:31:34-debug: [Build Memory track]: 查询 Asset Bundle start:210.69MB, end 210.75MB, increase: 65.19KB
2025-8-8 20:31:34-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-8 20:31:34-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-8 20:31:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:31:34-debug: [Build Memory track]: 查询 Asset Bundle start:210.78MB, end 211.07MB, increase: 293.40KB
2025-8-8 20:31:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:31:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-8 20:31:34-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-8-8 20:31:34-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:31:34-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:31:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.10MB, end 211.13MB, increase: 26.93KB
2025-8-8 20:31:34-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-8 20:31:34-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-8 20:31:34-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:31:34-debug: [Build Memory track]: 填充脚本数据到 settings.json start:211.15MB, end 211.18MB, increase: 27.45KB
2025-8-8 20:31:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:31:34-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (7ms)
2025-8-8 20:31:34-log: run build task 整理部分构建选项内数据到 settings.json success in 7 ms√, progress: 15%
2025-8-8 20:31:34-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.21MB, end 211.34MB, increase: 134.02KB
2025-8-8 20:31:51-debug: refresh db internal success
2025-8-8 20:31:51-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\GameWindCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:31:51-debug: refresh db assets success
2025-8-8 20:31:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:31:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:31:51-debug: asset-db:refresh-all-database (203ms)
2025-8-8 20:31:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:31:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:31:52-debug: Query all assets info in project
2025-8-8 20:31:52-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:31:52-debug: Skip compress image, progress: 0%
2025-8-8 20:31:52-debug: Init all bundles start..., progress: 0%
2025-8-8 20:31:52-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:31:52-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:31:52-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:31:52-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:31:52-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:31:52-debug:   Number of all scenes: 1
2025-8-8 20:31:52-debug:   Number of all scripts: 123
2025-8-8 20:31:52-debug:   Number of other assets: 1549
2025-8-8 20:31:52-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:31:52-debug: // ---- build task 查询 Asset Bundle ---- (19ms)
2025-8-8 20:31:52-log: run build task 查询 Asset Bundle success in 19 ms√, progress: 5%
2025-8-8 20:31:52-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:31:52-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:31:52-debug: [Build Memory track]: 查询 Asset Bundle start:213.65MB, end 213.72MB, increase: 79.91KB
2025-8-8 20:31:52-debug: // ---- build task 查询 Asset Bundle ---- (7ms)
2025-8-8 20:31:52-debug: [Build Memory track]: 查询 Asset Bundle start:213.75MB, end 214.04MB, increase: 295.97KB
2025-8-8 20:31:52-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:31:52-log: run build task 查询 Asset Bundle success in 7 ms√, progress: 10%
2025-8-8 20:31:52-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:31:52-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-8-8 20:31:52-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 12%
2025-8-8 20:31:52-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.07MB, end 214.10MB, increase: 25.09KB
2025-8-8 20:31:52-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:31:52-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:31:52-debug: [Build Memory track]: 填充脚本数据到 settings.json start:214.12MB, end 214.15MB, increase: 26.91KB
2025-8-8 20:31:52-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-8 20:31:52-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-8 20:31:52-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:31:52-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:31:52-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (9ms)
2025-8-8 20:31:52-log: run build task 整理部分构建选项内数据到 settings.json success in 9 ms√, progress: 15%
2025-8-8 20:31:52-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.18MB, end 214.30MB, increase: 128.58KB
2025-8-8 20:33:04-debug: refresh db internal success
2025-8-8 20:33:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:33:04-debug: refresh db assets success
2025-8-8 20:33:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:33:04-debug: asset-db:refresh-all-database (179ms)
2025-8-8 20:33:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:33:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:33:28-debug: refresh db internal success
2025-8-8 20:33:28-debug: refresh db assets success
2025-8-8 20:33:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:33:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:33:28-debug: asset-db:refresh-all-database (185ms)
2025-8-8 20:33:28-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-8 20:33:28-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-8 20:33:42-debug: Query all assets info in project
2025-8-8 20:33:42-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:33:42-debug: Skip compress image, progress: 0%
2025-8-8 20:33:42-debug: Init all bundles start..., progress: 0%
2025-8-8 20:33:42-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:33:42-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:33:42-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:33:42-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:33:42-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:33:42-debug:   Number of all scenes: 1
2025-8-8 20:33:42-debug:   Number of all scripts: 123
2025-8-8 20:33:42-debug:   Number of other assets: 1549
2025-8-8 20:33:42-debug: // ---- build task 查询 Asset Bundle ---- (14ms)
2025-8-8 20:33:42-log: run build task 查询 Asset Bundle success in 14 ms√, progress: 5%
2025-8-8 20:33:42-debug: [Build Memory track]: 查询 Asset Bundle start:217.43MB, end 216.49MB, increase: -968.08KB
2025-8-8 20:33:42-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:33:42-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:33:42-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:33:42-debug: // ---- build task 查询 Asset Bundle ---- (5ms)
2025-8-8 20:33:42-log: run build task 查询 Asset Bundle success in 5 ms√, progress: 10%
2025-8-8 20:33:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:33:42-debug: [Build Memory track]: 查询 Asset Bundle start:216.52MB, end 216.81MB, increase: 295.96KB
2025-8-8 20:33:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:33:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 20:33:42-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 20:33:42-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:33:42-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:216.84MB, end 216.86MB, increase: 26.89KB
2025-8-8 20:33:42-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:33:42-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 20:33:42-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 20:33:42-debug: [Build Memory track]: 填充脚本数据到 settings.json start:216.89MB, end 216.92MB, increase: 26.30KB
2025-8-8 20:33:42-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:33:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:33:42-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-8 20:33:42-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:216.95MB, end 217.07MB, increase: 127.09KB
2025-8-8 20:33:42-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-8-8 20:35:24-debug: Query all assets info in project
2025-8-8 20:35:24-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:35:24-debug: Skip compress image, progress: 0%
2025-8-8 20:35:24-debug: Init all bundles start..., progress: 0%
2025-8-8 20:35:24-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:35:24-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:35:24-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:35:24-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:35:24-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:35:24-debug:   Number of all scenes: 1
2025-8-8 20:35:24-debug:   Number of other assets: 1549
2025-8-8 20:35:24-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:35:24-debug:   Number of all scripts: 123
2025-8-8 20:35:24-debug: // ---- build task 查询 Asset Bundle ---- (14ms)
2025-8-8 20:35:24-log: run build task 查询 Asset Bundle success in 14 ms√, progress: 5%
2025-8-8 20:35:24-debug: [Build Memory track]: 查询 Asset Bundle start:217.06MB, end 216.88MB, increase: -187.53KB
2025-8-8 20:35:24-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:35:24-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:35:24-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-8 20:35:24-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-8 20:35:24-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:35:24-debug: [Build Memory track]: 查询 Asset Bundle start:216.91MB, end 217.19MB, increase: 293.87KB
2025-8-8 20:35:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:35:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 20:35:24-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 20:35:24-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.22MB, end 217.25MB, increase: 25.96KB
2025-8-8 20:35:24-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:35:24-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:35:24-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 20:35:24-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 20:35:24-debug: [Build Memory track]: 填充脚本数据到 settings.json start:217.28MB, end 217.30MB, increase: 27.24KB
2025-8-8 20:35:24-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:35:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:35:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-8 20:35:24-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-8-8 20:35:24-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.33MB, end 217.47MB, increase: 139.76KB
2025-8-8 20:39:36-debug: refresh db internal success
2025-8-8 20:39:36-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\manage\ViewCtrlMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:39:37-debug: refresh db assets success
2025-8-8 20:39:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:39:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:39:37-debug: asset-db:refresh-all-database (232ms)
2025-8-8 20:39:37-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-8 20:39:37-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:39:41-debug: refresh db internal success
2025-8-8 20:39:41-debug: refresh db assets success
2025-8-8 20:39:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:39:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:39:41-debug: asset-db:refresh-all-database (214ms)
2025-8-8 20:39:41-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-8 20:39:41-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-8 20:39:43-debug: refresh db internal success
2025-8-8 20:39:43-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\manage\ViewCtrlMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:39:43-debug: refresh db assets success
2025-8-8 20:39:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:39:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:39:43-debug: asset-db:refresh-all-database (214ms)
2025-8-8 20:39:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:39:43-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:39:45-debug: Query all assets info in project
2025-8-8 20:39:45-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:39:45-debug: Skip compress image, progress: 0%
2025-8-8 20:39:45-debug: Init all bundles start..., progress: 0%
2025-8-8 20:39:45-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:39:45-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:39:45-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:39:45-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:39:45-debug:   Number of all scenes: 1
2025-8-8 20:39:45-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:39:45-debug:   Number of all scripts: 123
2025-8-8 20:39:45-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:39:45-debug:   Number of other assets: 1549
2025-8-8 20:39:45-debug: // ---- build task 查询 Asset Bundle ---- (11ms)
2025-8-8 20:39:45-log: run build task 查询 Asset Bundle success in 11 ms√, progress: 5%
2025-8-8 20:39:45-debug: [Build Memory track]: 查询 Asset Bundle start:202.10MB, end 204.63MB, increase: 2.53MB
2025-8-8 20:39:45-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:39:45-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:39:45-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-8 20:39:45-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-8-8 20:39:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:39:45-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:39:45-debug: [Build Memory track]: 查询 Asset Bundle start:204.66MB, end 204.95MB, increase: 294.45KB
2025-8-8 20:39:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 20:39:45-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 20:39:45-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:39:45-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:204.98MB, end 205.01MB, increase: 26.88KB
2025-8-8 20:39:45-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:39:45-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 20:39:45-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 20:39:45-debug: [Build Memory track]: 填充脚本数据到 settings.json start:205.03MB, end 205.06MB, increase: 26.37KB
2025-8-8 20:39:45-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:39:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:39:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-8 20:39:45-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-8-8 20:39:45-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.09MB, end 205.22MB, increase: 131.25KB
2025-8-8 20:40:12-debug: refresh db internal success
2025-8-8 20:40:12-debug: refresh db assets success
2025-8-8 20:40:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:40:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:40:12-debug: asset-db:refresh-all-database (181ms)
2025-8-8 20:40:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:40:12-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:40:19-debug: refresh db internal success
2025-8-8 20:40:19-debug: refresh db assets success
2025-8-8 20:40:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:40:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:40:19-debug: asset-db:refresh-all-database (168ms)
2025-8-8 20:40:19-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-8 20:40:19-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:40:24-debug: refresh db internal success
2025-8-8 20:40:24-debug: refresh db assets success
2025-8-8 20:40:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:40:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:40:24-debug: asset-db:refresh-all-database (175ms)
2025-8-8 20:40:24-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-8 20:40:24-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:40:44-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\common\prefab\PNL_MASK.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:40:44-debug: asset-db:reimport-asset63aba7d8-dd44-4e3b-b491-4b0230ba8434 (12ms)
2025-8-8 20:42:20-debug: refresh db internal success
2025-8-8 20:42:20-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\extend\ExtendNode.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:42:20-debug: refresh db assets success
2025-8-8 20:42:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:42:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:42:20-debug: asset-db:refresh-all-database (191ms)
2025-8-8 20:42:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:42:20-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:42:21-debug: Query all assets info in project
2025-8-8 20:42:21-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:42:21-debug: Skip compress image, progress: 0%
2025-8-8 20:42:21-debug: Init all bundles start..., progress: 0%
2025-8-8 20:42:21-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:42:21-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:42:21-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:42:21-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:42:21-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:42:21-debug:   Number of all scripts: 123
2025-8-8 20:42:21-debug:   Number of other assets: 1549
2025-8-8 20:42:21-debug:   Number of all scenes: 1
2025-8-8 20:42:21-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:42:21-debug: // ---- build task 查询 Asset Bundle ---- (13ms)
2025-8-8 20:42:21-log: run build task 查询 Asset Bundle success in 13 ms√, progress: 5%
2025-8-8 20:42:21-debug: [Build Memory track]: 查询 Asset Bundle start:211.09MB, end 211.07MB, increase: -18.82KB
2025-8-8 20:42:21-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:42:21-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:42:21-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-8 20:42:21-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-8 20:42:21-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:42:21-debug: [Build Memory track]: 查询 Asset Bundle start:211.10MB, end 211.39MB, increase: 294.22KB
2025-8-8 20:42:21-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:42:21-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 20:42:21-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 20:42:21-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.42MB, end 211.44MB, increase: 26.90KB
2025-8-8 20:42:21-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:42:21-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:42:21-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 20:42:21-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 20:42:21-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:42:21-debug: [Build Memory track]: 填充脚本数据到 settings.json start:211.47MB, end 211.50MB, increase: 25.44KB
2025-8-8 20:42:21-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:42:21-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-8 20:42:21-log: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 15%
2025-8-8 20:42:21-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.53MB, end 211.67MB, increase: 145.39KB
2025-8-8 20:42:42-debug: refresh db internal success
2025-8-8 20:42:42-debug: refresh db assets success
2025-8-8 20:42:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:42:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:42:42-debug: asset-db:refresh-all-database (176ms)
2025-8-8 20:43:11-debug: refresh db internal success
2025-8-8 20:43:11-debug: refresh db assets success
2025-8-8 20:43:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:43:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:43:11-debug: asset-db:refresh-all-database (206ms)
2025-8-8 20:43:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:43:11-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:43:16-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\common\prefab\PNL_MASK.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:43:16-debug: asset-db:reimport-asset63aba7d8-dd44-4e3b-b491-4b0230ba8434 (11ms)
2025-8-8 20:44:01-debug: Query all assets info in project
2025-8-8 20:44:01-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:44:01-debug: Skip compress image, progress: 0%
2025-8-8 20:44:01-debug: Init all bundles start..., progress: 0%
2025-8-8 20:44:01-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:44:01-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:44:01-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:44:01-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:44:01-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:44:01-debug:   Number of all scripts: 123
2025-8-8 20:44:01-debug:   Number of other assets: 1549
2025-8-8 20:44:01-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:44:01-debug:   Number of all scenes: 1
2025-8-8 20:44:01-debug: // ---- build task 查询 Asset Bundle ---- (14ms)
2025-8-8 20:44:01-log: run build task 查询 Asset Bundle success in 14 ms√, progress: 5%
2025-8-8 20:44:01-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:44:01-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:44:01-debug: [Build Memory track]: 查询 Asset Bundle start:216.58MB, end 215.74MB, increase: -855.27KB
2025-8-8 20:44:01-debug: // ---- build task 查询 Asset Bundle ---- (5ms)
2025-8-8 20:44:01-log: run build task 查询 Asset Bundle success in 5 ms√, progress: 10%
2025-8-8 20:44:01-debug: [Build Memory track]: 查询 Asset Bundle start:215.77MB, end 215.21MB, increase: -576.21KB
2025-8-8 20:44:01-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:44:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:44:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-8 20:44:01-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-8-8 20:44:01-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.24MB, end 215.26MB, increase: 25.79KB
2025-8-8 20:44:01-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:44:01-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:44:01-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-8 20:44:01-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-8 20:44:01-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:44:01-debug: [Build Memory track]: 填充脚本数据到 settings.json start:215.29MB, end 215.32MB, increase: 27.45KB
2025-8-8 20:44:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:44:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-8 20:44:01-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-8-8 20:44:01-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.35MB, end 215.49MB, increase: 140.34KB
2025-8-8 20:44:03-debug: refresh db internal success
2025-8-8 20:44:03-debug: refresh db assets success
2025-8-8 20:44:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:44:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:44:03-debug: asset-db:refresh-all-database (206ms)
2025-8-8 20:44:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:44:03-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:44:04-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\resources\common\prefab\PNL_MASK.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:44:04-debug: asset-db:reimport-asset63aba7d8-dd44-4e3b-b491-4b0230ba8434 (3ms)
2025-8-8 20:44:06-debug: Query all assets info in project
2025-8-8 20:44:06-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:44:06-debug: Skip compress image, progress: 0%
2025-8-8 20:44:06-debug: Init all bundles start..., progress: 0%
2025-8-8 20:44:06-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:44:06-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:44:06-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:44:06-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:44:06-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:44:06-debug:   Number of all scripts: 123
2025-8-8 20:44:06-debug:   Number of other assets: 1549
2025-8-8 20:44:06-debug:   Number of all scenes: 1
2025-8-8 20:44:06-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:44:06-log: run build task 查询 Asset Bundle success in 17 ms√, progress: 5%
2025-8-8 20:44:06-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:44:06-debug: [Build Memory track]: 查询 Asset Bundle start:221.61MB, end 217.79MB, increase: -3913.86KB
2025-8-8 20:44:06-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-8-8 20:44:06-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:44:06-debug: // ---- build task 查询 Asset Bundle ---- (6ms)
2025-8-8 20:44:06-log: run build task 查询 Asset Bundle success in 6 ms√, progress: 10%
2025-8-8 20:44:06-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:44:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:44:06-debug: [Build Memory track]: 查询 Asset Bundle start:217.82MB, end 218.11MB, increase: 294.63KB
2025-8-8 20:44:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-8-8 20:44:06-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 12%
2025-8-8 20:44:06-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:44:06-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:44:06-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.14MB, end 218.16MB, increase: 26.83KB
2025-8-8 20:44:06-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-8 20:44:06-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-8 20:44:06-debug: [Build Memory track]: 填充脚本数据到 settings.json start:218.19MB, end 218.22MB, increase: 26.38KB
2025-8-8 20:44:06-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:44:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:44:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (8ms)
2025-8-8 20:44:06-log: run build task 整理部分构建选项内数据到 settings.json success in 8 ms√, progress: 15%
2025-8-8 20:44:06-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.48MB, end 218.38MB, increase: -101.94KB
2025-8-8 20:44:09-debug: refresh db internal success
2025-8-8 20:44:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:44:09-debug: refresh db assets success
2025-8-8 20:44:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:44:09-debug: asset-db:refresh-all-database (205ms)
2025-8-8 20:44:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:44:09-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:45:07-debug: Query all assets info in project
2025-8-8 20:45:07-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:45:07-debug: Skip compress image, progress: 0%
2025-8-8 20:45:07-debug: Init all bundles start..., progress: 0%
2025-8-8 20:45:07-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:45:07-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:45:07-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:45:07-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:45:07-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:45:07-debug:   Number of all scenes: 1
2025-8-8 20:45:07-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:45:07-log: run build task 查询 Asset Bundle success in 10 ms√, progress: 5%
2025-8-8 20:45:07-debug:   Number of other assets: 1549
2025-8-8 20:45:07-debug:   Number of all scripts: 123
2025-8-8 20:45:07-debug: [Build Memory track]: 查询 Asset Bundle start:218.59MB, end 217.74MB, increase: -867.19KB
2025-8-8 20:45:07-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:45:07-debug: // ---- build task 查询 Asset Bundle ---- (10ms)
2025-8-8 20:45:07-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:45:07-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-8 20:45:07-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-8-8 20:45:07-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:45:07-debug: [Build Memory track]: 查询 Asset Bundle start:217.77MB, end 218.06MB, increase: 295.86KB
2025-8-8 20:45:07-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:45:07-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-8 20:45:07-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.09MB, end 218.11MB, increase: 17.43KB
2025-8-8 20:45:07-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:45:07-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:45:07-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 20:45:07-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 20:45:07-debug: [Build Memory track]: 填充脚本数据到 settings.json start:218.14MB, end 218.16MB, increase: 25.74KB
2025-8-8 20:45:07-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:45:07-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:45:07-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (7ms)
2025-8-8 20:45:07-log: run build task 整理部分构建选项内数据到 settings.json success in 7 ms√, progress: 15%
2025-8-8 20:45:07-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.19MB, end 218.32MB, increase: 131.78KB
2025-8-8 20:49:26-debug: refresh db internal success
2025-8-8 20:49:26-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\core\manage\ViewCtrlMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:49:26-debug: refresh db assets success
2025-8-8 20:49:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:49:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:49:26-debug: asset-db:refresh-all-database (215ms)
2025-8-8 20:49:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:49:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-8 20:49:27-debug: Query all assets info in project
2025-8-8 20:49:27-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:49:27-debug: Skip compress image, progress: 0%
2025-8-8 20:49:27-debug: Init all bundles start..., progress: 0%
2025-8-8 20:49:27-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:49:27-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:49:27-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:49:27-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:49:27-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:49:27-debug:   Number of all scenes: 1
2025-8-8 20:49:27-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:49:27-debug: // ---- build task 查询 Asset Bundle ---- (12ms)
2025-8-8 20:49:27-log: run build task 查询 Asset Bundle success in 12 ms√, progress: 5%
2025-8-8 20:49:27-debug: [Build Memory track]: 查询 Asset Bundle start:222.28MB, end 222.25MB, increase: -24.46KB
2025-8-8 20:49:27-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:49:27-debug:   Number of all scripts: 123
2025-8-8 20:49:27-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:49:27-debug:   Number of other assets: 1549
2025-8-8 20:49:27-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-8-8 20:49:27-log: run build task 查询 Asset Bundle success in 17 ms√, progress: 10%
2025-8-8 20:49:27-debug: [Build Memory track]: 查询 Asset Bundle start:222.28MB, end 222.57MB, increase: 295.01KB
2025-8-8 20:49:27-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:49:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:49:27-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 20:49:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 20:49:27-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:49:27-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:222.60MB, end 222.63MB, increase: 26.92KB
2025-8-8 20:49:27-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:49:27-log: run build task 填充脚本数据到 settings.json success in 15 ms√, progress: 13%
2025-8-8 20:49:27-debug: [Build Memory track]: 填充脚本数据到 settings.json start:222.65MB, end 222.68MB, increase: 25.19KB
2025-8-8 20:49:27-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:49:27-debug: // ---- build task 填充脚本数据到 settings.json ---- (15ms)
2025-8-8 20:49:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:49:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (14ms)
2025-8-8 20:49:27-log: run build task 整理部分构建选项内数据到 settings.json success in 14 ms√, progress: 15%
2025-8-8 20:49:27-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:222.71MB, end 200.13MB, increase: -23116.34KB
2025-8-8 20:53:11-debug: refresh db internal success
2025-8-8 20:53:11-debug: %cImport%c: D:\Projects\cute-animals-client\client\assets\app\script\view\game\TestPnlCtrl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-8 20:53:11-debug: refresh db assets success
2025-8-8 20:53:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-8 20:53:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-8 20:53:11-debug: asset-db:refresh-all-database (263ms)
2025-8-8 20:53:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-8 20:53:11-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-8 20:53:13-debug: Query all assets info in project
2025-8-8 20:53:13-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-8 20:53:13-debug: Skip compress image, progress: 0%
2025-8-8 20:53:13-debug: Init all bundles start..., progress: 0%
2025-8-8 20:53:13-debug: Num of bundles: 3..., progress: 0%
2025-8-8 20:53:13-debug: 查询 Asset Bundle start, progress: 0%
2025-8-8 20:53:13-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:53:13-debug: Init bundle root assets start..., progress: 0%
2025-8-8 20:53:13-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-8 20:53:13-debug:   Number of all scenes: 1
2025-8-8 20:53:13-debug:   Number of all scripts: 123
2025-8-8 20:53:13-debug: Init bundle root assets success..., progress: 0%
2025-8-8 20:53:13-debug:   Number of other assets: 1549
2025-8-8 20:53:13-debug: // ---- build task 查询 Asset Bundle ---- (13ms)
2025-8-8 20:53:13-log: run build task 查询 Asset Bundle success in 13 ms√, progress: 5%
2025-8-8 20:53:13-debug: 查询 Asset Bundle start, progress: 5%
2025-8-8 20:53:13-debug: [Build Memory track]: 查询 Asset Bundle start:204.88MB, end 205.00MB, increase: 122.92KB
2025-8-8 20:53:13-debug: // ---- build task 查询 Asset Bundle ----
2025-8-8 20:53:13-debug: // ---- build task 查询 Asset Bundle ---- (7ms)
2025-8-8 20:53:13-log: run build task 查询 Asset Bundle success in 7 ms√, progress: 10%
2025-8-8 20:53:13-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:53:13-debug: [Build Memory track]: 查询 Asset Bundle start:205.03MB, end 205.31MB, increase: 293.64KB
2025-8-8 20:53:13-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-8 20:53:13-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-8 20:53:13-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-8 20:53:13-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.34MB, end 205.37MB, increase: 26.65KB
2025-8-8 20:53:13-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-8 20:53:13-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-8 20:53:13-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-8 20:53:13-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-8 20:53:13-debug: [Build Memory track]: 填充脚本数据到 settings.json start:205.40MB, end 205.42MB, increase: 26.50KB
2025-8-8 20:53:13-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-8 20:53:13-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-8 20:53:13-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-8 20:53:13-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.45MB, end 205.58MB, increase: 127.06KB
2025-8-8 20:53:13-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
