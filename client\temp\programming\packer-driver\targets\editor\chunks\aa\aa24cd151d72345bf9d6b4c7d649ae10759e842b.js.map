{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/script/common/config/AnimalFrameAnimConf.ts"], "names": ["getAnimalFrameAnimConf", "id", "conf", "ANIMAL_FRAME_ANIM_CONF", "error", "url", "anims", "name", "interval", "loop", "frameIndexs"], "mappings": ";;;;;AAEA;AACA,WAASA,sBAAT,CAAgCC,EAAhC,EAA4C;AACxC,UAAMC,IAAI,GAAGC,sBAAsB,CAACF,EAAD,CAAnC;;AACA,QAAI,CAACC,IAAL,EAAW;AACPE,MAAAA,KAAK,CAAC,uCAAuCH,EAAxC,CAAL;AACA,aAAO,IAAP;AACH,KAHD,MAGO,IAAI,CAACC,IAAI,CAACG,GAAV,EAAe;AAClBH,MAAAA,IAAI,CAACG,GAAL,GAAY,UAASJ,EAAG,WAAUA,EAAG,GAArC;AACH;;AACD,WAAOC,IAAP;AACH,G,CAED;;;oCAaIF,sB;;;;;;;AA3BKI,MAAAA,K,OAAAA,K;;;;;;;;;AAeHD,MAAAA,sB,GAAyB;AAC3B,gBAAQ;AAAE;AACNG,UAAAA,KAAK,EAAE,CACH;AAAEC,YAAAA,IAAI,EAAE,MAAR;AAAgBC,YAAAA,QAAQ,EAAE,GAA1B;AAA+BC,YAAAA,IAAI,EAAE,KAArC;AAA4CC,YAAAA,WAAW,EAAE,CAAC,IAAD;AAAzD,WADG,EAEH;AAAEH,YAAAA,IAAI,EAAE,QAAR;AAAkBC,YAAAA,QAAQ,EAAE,GAA5B;AAAiCC,YAAAA,IAAI,EAAE,KAAvC;AAA8CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB;AAA3D,WAFG,EAGH;AAAEH,YAAAA,IAAI,EAAE,KAAR;AAAeC,YAAAA,QAAQ,EAAE,GAAzB;AAA8BC,YAAAA,IAAI,EAAE,KAApC;AAA2CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb;AAAxD,WAHG,EAIH;AAAEH,YAAAA,IAAI,EAAE,KAAR;AAAeC,YAAAA,QAAQ,EAAE,GAAzB;AAA8BC,YAAAA,IAAI,EAAE,KAApC;AAA2CC,YAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB;AAAxD,WAJG;AADH;AADmB,O", "sourcesContent": ["import { error } from \"cc\"\r\n\r\n// 获取动物的帧动画配置\r\nfunction getAnimalFrameAnimConf(id: number) {\r\n    const conf = ANIMAL_FRAME_ANIM_CONF[id]\r\n    if (!conf) {\r\n        error('getAnimalFrameAnimConf error. id: ' + id)\r\n        return null\r\n    } else if (!conf.url) {\r\n        conf.url = `animal/${id}/animal_${id}_`\r\n    }\r\n    return conf\r\n}\r\n\r\n// 动物动画帧配置\r\nconst ANIMAL_FRAME_ANIM_CONF = {\r\n    211001: { //狮子\r\n        anims: [\r\n            { name: 'idle', interval: 100, loop: false, frameIndexs: ['01'] },\r\n            { name: 'attack', interval: 140, loop: false, frameIndexs: ['01', '07', '08', '09', '10'] },\r\n            { name: 'hit', interval: 140, loop: false, frameIndexs: ['01', '12', '12'] },\r\n            { name: 'die', interval: 160, loop: false, frameIndexs: ['01', '12', '13', '14'] },\r\n        ]\r\n    },\r\n}\r\n\r\nexport {\r\n    getAnimalFrameAnimConf,\r\n}"]}