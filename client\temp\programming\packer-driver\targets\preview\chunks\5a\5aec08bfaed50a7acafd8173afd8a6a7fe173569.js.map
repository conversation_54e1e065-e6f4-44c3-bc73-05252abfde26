{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/script/view/lobby/LobbyWindCtrl.ts"], "names": ["_decorator", "Label", "g<PERSON>elper", "viewHelper", "ccclass", "LobbyWindCtrl", "mc", "BaseWindCtrl", "enterGameNode_", "user", "listenEventMaps", "onCreate", "getModel", "onEnter", "data", "Child", "string", "game", "getDay", "onClean", "onClickEnterGame", "event", "enterGame", "err", "net", "request", "nickname", "getNickname", "roleId", "getRoleId", "show<PERSON><PERSON><PERSON>", "init", "gotoWind"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAwBC,MAAAA,K,OAAAA,K;;AACxBC,MAAAA,O,iBAAAA,O;;AAEAC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcJ,U;;yBAGCK,a,GADpBD,O,UAAD,MACqBC,aADrB,SAC2CC,EAAE,CAACC,YAD9C,CAC2D;AAAA;AAAA;AAEvD;AAFuD,eAG/CC,cAH+C,GAGxB,IAHwB;AAGnB;AACpC;AAJuD,eAM/CC,IAN+C,GAM7B,IAN6B;AAAA;;AAQhDC,QAAAA,eAAe,GAAG;AACrB,iBAAO,EAAP;AACH;;AAEYC,QAAAA,QAAQ,GAAG;AAAA;;AAAA;AACpB,YAAA,KAAI,CAACF,IAAL,GAAY,KAAI,CAACG,QAAL,CAAc,MAAd,CAAZ;AADoB;AAEvB;;AAEMC,QAAAA,OAAO,CAACC,IAAD,EAAY;AACtB,eAAKN,cAAL,CAAoBO,KAApB,CAA0B,KAA1B,EAAiCd,KAAjC,EAAwCe,MAAxC,GAAiD;AAAA;AAAA,kCAAQC,IAAR,CAAaC,MAAb,KAAwB,MAAxB,GAAiC,QAAlF;AACH;;AAEMC,QAAAA,OAAO,GAAG,CAChB,CArBsD,CAuBvD;AACA;AAEA;;;AACAC,QAAAA,gBAAgB,CAACC,KAAD,EAAoBP,IAApB,EAAkC;AAC9C,eAAKQ,SAAL;AACH,SA7BsD,CA8BvD;AACA;AAEA;;;AAEcA,QAAAA,SAAS,GAAG;AAAA;;AAAA;AACtB,gBAAM;AAAEC,cAAAA,GAAF;AAAOT,cAAAA;AAAP,sBAAsB;AAAA;AAAA,oCAAQU,GAAR,CAAYC,OAAZ,CAAoB,eAApB,EAAqC;AAAEC,cAAAA,QAAQ,EAAE,MAAI,CAACjB,IAAL,CAAUkB,WAAV,EAAZ;AAAqCC,cAAAA,MAAM,EAAE,MAAI,CAACnB,IAAL,CAAUoB,SAAV;AAA7C,aAArC,CAA5B;;AACA,gBAAIN,GAAJ,EAAS;AACL,qBAAO;AAAA;AAAA,4CAAWO,SAAX,CAAqBP,GAArB,CAAP;AACH;;AACD;AAAA;AAAA,oCAAQN,IAAR,CAAac,IAAb,CAAkBjB,IAAlB;AACA;AAAA;AAAA,0CAAWkB,QAAX,CAAoB,MAApB;AANsB;AAOzB;;AA1CsD,O", "sourcesContent": ["import { _decorator, EventTouch, Label, log, Node } from \"cc\";\nimport { gHelper } from \"../../common/helper/GameHelper\";\nimport UserModel from \"../../model/common/UserModel\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nconst { ccclass } = _decorator;\n\n@ccclass\nexport default class LobbyWindCtrl extends mc.BaseWindCtrl {\n\n    //@autocode property begin\n    private enterGameNode_: Node = null // path://enter_game_be_n\n    //@end\n\n    private user: UserModel = null\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n        this.user = this.getModel('user')\n    }\n\n    public onEnter(data: any) {\n        this.enterGameNode_.Child('val', Label).string = gHelper.game.getDay() ? '继续冒险' : '开始新的冒险'\n    }\n\n    public onClean() {\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://enter_game_be_n\n    onClickEnterGame(event: EventTouch, data: string) {\n        this.enterGame()\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    private async enterGame() {\n        const { err, data } = await gHelper.net.request('game/HD_Entry', { nickname: this.user.getNickname(), roleId: this.user.getRoleId() })\n        if (err) {\n            return viewHelper.showAlert(err)\n        }\n        gHelper.game.init(data)\n        viewHelper.gotoWind('game')\n    }\n}\n"]}