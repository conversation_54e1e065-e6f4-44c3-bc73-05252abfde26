System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, easing, instantiate, js, Prefab, Tween, tween, UIOpacity, v3, view, Widget, BasePnlCtrl, CoreEventType, loader, ViewCtrlMgr, _crd;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfBasePnlCtrl(extras) {
    _reporterNs.report("BasePnlCtrl", "../base/BasePnlCtrl", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCoreEventType(extras) {
    _reporterNs.report("CoreEventType", "../event/CoreEventType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfloader(extras) {
    _reporterNs.report("loader", "../utils/ResLoader", _context.meta, extras);
  }

  _export("default", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      easing = _cc.easing;
      instantiate = _cc.instantiate;
      js = _cc.js;
      Prefab = _cc.Prefab;
      Tween = _cc.Tween;
      tween = _cc.tween;
      UIOpacity = _cc.UIOpacity;
      v3 = _cc.v3;
      view = _cc.view;
      Widget = _cc.Widget;
    }, function (_unresolved_2) {
      BasePnlCtrl = _unresolved_2.default;
    }, function (_unresolved_3) {
      CoreEventType = _unresolved_3.default;
    }, function (_unresolved_4) {
      loader = _unresolved_4.loader;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7b762tQD0JATLLljp2IsxGT", "ViewCtrlMgr", undefined);

      __checkObsolete__(['easing', 'instantiate', 'js', 'Node', 'Prefab', 'Tween', 'tween', 'UIOpacity', 'v3', 'view', 'Widget']);

      _export("default", ViewCtrlMgr = class ViewCtrlMgr {
        constructor() {
          this.node = null;
          this.caches = new Map();
          this.opened = [];
          this.masks = [];
          // 遮罩列表
          this.pnlOpenIndex = 0;
          // 打开顺序id
          this.pnlIndexConf = {};
          // ui的层级关系配置
          this.loadQueues = [];
          // 当前加载队列
          this.loadId = 0;
        }

        getOpened() {
          return this.opened;
        }

        setPnlIndexConf(conf) {
          this.pnlIndexConf = conf;
        } // 添加到加载队列


        addLoadQueue(name) {
          return this.loadQueues.add({
            id: ++this.loadId,
            name: name,
            url: ''
          });
        }

        hasLoadQueue(name) {
          return this.loadQueues.has('name', name);
        }

        __load(name, info) {
          var _this = this;

          return _asyncToGenerator(function* () {
            // console.time('load ' + name)
            var pfb = null,
                url = '',
                mod = '';
            var [wind, pnl] = name.split('/');

            if (!pnl) {
              // 没有输入模块的情况下
              // 先默认从当前模块找
              var head = ut.initialUpperCase(wind).replace('Pnl', '');
              mod = mc.currWindName;
              info.url = url = "view/" + mod + "/" + head + "Pnl";
              (_crd && loader === void 0 ? (_reportPossibleCrUseOfloader({
                error: Error()
              }), loader) : loader).error = false;
              pfb = yield (_crd && loader === void 0 ? (_reportPossibleCrUseOfloader({
                error: Error()
              }), loader) : loader).loadRes(url, Prefab);
              (_crd && loader === void 0 ? (_reportPossibleCrUseOfloader({
                error: Error()
              }), loader) : loader).error = true;

              if (!pfb) {
                //如果没有就从common里面找
                mod = 'common';
                info.url = url = "view/" + mod + "/" + head + "Pnl";
                pfb = yield (_crd && loader === void 0 ? (_reportPossibleCrUseOfloader({
                  error: Error()
                }), loader) : loader).loadRes(url, Prefab);
              }
            } else {
              var _head = ut.initialUpperCase(pnl).replace('Pnl', '');

              mod = wind;
              info.url = url = "view/" + mod + "/" + _head + "Pnl";
              pfb = yield (_crd && loader === void 0 ? (_reportPossibleCrUseOfloader({
                error: Error()
              }), loader) : loader).loadRes(url, Prefab);
            } // console.timeEnd('load ' + name)


            if (!_this.loadQueues.remove('id', info.id)) {
              pfb = null;
            }

            info.id = 0; //这里表示已经加载完成了 用于后续弹出是否重试检测是的时候

            return Promise.resolve({
              mod,
              url,
              pfb
            });
          })();
        } // 加载一个Pnl


        __loadPnl(name, info) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            var {
              mod,
              url,
              pfb
            } = yield _this2.__load(name, info);

            if (!pfb) {
              return Promise.resolve(null);
            }

            var it = mc.instantiate(pfb, _this2.node);
            var className = it.name + 'Ctrl';

            if (!js.getClassByName(className)) {
              logger.error('loadPnl error! not found class ' + className);
              return Promise.resolve(null);
            }

            var pnl = it.getComponent(className);

            if (!pnl) {
              pnl = it.addComponent(className);
            }

            if (!pnl || !(pnl instanceof (_crd && BasePnlCtrl === void 0 ? (_reportPossibleCrUseOfBasePnlCtrl({
              error: Error()
            }), BasePnlCtrl) : BasePnlCtrl))) {
              logger.error('loadPnl error! not found class ' + className);
              return Promise.resolve(null);
            }

            pnl.key = name;
            pnl.mod = mod;
            pnl.url = url; // 这里检查一下 是否还没有加载属性

            if (!pnl._isLoadProperty) {
              logger.error('load pnl error! not load property. at=' + className);
              pnl.loadProperty();
            }

            it.active = false;
            yield pnl.__create();

            _this2.caches.set(url, pnl);

            return Promise.resolve(pnl);
          })();
        } // 获取缓存的Pnl


        __getForCache(name) {
          var [wind, key] = name.split('/'),
              ui = null;

          if (!key) {
            var head = ut.initialUpperCase(wind).replace('Pnl', '');
            ui = this.caches.get("view/" + mc.currWindName + "/" + head + "Pnl");

            if (!ui) {
              ui = this.caches.get("view/common/" + head + "Pnl");
            }
          } else {
            var _head2 = ut.initialUpperCase(key).replace('Pnl', '');

            ui = this.caches.get("view/" + wind + "/" + _head2 + "Pnl");
          }

          return ui;
        } // 预加载


        preloadPnl(name) {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            var pnl = _this3.__getForCache(name);

            if (!pnl && !_this3.hasLoadQueue(name)) {
              pnl = yield _this3.__loadPnl(name, _this3.addLoadQueue(name));
            }

            return pnl;
          })();
        } // 获取一个遮罩


        getMask(ui) {
          var it = this.masks.pop();

          if (!it) {
            var pfb = assetsMgr.getPrefab('PNL_MASK');

            if (pfb) {
              it = instantiate(pfb);
            }
          }

          if (it) {
            it.parent = this.node;
            it.active = true;
            it.opacity = 150; // it.zIndex = ui.node.zIndex - 1

            it.zIndex = ui.node.zIndex;
          }

          ui.mask = it;
        }

        putMask(ui) {
          if (ui && ui.mask) {
            ui.mask.parent = null;
            this.masks.push(ui.mask);
            ui.mask = null;
          }
        } // 播放显示的动作


        playShowAction(ui) {
          return _asyncToGenerator(function* () {
            var widget = ui.Component(Widget);

            if (widget && widget.enabled) {
              widget.updateAlignment();
              widget.enabled = false;
            }

            Tween.stopAllByTarget(ui.node);
            ui.node.scale = v3(0.4, 0.4);
            tween(ui.node).to(0.25, {
              scale: v3(1, 1)
            }, {
              easing: easing.backOut
            }).start();

            if (ui.mask) {
              var uiOpacity = ui.mask.Component(UIOpacity);
              Tween.stopAllByTarget(uiOpacity);
              uiOpacity.opacity = 0;
              tween(uiOpacity).to(0.3, {
                opacity: 120
              }, {
                easing: easing.sineOut
              }).start();
            }

            yield ut.wait(0.25);

            if (ui.isValid) {
              ui.onPlayActionComplete();
              eventCenter.emit((_crd && CoreEventType === void 0 ? (_reportPossibleCrUseOfCoreEventType({
                error: Error()
              }), CoreEventType) : CoreEventType).PNL_ENTER_PLAY_DONE, ui);
            }
          })();
        } // 适应大小


        adaptRootSize(ui) {
          if (!ui) {
            return;
          }

          var root = ui.Child('root') || ui.Child('root_n');

          if (!root) {
            return;
          }

          var wsize = view.getVisibleSize();
          var dsize = view.getDesignResolutionSize();
          var rsize = root.transform.contentSize; // 算出宽度比例

          var scale = rsize.width / dsize.width * wsize.width / rsize.width; // 如果高度超过了

          var height = wsize.height - ui.adaptHeight;

          if (rsize.height * scale > height) {
            scale = height / rsize.height;
          }

          root.scale = v3(Math.min(1.2, scale));
        }

        getNextOpenIndex() {
          return ++this.pnlOpenIndex;
        }

        pushPnl(ui) {
          ui.__open_index = this.getNextOpenIndex();
          this.opened.remove('url', ui.url);
          this.opened.push(ui);
          this.updatePnlIndex();
        }

        popPnl(ui) {
          this.opened.remove('url', ui.url);
          this.updatePnlIndex();
        } // 刷新ui层级关系


        updatePnlIndex() {
          // 排个序根据打开顺序
          var list = this.opened.sort((a, b) => a.__open_index - b.__open_index).map((m, i) => {
            var index = i + 1;
            return {
              ui: m,
              key: m.key,
              initIndex: index,
              sortIndex: index * 1000
            };
          });
          list.forEach(m => {
            var _conf$lt;

            var conf = this.pnlIndexConf[m.key];

            if (!conf) {
              return;
            }

            var lt = (_conf$lt = conf.lt) == null ? void 0 : _conf$lt.slice(); // 找出大于的 调整自己的位置 在大于的上面

            if (conf.gt) {
              var arr = list.filter(pnl => conf.gt.has(pnl.key)).sort((a, b) => b.sortIndex - a.sortIndex),
                  temp = arr[0];

              if (temp && m.sortIndex < temp.sortIndex) {
                m.sortIndex = temp.sortIndex + m.initIndex;
              } // 这里如果小于的也在 当前大于的大于里面 就删除掉


              lt && arr.forEach(m => {
                var _this$pnlIndexConf$m$;

                var gt = (_this$pnlIndexConf$m$ = this.pnlIndexConf[m.key]) == null ? void 0 : _this$pnlIndexConf$m$.gt;
                gt && lt.delete(s => gt.has(s));
              });
            } // 找出小于的 调整小于的位置 在自己的上面


            lt && list.filter(pnl => lt.has(pnl.key)).forEach(temp => {
              if (temp.sortIndex < m.sortIndex) {
                temp.sortIndex = m.sortIndex + temp.initIndex;
              }
            });
          });
          list.sort((a, b) => a.sortIndex - b.sortIndex).forEach((m, i) => m.ui.setIndex(i * 10));
          this.opened.sort((a, b) => a.node.zIndex - b.node.zIndex);
        } // 显示一个UI


        show(pnl) {
          var _arguments = arguments,
              _this4 = this;

          return _asyncToGenerator(function* () {
            mc.lockTouch('__show_pnl__');
            var ui = null;

            for (var _len = _arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
              params[_key - 1] = _arguments[_key];
            }

            if (typeof pnl === 'string') {
              ui = _this4.__getForCache(pnl);

              if (!ui && !_this4.hasLoadQueue(pnl)) {
                var data = _this4.addLoadQueue(pnl);

                data.params = params;
                eventCenter.emit((_crd && CoreEventType === void 0 ? (_reportPossibleCrUseOfCoreEventType({
                  error: Error()
                }), CoreEventType) : CoreEventType).LOAD_BEGIN_PNL, data);
                ui = yield _this4.__loadPnl(pnl, data);
                eventCenter.emit((_crd && CoreEventType === void 0 ? (_reportPossibleCrUseOfCoreEventType({
                  error: Error()
                }), CoreEventType) : CoreEventType).LOAD_END_PNL, data);
              }
            } else if (pnl.isValid && pnl._state !== 'clean') {
              ui = pnl;
            } else {
              return _this4.show(pnl.key, ...params);
            }

            if (!ui || !ui.isValid) {
              mc.unlockTouch('__show_pnl__');
              return null;
            } else if (!ui.getActive()) {
              _this4.pushPnl(ui);

              ui.setActive(true);
              ui.isMask && _this4.getMask(ui);

              ui.__enter(...params); // this.adaptRootSize(ui)


              ui.setOpacity(255); // 发送进入事件

              eventCenter.emit((_crd && CoreEventType === void 0 ? (_reportPossibleCrUseOfCoreEventType({
                error: Error()
              }), CoreEventType) : CoreEventType).PNL_ENTER, ui);

              if (ui.isAct) {
                _this4.playShowAction(ui).then(() => mc.unlockTouch('__show_pnl__'));
              } else {
                ui.node.scale = v3(1, 1);
                ui.onPlayActionComplete();
                eventCenter.emit((_crd && CoreEventType === void 0 ? (_reportPossibleCrUseOfCoreEventType({
                  error: Error()
                }), CoreEventType) : CoreEventType).PNL_ENTER_PLAY_DONE, ui);
                mc.unlockTouch('__show_pnl__');
              }
            } else {
              mc.unlockTouch('__show_pnl__');
            }

            return ui;
          })();
        } // 隐藏一个Pnl


        hide(val) {
          var ui = val instanceof (_crd && BasePnlCtrl === void 0 ? (_reportPossibleCrUseOfBasePnlCtrl({
            error: Error()
          }), BasePnlCtrl) : BasePnlCtrl) ? val : this.__getForCache(val);

          if (!ui || !ui.isValid) {
            return;
          }

          this.popPnl(ui);
          this.putMask(ui);

          if (ui.getActive()) {
            ui.__remove();

            ui.setActive(false);
            eventCenter.emit((_crd && CoreEventType === void 0 ? (_reportPossibleCrUseOfCoreEventType({
              error: Error()
            }), CoreEventType) : CoreEventType).PNL_LEAVE, ui);
          }
        } // 隐藏所有Pnl


        hideAll(val, ignores) {
          if (!val) {
            var arr = ignores ? ignores.split('|') : [];

            for (var i = this.opened.length - 1; i >= 0; i--) {
              var m = this.opened[i]; // 这里关闭所有的时候 忽略掉不清理的UI

              if (m.isClean && arr.indexOf(m.key) === -1) {
                this.opened.splice(i, 1);
                this.putMask(m);

                if (m.getActive()) {
                  m.__remove();

                  m.setActive(false);
                  eventCenter.emit((_crd && CoreEventType === void 0 ? (_reportPossibleCrUseOfCoreEventType({
                    error: Error()
                  }), CoreEventType) : CoreEventType).PNL_LEAVE, m);
                }
              }
            }

            this.updatePnlIndex();
          } else {
            val.split('|').forEach(m => this.hide(m));
          }
        } // 清理一个Pnl


        clean(val, force) {
          var ui = val instanceof (_crd && BasePnlCtrl === void 0 ? (_reportPossibleCrUseOfBasePnlCtrl({
            error: Error()
          }), BasePnlCtrl) : BasePnlCtrl) ? val : this.__getForCache(val);

          if (!ui || !ui.isClean && !force) {
            return;
          }

          this.hide(ui);

          ui.__clean();

          ui.node.destroy();
          this.caches.delete(ui.url);
          (_crd && loader === void 0 ? (_reportPossibleCrUseOfloader({
            error: Error()
          }), loader) : loader).releaseRes(ui.url, Prefab);
        } // 清理所有Pnl


        cleanAll(val, force) {
          if (!val) {
            this.caches.forEach(m => this.clean(m, force));
            this.cleanLoadQueue();
          } else {
            val.split('|').forEach(m => {
              this.clean(m, force);
              this.giveupLoadByName(m);
            });
          }
        } // 清理pnl根据模块


        cleanByMod(mod) {
          this.caches.forEach(m => m.mod === mod && this.clean(m));
        } // 清理所有未打开的Pnl


        cleanAllUnused() {
          this.caches.forEach(m => !m.getActive() && this.clean(m));
          this.cleanLoadQueue();
        } // 清理加载队列


        cleanLoadQueue(isUnlock) {
          while (this.loadQueues.length > 0) {
            (_crd && loader === void 0 ? (_reportPossibleCrUseOfloader({
              error: Error()
            }), loader) : loader).giveupLoad(this.loadQueues.pop().url);
          }

          if (isUnlock) {
            mc.unlockTouch('__show_pnl__');
          }
        } // 放弃加载


        giveupLoadByName(name) {
          var data = this.loadQueues.remove('name', name);
          data && (_crd && loader === void 0 ? (_reportPossibleCrUseOfloader({
            error: Error()
          }), loader) : loader).giveupLoad(data.url);
        } // 放弃当前加载


        giveupLoadById(id) {
          var data = this.loadQueues.remove('id', id);
          data && (_crd && loader === void 0 ? (_reportPossibleCrUseOfloader({
            error: Error()
          }), loader) : loader).giveupLoad(data.url);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=109b425b47f0848ee2676d8636eb5622b533b698.js.map