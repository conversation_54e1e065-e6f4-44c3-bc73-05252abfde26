{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/script/model/login/LoginModel.ts"], "names": ["log", "ca", "LoginState", "NetEvent", "g<PERSON>elper", "ecode", "LoginModel", "mc", "addmodel", "BaseModel", "DEFAULT_MAX_RECONNECT_ATTEMPTS", "net", "user", "game", "initBaseAsset", "initGameAsset", "reconnectAttempts", "reconnectionDelay", "onCreate", "getModel", "isInitBaseAsset", "setInitBaseAsset", "val", "isInitGameAsset", "setInitGameAsset", "onDisconnect", "err", "emit", "NET_DISCONNECT", "connect", "offAll", "ok", "getServerUrl", "on", "reconnect", "isKick", "ut", "wait", "Math", "min", "disconnect", "off", "close", "loadGuestId", "id", "storageMgr", "loadString", "UID", "saveGuestId", "saveString", "getLobbyServerIsNeedQueueUp", "res", "post", "url", "getHttpServerUrl", "data", "lobbyFull", "<PERSON><PERSON><PERSON>in", "accountToken", "state", "NOT_ACCOUNT_TOKEN", "request", "lang", "platform", "getShopPlatform", "version", "VERSION", "TOKEN_INVALID", "VERSION_TOOLOW", "CUR_LOBBY_FULL", "LOBBY_QUEUE_UP", "QUEUE_UP", "BAN_ACCOUNT", "BANACCOUNT_TIME", "type", "banAccountType", "time", "banAccountSurplusTime", "FAILURE", "info", "init", "gameBaseData", "initBaseData", "SUCCEED", "sid", "guest<PERSON><PERSON><PERSON>", "guestId", "getBrowserParamByKey", "nickname", "assetsMgr", "loginVerifyRet"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOSA,MAAAA,G,OAAAA,G;;AAPFC,MAAAA,E;;AACEC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,Q;;AACEC,MAAAA,O,iBAAAA,O;;AAGAC,MAAAA,K,iBAAAA,K;;;;;;;;;AAIT;AACA;AACA;yBAEqBC,U,WADpBC,EAAE,CAACC,QAAH,CAAY,OAAZ,C,gBAAD,MACqBF,UADrB,SACwCC,EAAE,CAACE,SAD3C,CACqD;AAAA;AAAA;AAAA,eAEhCC,8BAFgC,GAEC,EAFD;AAEI;AAFJ,eAIzCC,GAJyC,GAIrB,IAJqB;AAAA,eAKzCC,IALyC,GAKvB,IALuB;AAAA,eAMzCC,IANyC,GAMvB,IANuB;AAAA,eAQzCC,aARyC,GAQhB,KARgB;AAQV;AARU,eASzCC,aATyC,GAShB,KATgB;AASV;AATU,eAUzCC,iBAVyC,GAUb,CAVa;AAAA,eAWzCC,iBAXyC,GAWb,CAXa;AAAA;;AAWX;AAE/BC,QAAAA,QAAQ,GAAG;AACd,eAAKP,GAAL,GAAW,KAAKQ,QAAL,CAAc,KAAd,CAAX;AACA,eAAKP,IAAL,GAAY,KAAKO,QAAL,CAAc,MAAd,CAAZ;AACA,eAAKN,IAAL,GAAY,KAAKM,QAAL,CAAc,MAAd,CAAZ;AACH;;AAEMC,QAAAA,eAAe,GAAG;AAAE,iBAAO,KAAKN,aAAZ;AAA2B;;AAC/CO,QAAAA,gBAAgB,CAACC,GAAD,EAAe;AAAE,eAAKR,aAAL,GAAqBQ,GAArB;AAA0B;;AAC3DC,QAAAA,eAAe,GAAG;AAAE,iBAAO,KAAKR,aAAZ;AAA2B;;AAC/CS,QAAAA,gBAAgB,CAACF,GAAD,EAAe;AAAE,eAAKP,aAAL,GAAqBO,GAArB;AAA0B,SAtBjB,CAwBjD;;;AACQG,QAAAA,YAAY,CAACC,GAAD,EAAW;AAC3B,eAAKC,IAAL,CAAU;AAAA;AAAA,oCAASC,cAAnB,EAAmCF,GAAnC;AACH,SA3BgD,CA6BjD;;;AACaG,QAAAA,OAAO,GAAG;AAAA;;AAAA;AACnB,YAAA,KAAI,CAAClB,GAAL,CAASmB,MAAT,GADmB,CAEnB;;;AACA,gBAAMC,EAAE,SAAS,KAAI,CAACpB,GAAL,CAASkB,OAAT,CAAiB;AAAA;AAAA,oCAAQG,YAAR,EAAjB,CAAjB;;AACA,gBAAID,EAAJ,EAAQ;AACJ,cAAA,KAAI,CAACpB,GAAL,CAASsB,EAAT,CAAY,YAAZ,EAA0B,KAAI,CAACR,YAA/B,EAA6C,KAA7C;AACH;;AACD,mBAAOM,EAAP;AAPmB;AAQtB,SAtCgD,CAwCjD;;;AACaG,QAAAA,SAAS,GAAG;AAAA;;AAAA;AACrB,YAAA,MAAI,CAAClB,iBAAL,GAAyB,CAAzB;AACA,YAAA,MAAI,CAACC,iBAAL,GAAyB,CAAzB;AACA,gBAAIc,EAAE,GAAG,KAAT;;AACA,mBAAO,MAAI,CAACf,iBAAL,GAAyB,MAAI,CAACN,8BAArC,EAAqE;AACjE,kBAAI,MAAI,CAACC,GAAL,CAASwB,MAAT,EAAJ,EAAuB;AACnB,uBAAO,KAAP;AACH;;AACDJ,cAAAA,EAAE,SAAS,MAAI,CAACpB,GAAL,CAASkB,OAAT,CAAiB;AAAA;AAAA,sCAAQG,YAAR,EAAjB,CAAX;;AACA,kBAAID,EAAJ,EAAQ;AACJ,gBAAA,MAAI,CAACpB,GAAL,CAASsB,EAAT,CAAY,YAAZ,EAA0B,MAAI,CAACR,YAA/B,EAA6C,MAA7C;;AACA;AACH;;AACD,oBAAMW,EAAE,CAACC,IAAH,CAAQ,MAAI,CAACpB,iBAAb,CAAN;AACA,cAAA,MAAI,CAACD,iBAAL,IAA0B,CAA1B;AACA,cAAA,MAAI,CAACC,iBAAL,GAAyBqB,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,MAAI,CAACtB,iBAAL,GAAyB,CAArC,CAAzB,CAXiE,CAWA;AACpE;;AACD,mBAAOc,EAAP;AAjBqB;AAkBxB,SA3DgD,CA6DjD;;;AACOS,QAAAA,UAAU,GAAG;AAChB,eAAK7B,GAAL,CAAS8B,GAAT,CAAa,YAAb;AACA,eAAK9B,GAAL,CAAS+B,KAAT;AACH,SAjEgD,CAmEjD;;;AACcC,QAAAA,WAAW,GAAG;AAAA;AACxB,gBAAIC,EAAE,GAAGC,UAAU,CAACC,UAAX,CAAsB,UAAtB,CAAT;;AACA,gBAAI,CAACF,EAAL,EAAS,CACL;AACH;;AACD,mBAAOA,EAAE,IAAIR,EAAE,CAACW,GAAH,EAAb;AALwB;AAM3B;;AAEOC,QAAAA,WAAW,CAACJ,EAAD,EAAa;AAC5BC,UAAAA,UAAU,CAACI,UAAX,CAAsB,UAAtB,EAAkCL,EAAlC,EAD4B,CAE5B;AACH,SA/EgD,CAiFjD;;;AACaM,QAAAA,2BAA2B,GAAG;AAAA;;AAAA;AAAA;;AACvC,gBAAMC,GAAG,SAAS,MAAI,CAACxC,GAAL,CAASyC,IAAT,CAAc;AAAEC,cAAAA,GAAG,EAAE;AAAA;AAAA,sCAAQC,gBAAR,KAA6B;AAApC,aAAd,CAAlB;AACA,mBAAO,CAAC,EAACH,GAAD,yBAACA,GAAG,CAAEI,IAAN,aAAC,UAAWC,SAAZ,CAAR;AAFuC;AAG1C,SArFgD,CAuFjD;;;AACaC,QAAAA,QAAQ,CAACC,YAAD,EAAsC;AAAA;;AAAA;AACvDA,YAAAA,YAAY,GAAGA,YAAY,IAAIb,UAAU,CAACC,UAAX,CAAsB,eAAtB,CAA/B;;AACA,gBAAI,CAACY,YAAD,IAAiBA,YAAY,KAAK,GAAtC,EAA2C;AACvC,kBAAIA,YAAY,KAAK,GAArB,EAA0B,CACtB;AACH;;AACD,qBAAO;AAAEC,gBAAAA,KAAK,EAAE;AAAA;AAAA,8CAAWC;AAApB,eAAP;AACH;;AACD,gBAAM;AAAElC,cAAAA,GAAF;AAAO6B,cAAAA;AAAP,sBAAsB,MAAI,CAAC5C,GAAL,CAASkD,OAAT,CAAiB,mBAAjB,EAAsC;AAC9DH,cAAAA,YAD8D;AAE9DI,cAAAA,IAAI,EAAEvD,EAAE,CAACuD,IAFqD;AAG9DC,cAAAA,QAAQ,EAAE;AAAA;AAAA,sCAAQC,eAAR,EAHoD;AAI9DC,cAAAA,OAAO,EAAE;AAAA;AAAA,4BAAGC;AAJkD,aAAtC,CAA5B;;AAMA,gBAAIxC,GAAG,KAAK;AAAA;AAAA,gCAAMkC,iBAAd,IAAmClC,GAAG,KAAK;AAAA;AAAA,gCAAMyC,aAArD,EAAoE;AAAE;AAClE,qBAAO;AAAER,gBAAAA,KAAK,EAAE;AAAA;AAAA,8CAAWC;AAApB,eAAP;AACH,aAFD,MAEO,IAAIlC,GAAG,KAAK;AAAA;AAAA,gCAAM0C,cAAlB,EAAkC;AAAE;AACvC,qBAAO;AAAET,gBAAAA,KAAK,EAAE;AAAA;AAAA,8CAAWS,cAApB;AAAoCb,gBAAAA;AAApC,eAAP;AACH,aAFM,MAEA,IAAI7B,GAAG,KAAK;AAAA;AAAA,gCAAM2C,cAAlB,EAAkC;AAAE;AACvC,oBAAMjC,EAAE,CAACC,IAAH,CAAQ,GAAR,CAAN;AACA,qBAAO,MAAI,CAACoB,QAAL,CAAcC,YAAd,CAAP;AACH,aAHM,MAGA,IAAIhC,GAAG,KAAK;AAAA;AAAA,gCAAM4C,cAAlB,EAAkC;AAAE;AACvC,kBAAMvC,EAAE,SAAS,MAAI,CAACmB,2BAAL,EAAjB;;AACA,kBAAInB,EAAJ,EAAQ;AACJ,uBAAO;AAAE4B,kBAAAA,KAAK,EAAE;AAAA;AAAA,gDAAWY;AAApB,iBAAP,CADI,CACkC;AACzC;;AACD,qBAAO,MAAI,CAACd,QAAL,CAAcC,YAAd,CAAP,CALqC,CAKF;AACtC,aANM,MAMA,IAAIhC,GAAG,KAAK;AAAA;AAAA,gCAAM8C,WAAlB,EAA+B;AAAE;AACpC,qBAAO;AAAEb,gBAAAA,KAAK,EAAE;AAAA;AAAA,8CAAWc,eAApB;AAAqCC,gBAAAA,IAAI,EAAEnB,IAAI,CAACoB,cAAhD;AAAgEC,gBAAAA,IAAI,EAAErB,IAAI,CAACsB;AAA3E,eAAP;AACH,aAFM,MAEA,IAAInD,GAAJ,EAAS;AACZ1B,cAAAA,GAAG,CAAC,eAAD,EAAkB0B,GAAlB,CAAH;AACA,qBAAO;AAAEiC,gBAAAA,KAAK,EAAE;AAAA;AAAA,8CAAWmB,OAApB;AAA6BpD,gBAAAA,GAAG,EAAEA;AAAlC,eAAP;AACH;;AACD,gBAAMqD,IAAI,GAAGxB,IAAI,CAAC3C,IAAlB;;AACA,YAAA,MAAI,CAACA,IAAL,CAAUoE,IAAV,CAAeD,IAAf;;AACA,gBAAIxB,IAAI,CAAC0B,YAAT,EAAuB;AACnB,cAAA,MAAI,CAACpE,IAAL,CAAUqE,YAAV,CAAuB3B,IAAI,CAAC0B,YAA5B;AACH,aArCsD,CAsCvD;;;AACApC,YAAAA,UAAU,CAACI,UAAX,CAAsB,eAAtB,EAAuCM,IAAI,CAACG,YAAL,IAAqB,GAA5D;AACA,mBAAO;AAAEC,cAAAA,KAAK,EAAE;AAAA;AAAA,4CAAWwB,OAApB;AAA6B5B,cAAAA,IAAI,EAAE;AAAE6B,gBAAAA,GAAG,EAAEL,IAAI,CAACK;AAAZ;AAAnC,aAAP;AAxCuD;AAyC1D,SAjIgD,CAmIjD;;;AACaC,QAAAA,UAAU,GAAG;AAAA;;AAAA;AACtB,gBAAIC,OAAO,GAAGlD,EAAE,CAACmD,oBAAH,CAAwB,IAAxB,CAAd;;AACA,gBAAI,CAACD,OAAL,EAAc;AACVA,cAAAA,OAAO,SAAS,MAAI,CAAC3C,WAAL,EAAhB;AACH;;AACD,gBAAMZ,EAAE,SAAS,MAAI,CAACG,SAAL,EAAjB;;AACA,gBAAI,CAACH,EAAL,EAAS;AACL,qBAAO;AAAE4B,gBAAAA,KAAK,EAAE;AAAA;AAAA,8CAAWmB,OAApB;AAA6BpD,gBAAAA,GAAG,EAAE;AAAlC,eAAP;AACH;;AACD,gBAAM;AAAEA,cAAAA,GAAF;AAAO6B,cAAAA;AAAP,sBAAsB,MAAI,CAAC5C,GAAL,CAASkD,OAAT,CAAiB,qBAAjB,EAAwC;AAChEyB,cAAAA,OADgE;AAEhEE,cAAAA,QAAQ,EAAEC,SAAS,CAAC3B,IAAV,CAAe,sBAAf,CAFsD;AAGhEC,cAAAA,QAAQ,EAAE;AAAA;AAAA,sCAAQC,eAAR;AAHsD,aAAxC,EAIzB,IAJyB,CAA5B;;AAKA,gBAAI,CAACtC,GAAL,EAAU;AACN,cAAA,MAAI,CAACsB,WAAL,CAAiB,CAAAO,IAAI,QAAJ,YAAAA,IAAI,CAAE+B,OAAN,KAAiB,EAAlC;AACH;;AACD,mBAAO,MAAI,CAACI,cAAL,CAAoBhE,GAApB,EAAyB6B,IAAzB,oBAAyBA,IAAI,CAAEG,YAA/B,CAAP;AAjBsB;AAkBzB,SAtJgD,CAwJjD;;;AACQgC,QAAAA,cAAc,CAAChE,GAAD,EAAcgC,YAAd,EAAyC;AAC3D,cAAIhC,GAAJ,EAAS;AACL,mBAAO;AAAEiC,cAAAA,KAAK,EAAE;AAAA;AAAA,4CAAWmB,OAApB;AAA6BpD,cAAAA,GAAG,EAAEA;AAAlC,aAAP;AACH;;AACDmB,UAAAA,UAAU,CAACI,UAAX,CAAsB,eAAtB,EAAuCS,YAAY,IAAI,GAAvD,EAJ2D,CAK3D;;AACA,iBAAO;AAAEC,YAAAA,KAAK,EAAE;AAAA;AAAA,0CAAWwB,OAApB;AAA6BzB,YAAAA;AAA7B,WAAP;AACH;;AAhKgD,O", "sourcesContent": ["import ca from \"db://assets/scene/ca\"\r\nimport { LoginState } from \"../../common/constant/Enums\"\r\nimport NetEvent from \"../../common/event/NetEvent\"\r\nimport { gHelper } from \"../../common/helper/GameHelper\"\r\nimport NetworkModel from \"../common/NetworkModel\"\r\nimport UserModel from \"../common/UserModel\"\r\nimport { ecode } from \"../../common/constant/ECode\"\r\nimport { log } from \"cc\"\r\nimport GameModel from \"../game/GameModel\"\r\n\r\n/**\r\n * 登录模块\r\n */\r\*************('login')\r\nexport default class LoginModel extends mc.BaseModel {\r\n\r\n    private readonly DEFAULT_MAX_RECONNECT_ATTEMPTS = 60 //最大重连次数\r\n\r\n    private net: NetworkModel = null\r\n    private user: UserModel = null\r\n    private game: GameModel = null\r\n\r\n    private initBaseAsset: boolean = false //是否初始化基础资源\r\n    private initGameAsset: boolean = false //是否初始化游戏资源\r\n    private reconnectAttempts: number = 0\r\n    private reconnectionDelay: number = 5 //重连间隔（秒）\r\n\r\n    public onCreate() {\r\n        this.net = this.getModel('net')\r\n        this.user = this.getModel('user')\r\n        this.game = this.getModel('game')\r\n    }\r\n\r\n    public isInitBaseAsset() { return this.initBaseAsset }\r\n    public setInitBaseAsset(val: boolean) { this.initBaseAsset = val }\r\n    public isInitGameAsset() { return this.initGameAsset }\r\n    public setInitGameAsset(val: boolean) { this.initGameAsset = val }\r\n\r\n    // 服务器断开连接\r\n    private onDisconnect(err: any) {\r\n        this.emit(NetEvent.NET_DISCONNECT, err)\r\n    }\r\n\r\n    // 连接服务器\r\n    public async connect() {\r\n        this.net.offAll()\r\n        // 连接服务器\r\n        const ok = await this.net.connect(gHelper.getServerUrl())\r\n        if (ok) {\r\n            this.net.on('disconnect', this.onDisconnect, this)\r\n        }\r\n        return ok\r\n    }\r\n\r\n    // 重新连接\r\n    public async reconnect() {\r\n        this.reconnectAttempts = 0\r\n        this.reconnectionDelay = 2\r\n        let ok = false\r\n        while (this.reconnectAttempts < this.DEFAULT_MAX_RECONNECT_ATTEMPTS) {\r\n            if (this.net.isKick()) {\r\n                return false\r\n            }\r\n            ok = await this.net.connect(gHelper.getServerUrl())\r\n            if (ok) {\r\n                this.net.on('disconnect', this.onDisconnect, this)\r\n                break\r\n            }\r\n            await ut.wait(this.reconnectionDelay)\r\n            this.reconnectAttempts += 1\r\n            this.reconnectionDelay = Math.min(5, this.reconnectionDelay + 1) //下一次久一点\r\n        }\r\n        return ok\r\n    }\r\n\r\n    // 断开网络\r\n    public disconnect() {\r\n        this.net.off('disconnect')\r\n        this.net.close()\r\n    }\r\n\r\n    // 加载游客id\r\n    private async loadGuestId() {\r\n        let id = storageMgr.loadString('guest_id')\r\n        if (!id) {\r\n            // id = await jsbHelper.getDeviceData('guest_id', 'ca_account')\r\n        }\r\n        return id || ut.UID()\r\n    }\r\n\r\n    private saveGuestId(id: string) {\r\n        storageMgr.saveString('guest_id', id)\r\n        // jsbHelper.saveDeviceData('guest_id', id, 'ca_account')\r\n    }\r\n\r\n    // 获取大厅服务器是否需要排队\r\n    public async getLobbyServerIsNeedQueueUp() {\r\n        const res = await this.net.post({ url: gHelper.getHttpServerUrl() + '/getServerLoad' })\r\n        return !!res?.data?.lobbyFull\r\n    }\r\n\r\n    // 尝试登录\r\n    public async tryLogin(accountToken?: string): Promise<any> {\r\n        accountToken = accountToken || storageMgr.loadString('account_token')\r\n        if (!accountToken || accountToken === '0') {\r\n            if (accountToken !== '0') {\r\n                // taHelper.track('ta_tutorial', { tutorial_step: '0-1' }) //首个场景打开\r\n            }\r\n            return { state: LoginState.NOT_ACCOUNT_TOKEN }\r\n        }\r\n        const { err, data } = await this.net.request('lobby/HD_TryLogin', {\r\n            accountToken,\r\n            lang: mc.lang,\r\n            platform: gHelper.getShopPlatform(),\r\n            version: ca.VERSION,\r\n        })\r\n        if (err === ecode.NOT_ACCOUNT_TOKEN || err === ecode.TOKEN_INVALID) { //没有token或者无效 都重新登录\r\n            return { state: LoginState.NOT_ACCOUNT_TOKEN }\r\n        } else if (err === ecode.VERSION_TOOLOW) { //提示版本过低\r\n            return { state: LoginState.VERSION_TOOLOW, data }\r\n        } else if (err === ecode.CUR_LOBBY_FULL) { //当前大厅服满了 重新登陆\r\n            await ut.wait(0.5)\r\n            return this.tryLogin(accountToken)\r\n        } else if (err === ecode.LOBBY_QUEUE_UP) { //大厅服都满了 需要排队\r\n            const ok = await this.getLobbyServerIsNeedQueueUp()\r\n            if (ok) {\r\n                return { state: LoginState.QUEUE_UP } //满了 需要排队\r\n            }\r\n            return this.tryLogin(accountToken) //没满继续登陆\r\n        } else if (err === ecode.BAN_ACCOUNT) { //被封禁了\r\n            return { state: LoginState.BANACCOUNT_TIME, type: data.banAccountType, time: data.banAccountSurplusTime }\r\n        } else if (err) {\r\n            log('tryLogin err!', err)\r\n            return { state: LoginState.FAILURE, err: err }\r\n        }\r\n        const info = data.user\r\n        this.user.init(info)\r\n        if (data.gameBaseData) {\r\n            this.game.initBaseData(data.gameBaseData)\r\n        }\r\n        // 这里大厅服 不再返回账号token 统一使用登陆服的token 重复使用\r\n        storageMgr.saveString('account_token', data.accountToken || '0')\r\n        return { state: LoginState.SUCCEED, data: { sid: info.sid } }\r\n    }\r\n\r\n    // 游客登录\r\n    public async guestLogin() {\r\n        let guestId = ut.getBrowserParamByKey('id')\r\n        if (!guestId) {\r\n            guestId = await this.loadGuestId()\r\n        }\r\n        const ok = await this.reconnect()\r\n        if (!ok) {\r\n            return { state: LoginState.FAILURE, err: 'login.failed' }\r\n        }\r\n        const { err, data } = await this.net.request('login/HD_GuestLogin', {\r\n            guestId,\r\n            nickname: assetsMgr.lang('login.guest_nickname'),\r\n            platform: gHelper.getShopPlatform(),\r\n        }, true)\r\n        if (!err) {\r\n            this.saveGuestId(data?.guestId || '')\r\n        }\r\n        return this.loginVerifyRet(err, data?.accountToken)\r\n    }\r\n\r\n    // 返回登陆验证结果\r\n    private loginVerifyRet(err: string, accountToken: string): any {\r\n        if (err) {\r\n            return { state: LoginState.FAILURE, err: err }\r\n        }\r\n        storageMgr.saveString('account_token', accountToken || '0')\r\n        //\r\n        return { state: LoginState.SUCCEED, accountToken }\r\n    }\r\n}"]}