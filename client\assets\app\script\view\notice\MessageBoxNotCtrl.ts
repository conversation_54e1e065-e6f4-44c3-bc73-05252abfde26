import { _decorator, log, Node, Label, RichText, EventTouch, Widget, v3, easing, tween, Button } from "cc";
import NotEvent from "../../common/event/NotEvent";
import { MessageBoxOpts } from "../../common/constant/DataType";
const { ccclass } = _decorator;

@ccclass
export default class MessageBoxNotCtrl extends mc.BaseNoticeCtrl {

	//@autocode property begin
	private closeNode_: Node = null // path://close_be_n
	private rootNode_: Node = null // path://root_n
	private titleLbl_: Label = null // path://root_n/title/title_l
	private contentRt_: RichText = null // path://root_n/content_rt
	private buttonsNode_: Node = null // path://root_n/buttons_nbe_n
	//@end

	private okCb: Function = null
	private cancelCb: Function = null

	private clickButtonClose: boolean = true //点击按钮是否关闭界面

	public listenEventMaps() {
		return [
			{ [NotEvent.OPEN_MESSAGE_BOX]: this.onEventOpen },
			{ [NotEvent.HIDE_MESSAGE_BOX]: this.onEventHide },
		]
	}

	public async onCreate() {
	}

	// ----------------------------------------- button listener function -------------------------------------------
	//@autocode button listener

	// path://close_be_n
	onClickClose(event: EventTouch, data: string) {
		this.hide()
		this.onHide()
	}

	// path://root_n/buttons_nbe_n
	onClickButtons(event: EventTouch, data: string) {
		if (this.clickButtonClose) {
			this.hide()
		}
		const name = event.target.name
		if (name === 'ok') {
			this.okCb && this.okCb()
		} else if (name === 'cancel') {
			this.cancelCb && this.cancelCb()
		}
		this.onHide()
	}
	//@end
	// ----------------------------------------- event listener function --------------------------------------------

	private onEventOpen(msg: string, opts?: MessageBoxOpts) {
		this.open()
		this.show(msg, opts)
	}

	private onEventHide() {
		this.hide()
		this.onHide()
	}
	// ----------------------------------------- custom function ----------------------------------------------------

	@ut.syncLock
	private async show(msg: string, opts?: MessageBoxOpts) {
		mc.lockTouch('__open_message_box__')
		opts = opts || {}
		this.titleLbl_.setLocaleKey(opts.title || 'login.title_tip')
		this.contentRt_.setLocaleKey(msg, ...(opts.params || []))
		this.okCb = opts.ok
		this.cancelCb = opts.cancel
		// 是否显示取消按钮
		this.buttonsNode_.Child('cancel').active = !!this.cancelCb
		// 设置按钮名字
		this.buttonsNode_.Child('ok/val', Label).setLocaleKey(opts.okText || 'login.button_ok')
		this.buttonsNode_.Child('cancel/val', Label).setLocaleKey(opts.cancelText || 'login.button_cancel')
		// 做动画
		this.rootNode_.stopAllActions()
		this.rootNode_.Height = Math.max(360, this.contentRt_.node.Height + 200)
		this.rootNode_.children.forEach(m => m.Component(Widget)?.updateAlignment())
		// 是否显示mask 默认显示
		const isMask = this.closeNode_.active = opts?.mask === false ? false : true
		this.playShowAction(isMask, !opts?.lockClose)
		// 是否开启点击按钮就关闭 默认开启
		this.clickButtonClose = opts?.clickButtonClose ?? true
		await ut.wait(0.25)
		mc.unlockTouch('__open_message_box__')
	}

	private playShowAction(isMask: boolean, lockClose: boolean) {
		const widget = this.Component(Widget)
		if (widget && widget.enabled) {
			widget.updateAlignment()
			widget.enabled = false
		}
		this.rootNode_.stopAllActions()
		this.rootNode_.scale = v3(0.4)
		tween(this.rootNode_).to(0.25, { scale: v3(1) }, { easing: easing.backOut }).start()
		if (isMask) {
			// 禁止点击空白关闭
			this.closeNode_.Component(Button).interactable = lockClose
			// 做动画
			this.closeNode_.stopAllActions()
			this.closeNode_.opacity = 0
			tween(this.closeNode_).to(0.3, { opacity: 120 }, { easing: easing.sineOut }).start()
		}
	}

	private onHide() {
		this.okCb = null
		this.cancelCb = null
	}
}
