System.register(["__unresolved_0", "cc"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, log, Label, v2, _class, _crd, ccclass, MapPnlCtrl;

  function _reportPossibleCrUseOfGameModel(extras) {
    _reporterNs.report("GameModel", "../../model/game/GameModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMapNodeObj(extras) {
    _reporterNs.report("MapNodeObj", "../../model/game/MapNodeObj", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      log = _cc.log;
      Label = _cc.Label;
      v2 = _cc.v2;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "14b2ev1jM5Nj6hPC80Gjvpr", "MapPnlCtrl", undefined);

      __checkObsolete__(['_decorator', 'log', 'ScrollView', 'Label', 'Node', 'EventTouch', 'v2']);

      ({
        ccclass
      } = _decorator);

      _export("default", MapPnlCtrl = ccclass(_class = class MapPnlCtrl extends mc.BasePnlCtrl {
        constructor(...args) {
          super(...args);
          //@autocode property begin
          this.layersSv_ = null;
          // path://layers_sv
          //@end
          this.game = null;
        }

        listenEventMaps() {
          return [];
        }

        async onCreate() {
          this.setParam({
            isMask: false,
            isAct: false
          });
          this.game = this.getModel('game');
        }

        onEnter() {
          const maps = this.game.getMaps();
          let lastLayerNode = null,
              tempVec = v2();
          this.layersSv_.Items(maps, (it, layer) => {
            it.Data = layer;
            it.Items(layer, (node, data) => {
              node.Child('val', Label).string = data.type + '';
            }); // 绘制上一个的线

            if (lastLayerNode) {
              const lastLayerNodes = lastLayerNode.Data;
              lastLayerNodes.forEach((m, i) => {
                const preNode = lastLayerNode.children[i],
                      prePos = preNode.position.toVec2();
                const lineNode = preNode.Child('lines');
                lineNode.Items(m.children, (line, id) => {
                  const curNode = it.children[id],
                        curPos = ut.convertToNodeAR(curNode, lineNode, tempVec);
                  line.Height = curPos.subtract(prePos).length();
                  line.angle = ut.getAngle(curPos, prePos);
                });
              });
            }

            lastLayerNode = it;
          });
        }

        onRemove() {}

        onClean() {} // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        // path://layers_sv/view/content/layer/item_be


        onClickItem(event, data) {
          log('onClickItem', data);
        } //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------


      }) || _class);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0ad75528e92078f2e5ba86ddc66657a51581b2b5.js.map