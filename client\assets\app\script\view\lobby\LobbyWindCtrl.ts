import { _decorator, EventTouch, Label, log, Node } from "cc";
import { gHelper } from "../../common/helper/GameHelper";
import UserModel from "../../model/common/UserModel";
import { viewHelper } from "../../common/helper/ViewHelper";
const { ccclass } = _decorator;

@ccclass
export default class LobbyWindCtrl extends mc.BaseWindCtrl {

    //@autocode property begin
    private enterGameNode_: Node = null // path://enter_game_be_n
    //@end

    private user: UserModel = null

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        this.user = this.getModel('user')
    }

    public onEnter(data: any) {
        this.enterGameNode_.Child('val', Label).string = gHelper.game.getDay() ? '继续冒险' : '开始新的冒险'
    }

    public onClean() {
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://enter_game_be_n
    onClickEnterGame(event: EventTouch, data: string) {
        this.enterGame()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private async enterGame() {
        const { err, data } = await gHelper.net.request('game/HD_Entry', { nickname: this.user.getNickname(), roleId: this.user.getRoleId() })
        if (err) {
            return viewHelper.showAlert(err)
        }
        gHelper.game.init(data)
        viewHelper.gotoWind('game')
    }
}
