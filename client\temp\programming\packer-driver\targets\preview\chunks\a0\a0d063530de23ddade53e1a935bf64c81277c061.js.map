{"version": 3, "sources": ["cce:/internal/ml/cjs-loader.mjs"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "_registry", "_moduleCache", "define", "id", "factory", "resolveMap", "require", "_require", "throwInvalidWrapper", "requestTarget", "from", "Error", "parent", "cachedModule", "exports", "module", "_tryModuleLoad", "_resolve", "specifier", "_resolveFromInfos", "_throwUnresolved", "cjsInfos", "resolveCache", "undefined", "threw", "_load", "_loadWrapper", "vendorRequire", "_createRequire", "_createRequireWithResolveMap", "_loadHostProvidedModules", "_exports", "err", "cause", "requireMap", "originalRequire", "resolved", "parentUrl"], "mappings": ";;;MAAMA,S;;;;AAAAA,MAAAA,S,GAAN,MAAMA,SAAN,CAAgB;AACZC,QAAAA,WAAW,GAAG;AACV,eAAKC,SAAL,GAAiB,EAAjB;AACA,eAAKC,YAAL,GAAoB,EAApB;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,EAAD,EAAKC,OAAL,EAAcC,UAAd,EAA0B;AAC5B,eAAKL,SAAL,CAAeG,EAAf,IAAqB;AACjBC,YAAAA,OADiB;AAEjBC,YAAAA;AAFiB,WAArB;AAIH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,OAAO,CAACH,EAAD,EAAK;AACR,iBAAO,KAAKI,QAAL,CAAcJ,EAAd,CAAP;AACH;;AAEDK,QAAAA,mBAAmB,CAACC,aAAD,EAAgBC,IAAhB,EAAsB;AACrC,gBAAM,IAAIC,KAAJ,cAAqBF,aAArB,yBAAsDC,IAAtD,qEAAN;AACH;;AAEDH,QAAAA,QAAQ,CAACJ,EAAD,EAAKS,MAAL,EAAa;AACjB,cAAMC,YAAY,GAAG,KAAKZ,YAAL,CAAkBE,EAAlB,CAArB;;AACA,cAAIU,YAAJ,EAAkB;AACd,mBAAOA,YAAY,CAACC,OAApB;AACH;;AAED,cAAMC,MAAM,GAAG;AAAEZ,YAAAA,EAAF;AAAMW,YAAAA,OAAO,EAAE;AAAf,WAAf;AACA,eAAKb,YAAL,CAAkBE,EAAlB,IAAwBY,MAAxB;;AACA,eAAKC,cAAL,CAAoBD,MAApB,EAA4BZ,EAA5B;;AACA,iBAAOY,MAAM,CAACD,OAAd;AACH;;AAEDG,QAAAA,QAAQ,CAACC,SAAD,EAAYN,MAAZ,EAAoB;AACxB,iBAAO,KAAKO,iBAAL,CAAuBD,SAAvB,EAAkCN,MAAlC,KAA6C,KAAKQ,gBAAL,CAAsBF,SAAtB,EAAiCN,MAAjC,CAApD;AACH;;AAEDO,QAAAA,iBAAiB,CAACD,SAAD,EAAYN,MAAZ,EAAoB;AAAA;;AACjC,cAAIM,SAAS,IAAIG,QAAjB,EAA2B;AACvB,mBAAOH,SAAP;AACH;;AACD,cAAI,CAACN,MAAL,EAAa;AACT;AACH;;AACD,8DAAOS,QAAQ,CAACT,MAAD,CAAf,qBAAO,iBAAkBU,YAAlB,CAA+BJ,SAA/B,CAAP,oCAAoDK,SAApD;AACH;;AAEDP,QAAAA,cAAc,CAACD,MAAD,EAASZ,EAAT,EAAa;AACvB,cAAIqB,KAAK,GAAG,IAAZ;;AACA,cAAI;AACA,iBAAKC,KAAL,CAAWV,MAAX,EAAmBZ,EAAnB;;AACAqB,YAAAA,KAAK,GAAG,KAAR;AACH,WAHD,SAGU;AACN,gBAAIA,KAAJ,EAAW;AACP,qBAAO,KAAKvB,YAAL,CAAkBE,EAAlB,CAAP;AACH;AACJ;AACJ;;AAEDsB,QAAAA,KAAK,CAACV,MAAD,EAASZ,EAAT,EAAa;AACd,cAAM;AAAEC,YAAAA,OAAF;AAAWC,YAAAA;AAAX,cAA0B,KAAKqB,YAAL,CAAkBvB,EAAlB,CAAhC;;AACA,cAAMwB,aAAa,GAAG,KAAKC,cAAL,CAAoBb,MAApB,CAAtB;;AACA,cAAMT,OAAO,GAAGD,UAAU,GACpB,KAAKwB,4BAAL,CAAkC,OAAOxB,UAAP,KAAsB,UAAtB,GAAmCA,UAAU,EAA7C,GAAkDA,UAApF,EAAgGsB,aAAhG,CADoB,GAEpBA,aAFN;;AAGAvB,UAAAA,OAAO,CAACW,MAAM,CAACD,OAAR,EAAiBR,OAAjB,EAA0BS,MAA1B,CAAP;AACH;;AAEDW,QAAAA,YAAY,CAACvB,EAAD,EAAK;AACb,cAAIA,EAAE,IAAI,KAAKH,SAAf,EAA0B;AACtB,mBAAO,KAAKA,SAAL,CAAeG,EAAf,CAAP;AACH,WAFD,MAEO;AACH,mBAAO,KAAK2B,wBAAL,CAA8B3B,EAA9B,CAAP;AACH;AACJ;;AAED2B,QAAAA,wBAAwB,CAAC3B,EAAD,EAAK;AACzB,iBAAO;AACHC,YAAAA,OAAO,EAAE,CAAC2B,QAAD,EAAWxB,QAAX,EAAqBQ,MAArB,KAAgC;AACrC,kBAAI,OAAOT,OAAP,KAAmB,WAAvB,EAAoC;AAChC,sBAAM,IAAIK,KAAJ,sEAA6ER,EAA7E,QAAN;AACH;;AACD,kBAAI;AACAY,gBAAAA,MAAM,CAACD,OAAP,GAAiBR,OAAO,CAACH,EAAD,CAAxB;AACH,eAFD,CAEE,OAAO6B,GAAP,EAAY;AACV,sBAAM,IAAIrB,KAAJ,0DAAiER,EAAjE,UAA0E;AAAE8B,kBAAAA,KAAK,EAAED;AAAT,iBAA1E,CAAN;AACH;AACJ;AAVE,WAAP;AAYH;;AAEDJ,QAAAA,cAAc,CAACb,MAAD,EAAS;AACnB,iBAAQG,SAAD,IAAe,KAAKX,QAAL,CAAcW,SAAd,EAAyBH,MAAzB,CAAtB;AACH;;AAEDc,QAAAA,4BAA4B,CAACK,UAAD,EAAaC,eAAb,EAA8B;AACtD,iBAAQjB,SAAD,IAAe;AAClB,gBAAMkB,QAAQ,GAAGF,UAAU,CAAChB,SAAD,CAA3B;;AACA,gBAAIkB,QAAJ,EAAc;AACV,qBAAOD,eAAe,CAACC,QAAD,CAAtB;AACH,aAFD,MAEO;AACH,oBAAM,IAAIzB,KAAJ,CAAU,0BAA0BO,SAApC,CAAN;AACH;AACJ,WAPD;AAQH;;AAEDE,QAAAA,gBAAgB,CAACF,SAAD,EAAYmB,SAAZ,EAAuB;AACnC,gBAAM,IAAI1B,KAAJ,wBAA+BO,SAA/B,cAAiDN,MAAjD,OAAN;AACH;;AAxHW,O;;yBA2HD,IAAId,SAAJ,E", "sourcesContent": ["class CjsLoader {\n    constructor() {\n        this._registry = {};\n        this._moduleCache = {};\n    }\n\n    /**\n     * Defines a CommonJS module.\n     * @param id Module ID.\n     * @param factory The factory.\n     * @param resolveMap An object or a function returning object which records the module specifier resolve result.\n     * The later is called as \"deferred resolve map\" and would be invocated right before CommonJS code execution.\n     */\n    define(id, factory, resolveMap) {\n        this._registry[id] = {\n            factory,\n            resolveMap,\n        };\n    }\n\n    /**\n     * Requires a CommonJS module.\n     * @param id Module ID.\n     * @returns The module's `module.exports`.\n     */\n    require(id) {\n        return this._require(id);\n    }\n\n    throwInvalidWrapper(requestTarget, from) {\n        throw new Error(`Module '${requestTarget}' imported from '${from}' is expected be an ESM-wrapped CommonJS module but it doesn't.`);\n    }\n\n    _require(id, parent) {\n        const cachedModule = this._moduleCache[id];\n        if (cachedModule) {\n            return cachedModule.exports;\n        }\n\n        const module = { id, exports: {} };\n        this._moduleCache[id] = module;\n        this._tryModuleLoad(module, id);\n        return module.exports;\n    }\n\n    _resolve(specifier, parent) {\n        return this._resolveFromInfos(specifier, parent) || this._throwUnresolved(specifier, parent);\n    }\n\n    _resolveFromInfos(specifier, parent) {\n        if (specifier in cjsInfos) {\n            return specifier;\n        }\n        if (!parent) {\n            return;\n        }\n        return cjsInfos[parent]?.resolveCache[specifier] ?? undefined;\n    }\n\n    _tryModuleLoad(module, id) {\n        let threw = true;\n        try {\n            this._load(module, id);\n            threw = false;\n        } finally {\n            if (threw) {\n                delete this._moduleCache[id];\n            }\n        }\n    }\n\n    _load(module, id) {\n        const { factory, resolveMap } = this._loadWrapper(id);\n        const vendorRequire = this._createRequire(module);\n        const require = resolveMap\n            ? this._createRequireWithResolveMap(typeof resolveMap === 'function' ? resolveMap() : resolveMap, vendorRequire)\n            : vendorRequire;\n        factory(module.exports, require, module);\n    }\n\n    _loadWrapper(id) {\n        if (id in this._registry) {\n            return this._registry[id];\n        } else {\n            return this._loadHostProvidedModules(id);\n        }\n    }\n\n    _loadHostProvidedModules(id) {\n        return {\n            factory: (_exports, _require, module) => {\n                if (typeof require === 'undefined') {\n                    throw new Error(`Current environment does not provide a require() for requiring '${id}'.`);\n                }\n                try {\n                    module.exports = require(id);\n                } catch (err) {\n                    throw new Error(`Exception thrown when calling host defined require('${id}').`, { cause: err });\n                }\n            },\n        };\n    }\n\n    _createRequire(module) {\n        return (specifier) => this._require(specifier, module);\n    }\n\n    _createRequireWithResolveMap(requireMap, originalRequire) {\n        return (specifier) => {\n            const resolved = requireMap[specifier];\n            if (resolved) {\n                return originalRequire(resolved);\n            } else {\n                throw new Error('Unresolved specifier ' + specifier);\n            }\n        };\n    }\n\n    _throwUnresolved(specifier, parentUrl) {\n        throw new Error(`Unable to resolve ${specifier} from ${parent}.`);\n    }\n}\n\nexport default new CjsLoader();\n"]}